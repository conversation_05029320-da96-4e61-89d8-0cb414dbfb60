window.addEventListener('DOMContentLoaded', () => {
	const replaceText = (selector, text) => {
		const element = document.getElementById(selector)
		if (element) element.innerText = text
	}
	for (const dependency of ['chrome', 'node', 'electron']) {
		replaceText(`${dependency}-version`, process.versions[dependency])
	}
})
const { contextBridge, ipcRenderer } = require('electron')
let baseURL, websocket_url
ipcRenderer.on('port', (event, port, version, webConfig) => {
	baseURL = 'http://127.0.0.1:' + port
	websocket_url = '127.0.0.1:' + port
	contextBridge.exposeInMainWorld('config', {
		baseURL,
		websocket_url,
		versions: () => process.versions,
		version,
		webConfig
	})
})
contextBridge.exposeInMainWorld('electronApi', {
	sendToMain: (channel, data) => {
		ipcRenderer.send(channel, data)
	},
	receiveFromMain: (channel, callback) => {
		ipcRenderer.on(channel, (event, ...args) => callback(...args))
	},
	waitToMain: (channel, data) => {
		return ipcRenderer.invoke(channel, data)
	}
})
