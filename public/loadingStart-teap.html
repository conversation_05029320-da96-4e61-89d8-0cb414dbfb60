<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
</head>

<body>
    <div>
        <div class="app_start">
            <div class="description">
                <p id="version"></p>
                <p id="arh"></p>
                <p id="date"></p>
                <!-- <p>合作单位：
                    <span style="text-align-last: justify;">华中科技大学</span>
                </p>
                <p><span>南京图德科技有限公司</span></p> -->
            </div>
            <div id="log">
                <div id="log_lit">

                </div>
            </div>
        </div>
    </div>
</body>
<script>
    if (navigator.userAgent.includes('Electron')) {
        window.electronApi.receiveFromMain('version', (version, arh, date) => {
            if (arh == 'x64') {
                document.getElementById('arh').innerHTML = '64-bit(win64)'
            } else {
                document.getElementById('arh').innerHTML = '32-bit(win32)'
            }
            document.getElementById('date').innerHTML = date
            document.getElementById('version').innerHTML = `Version ${version}`
        })
        window.electronApi.receiveFromMain('files', (files) => {
            files.filter(items => items.includes('.')).forEach((item, index, arr) => {
                setTimeout(() => {
                    var paragraph = document.createElement('p');
                    paragraph.textContent = `正在加载 ${item}`
                    document.getElementById('log_lit').appendChild(paragraph)
                    document.getElementById('log').scrollTo(0, document.getElementById('log').scrollHeight)
                    if (index + 1 == arr.length) {
                        setTimeout(() => {
                            var paragraph = document.createElement('p');
                            paragraph.textContent = `正在加载算法模块...`
                            document.getElementById('log_lit').appendChild(paragraph)
                            document.getElementById('log').scrollTo(0, document.getElementById('log').scrollHeight)
                        }, 50)
                    }
                }, index * 50)
            });
        })
    }
</script>.
<style>
    ::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }

    body>div {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .app_start {
        width: 965px;
        height: 903px;
        background: url(./loadingImage-teap.png);
        background-size: 100%;
        background-position: center;
        position: relative;
        background-repeat: no-repeat;
    }

    .description {
        position: absolute;
        right: 20px;
        top: 20px;
    }

    .description p {
        text-align: right;
        height: 24px;
        line-height: 24px;
        font-size: 18px;
        color: #9AC0F9;

        span {
            display: inline-block;
            width: 180px;
        }
    }

    #log {
        position: absolute;
        bottom: 30px;
        left: 30px;
        height: 120px;
        width: 550px;
        overflow: scroll;
        color: #9AC0F9;
        line-height: 24px;
    }

    body,
    html {
        height: 100%;
        -moz-user-select: none;
        /*火狐*/
        -webkit-user-select: none;
        /*webkit浏览器*/
        -ms-user-select: none;
        /*IE10*/
        user-select: none;
        /*选中文字时避免出现蓝色背景*/
    }

    * {
        margin: 0;
        box-sizing: border-box;
    }
</style>
<script>

</script>

</html>