import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
// ant-design-vue
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'

import eslintPlugin from 'vite-plugin-eslint'

// https://vitejs.dev/config/
export default defineConfig({
	base: './',
	build: {
		// outDir: 'build',
		assetsDir: 'prsas_static'
	},
	plugins: [
		vue(),
		Components({
			dirs: ['src/components'],
			// ant-design-vue   importStyle = false
			resolvers: [AntDesignVueResolver({ importStyle: false, resolveIcons: true })]
		}),
		eslintPlugin({
			include: ['src/**/*.ts', 'src/**/*.vue', 'src/*.ts', 'src/*.vue']
		})
	],

	resolve: {
		alias: {
			'@': resolve(__dirname, './src')
		}
	},

	css: {
		preprocessorOptions: {
			scss: {
				additionalData:
          '@import "./src/styles/variable.scss";@import "./src/styles/antd.scss";@import "./src/styles/mixin.scss";'
			}
		}
	},
	server: {
		host: '0.0.0.0',
		port: 3000,
		// open: true,
		proxy: {
			'/teap3': {
				target: 'https://feat_eng_version.teap.tode.ltd/',
				changeOrigin: true,
				rewrite: (path) => path.replace(/^\/teap3/, '')
			}
		}
	}

})
