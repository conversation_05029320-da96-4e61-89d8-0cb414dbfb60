import { createWebHashHistory, createRouter } from 'vue-router'
import { routeStore } from '@/store/routeStore'
import { storeToRefs } from 'pinia'
/* Router Modules */
// require是webpack语法，vite构建的项目里使用会报错
// const moduleFiles = require.context('./modules', true, /.js$/)
// const modules = moduleFiles.keys().map((path) => moduleFiles(path).default)

const routes = [
	{
		path: '/', // 路由的路径
		name: 'layout', // 路由的名称
		// redirect: '/layout',
		component: () => import(/* webpackChunkName: "home" */ '../views/layout/index.vue'),
		children: [
			// {
			// 	path: '/start',
			// 	name: 'start',
			// 	component: () => import(/* webpackChunkName: "home" */ '../views/start/index.vue')
			// },
			// {
			// 	path: '/example',
			// 	name: 'example',
			// 	component: () => import(/* webpackChunkName: "home" */ '../views/example/index.vue')
			// },
			{
				path: '/detail',
				name: 'detail',
				component: () => import(/* webpackChunkName: "home" */ '../views/layout/detail.vue')
			},
			{
				path: '/result',
				name: 'result',
				component: () => import(/* webpackChunkName: "home" */ '../views/layout/result.vue')
			},
			{
				path: '/resultGis',
				name: 'resultGis',
				component: () => import(/* webpackChunkName: "home" */ '../views/layout/resultGis.vue')
			},
			{
				path: '/resultTeap',
				name: 'resultTeap',
				component: () => import(/* webpackChunkName: "home" */ '../views/layout/resultTeap.vue')
			},
			{
				path: '/params',
				name: 'params',
				component: () => import(/* webpackChunkName: "home" */ '../views/layout/params.vue')
			},
			{
				path: '/gis',
				name: 'gis',
				component: () => import(/* webpackChunkName: "home" */ '../views/layout/gis.vue')
			}

			// {
			// 	path: '/count',
			// 	name: 'count',
			// 	component: () => import(/* webpackChunkName: "home" */ '../views/errorPage/404.vue')
			// },
			// {
			// 	path: '/dataManage',
			// 	name: 'dataManage',
			// 	component: () => import(/* webpackChunkName: "home" */ '../views/errorPage/404.vue')
			// },
			// {
			// 	path: '/system',
			// 	name: 'system',
			// 	component: () => import(/* webpackChunkName: "home" */ '../views/errorPage/404.vue')
			// },
			// {
			// 	path: '/help',
			// 	name: 'help',
			// 	component: () => import(/* webpackChunkName: "home" */ '../views/errorPage/404.vue')
			// }
		]
	},
	{
		path: '/errorPage',
		name: 'errorPage',
		component: () => import(/* webpackChunkName: "home" */ '../views/errorPage/404.vue')
	}
	// ...modules,
]

// 创建路由实例并传递 `routes` 配置
const router = createRouter({
	history: createWebHashHistory(), // 内部提供了 history 模式的实现，这里使用 hash 模式
	routes // `routes: routes` 的缩写
})
router.beforeEach((to, from, next) => {
	const store = routeStore()
	const { routeCache, routeTabs } = storeToRefs(store)
	if (to.path == '/') {
		next()
		return
	}
	if (routeCache.value.length != 0 && routeCache.value.find(item => item == to.name)) {
		if (routeTabs.value.find(item => item.key == to.fullPath)) {
			store.setActive(to.fullPath)
			next()
		} else {
			store.addTabs(Object.assign({}, to.query, {
				key: to.fullPath,
				name: to.name,
				title: to.query.name,
				isUnsaved: to.query.type == 'isNewBuilt',
				isModalVisible: false,
				treeNode: 'bus'
			}))
			next()
		}
	} else {
		store.addTabs(Object.assign({}, to.query, {
			key: to.fullPath,
			name: to.name,
			title: to.query.name,
			isUnsaved: false,
			isModalVisible: false,
			treeNode: 'bus'
		}))
		next()
	}
})
export default router
