import { createApp } from 'vue'
import '@/styles/common.scss'
import App from './App.vue'
import i18n from './i18n'

import router from '@/router/index'
// import { createPinia } from 'pinia'
import AgGrid from '@/components/AG-Grid/index.vue'
// import ContextMenu from '@gahing/vcontextmenu'
// import '@gahing/vcontextmenu/lib/vcontextmenu.css'
import scrollLoadmore from './directive/el-scroll-loadmore/loadmore'
import pinia from '@/store/store'
// 导入RecycleScroller
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
// app.use(pinia)

// 引入字体文件
import './SiYuanHeiTi/font.css'

const app = createApp(App)

// 自定义全局指令
app.directive('scrollLoadmore', scrollLoadmore)

// 注册全局组件
app.component('AgGrid', AgGrid)
app.component('RecycleScroller', RecycleScroller)

// const pinia = createPinia()
app.use(router).use(i18n).use(pinia).mount('#app')
