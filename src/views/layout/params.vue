<template>
  <div class="detail-main">
    <div class="main_operate">
      <ParamsTree ref="treeRef" @selectTree="selectTree" @paramsTreeData="getParamsTreeData" @defaultLabel="getDefaultLabel"></ParamsTree>
    </div>
    <div class="main_conent">
      <div class="breadcrumb" v-show="!state.globalVisible">
        <!-- <a-tooltip>
          <template #title>{{ route.query.name.substring(4) }} / {{ treeStructure }}</template> -->
           {{ $t('参数管理') }}/ {{ state.treeStructure }}
        <!-- </a-tooltip> -->
      </div>
      <div class="table_box">
        <AgGrid ref="agGridRef" :isEdit="'paramsManage'"></AgGrid>
      </div>

    </div>

  </div>

  <!-- 恢复默认 -->
  <a-modal
    wrapClassName="modal_confirm"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="375px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p> {{ $t('提示') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content relative">
        <p> {{ $t('是针对当前表格、还是全部表格') }}？</p>
        <div class="modal_btns">
          <a-button @click="handleCurrentTable" type="primary" :style="{color:'#fff'}" size="small">{{ $t('当前表格') }}</a-button>
          <a-button @click="handleAllTable" type="primary" ghost size="small"  :style="{margin:'0 17px'}"> {{ $t('全部表格') }}</a-button>
          <a-button @click="closeModal" size="small">{{ $t('取消') }}</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script>
export default {
	name: 'detail'
}
</script>
<script setup>
import Mitt from '@/utils/mitt.js'
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import message from '@/utils/message'
import { basicApi } from '@/api/exampleApi'
import { t } from '@/utils/common'
// import { timeseriesColumns, splitGroup, hoursTitle, getCurveType, fileBlobFun } from '@/utils/common.js'
// import { fileBlobFun } from '@/utils/common.js'
import { useRoute } from 'vue-router'
// import { loadingStore } from '@/store/loadingStore'
import { routeStore } from '@/store/routeStore'
// import ActionBar from './components/actionBar.vue'
import ParamsTree from './components/ParamsTree.vue'

const route = useRoute()
// const storeLoading = loadingStore()
const storeRoute = routeStore()

// AG-Grid表格
const agGridRef = ref(null)
const treeRef = ref()

const state = reactive({
	routePath: route.fullPath,
	isDetail: false,
	globalVisible: false, // 全局参数设置
	treeStructure: undefined,
	visible: false,
	fieldSetShow: false, // 字段设置
	ShowResultModal: false,
	istimeseriesAdd: false, // 判断时序是否为新增
	actionsMove: '',
	areasOptions: [],
	dataTypeOptions: [],
	insertCountVisible: false, // 插入行弹框
	statisticShow: false,
	balanceGoalsVisible: false, // 平衡目标弹框
	timeShow: false,
	treeNode: '',
	treeNode_id: null,
	paramsTreeData: []
})

// 顶部操作栏
const handleActionBar = (val) => {
	if (state.routePath !== route.fullPath) return
	if (val == 'save') {
		nextTick(() => {
			agGridRef.value.saveParamsAgtable(treeType.value)
		})
	} if (val == 'reset') { // 重置
		nextTick(() => {
			agGridRef.value.getParamsAgTable(treeType.value)
		})
	} else if (val == 'deleteRow') { // 删除行
		nextTick(() => {
			agGridRef.value.onRemoveSelected()
		})
		// agGridRef.value.removeRow()
	} else if (val == 'insertRow') { // 插入行
		nextTick(() => {
			agGridRef.value.insertRow(1)
		})
	} else if (val == 'revoke') { // 撤销
		nextTick(() => {
			agGridRef.value.undo()
		})
	} else if (val == 'forward') { // 恢复
		nextTick(() => {
			agGridRef.value.redo()
		})
	} else if (val == 'clearFilter') { // 清空筛选条件
		nextTick(() => {
			agGridRef.value.clearFilter()
		})
	} else if (val == 'dataReplace') { // 替换
		nextTick(() => {
			agGridRef.value.openReplace()
		})
	} else if (val == 'dataHandle') { // 计算
		nextTick(() => {
			agGridRef.value.onCount()
		})
	} else if (val == 'copyData') {
		nextTick(() => {
			const agselectArr = agGridRef.value.getSelected()
			handleCopyData(agselectArr)
		})
	} else if (val == 'pasteData') {
		handlePasteData()
	} else if (val == 'cutData') {
		const agselectArr = agGridRef.value.getSelected()
		handleCuteData(agselectArr)
	} else if (val == 'restoreDefault') { // 恢复默认
		state.visible = true
		// Modal.confirm({
		// 	title: '注意',
		// 	content: `是针对当前表格、还是全部表格？`,
		// 	okText: '当前表格',
		// 	cancelText: '全部表格',
		// 	onOk() {
		// 		nextTick(() => {
		// 			agGridRef.value.resetParamsAgTable(treeType.value, state.paramsTreeData, true)
		// 		})
		// 	},
		// 	onCancel() {
		// 		nextTick(() => {
		// 			agGridRef.value.resetParamsAgTable(treeType.value, state.paramsTreeData, false)
		// 		})
		// 	}
		// })
	}
}
Mitt.on('handleActionBar', handleActionBar)

// 参数管理 树组件切换节点
const paramsTreeChange = (val) => {
	if (state.routePath !== route.fullPath) return
	nextTick(() => {
		agGridRef.value.saveParamsAgtable(val.treeNode_old, val.treeNode_new)
	})
}
Mitt.on('paramsTreeChange', paramsTreeChange)

const closeModal = () => {
	state.visible = false
}

const handleCurrentTable = () => {
	state.visible = false
	nextTick(() => {
		agGridRef.value.resetParamsAgTable(treeType.value, state.paramsTreeData, true)
	})
}

const handleAllTable = () => {
	state.visible = false
	nextTick(() => {
		agGridRef.value.resetParamsAgTable(treeType.value, state.paramsTreeData, false)
	})
}

// 复制
const handleCopyData = (data) => {
	if (state.itemizedViewShow || state.globalVisible || state.fieldSetShow) return // 逐项视图、全局参数设置、字段设置 下不允许复制
	if (data.length <= 0) {
		agGridRef.value.onCopyClick()
		sessionStorage.removeItem('fullPath')
	} else {
		const idList = data.map(item => item.index)
		sessionStorage.setItem('fullPath', route.fullPath)
		sessionStorage.setItem('filePath', route.query.filePath)
		sessionStorage.setItem('copyTreeNode', treeType.value)
		sessionStorage.setItem('ids', JSON.stringify(idList))
		message.success(t('复制成功') + '！')
	}
}

// 粘贴
const handlePasteData = (data) => {
	if (state.itemizedViewShow || state.globalVisible || state.fieldSetShow) return // 逐项视图、全局参数设置、字段设置 下不允许粘贴
	const fullPath = sessionStorage.getItem('fullPath')
	const filePath = sessionStorage.getItem('filePath')
	const copyTreeNode = sessionStorage.getItem('copyTreeNode')
	const idList = JSON.parse(sessionStorage.getItem('ids'))
	if (!fullPath) return agGridRef.value.onPasteClick()
	// if (copyTreeNode !== treeType.value) return message.warning('不同类型设备无法粘贴！')
	if (route.fullPath == fullPath) {
		basicApi({
			'import_string_func': 'teapcase:duplicates_rows_in_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': copyTreeNode,
				'target_sheet_name': treeType.value,
				'selected_row_id_list': idList
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				agGridRef.value.getParamsAgTable(treeType.value)
				Mitt.emit('getTreeMenuList')
				message.success(t('已粘贴至表末端') + '！')
			}
		})
	} else {
		// if (treeType.value !== copyTreeNode) return message.warning('仅支持相同设备跨表粘贴！')
		basicApi({
			'import_string_func': 'teapcase:copy_rows_from_tc_to_tc',
			'func_arg_dict': {
				'src_file_name': filePath,
				'dest_file_name': route.query.filePath,
				'sheet_name': copyTreeNode,
				'target_sheet_name': treeType.value,
				'selected_row_id_list': idList
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				agGridRef.value.getParamsAgTable(treeType.value)
				Mitt.emit('getTreeMenuList')
				message.success(t('已粘贴至表末端') + '！')
			}
		})
	}
}

// 前切
const handleCuteData = (data) => {
	if (state.itemizedViewShow || state.globalVisible || state.fieldSetShow) return // 逐项视图、全局参数设置、字段设置 下不允许复制
	if (data.length <= 0) {
		agGridRef.value.onCutClick()
		sessionStorage.removeItem('fullPath')
	} else {
		const idList = data.map(item => item.index)
		sessionStorage.setItem('fullPath', route.fullPath)
		sessionStorage.setItem('filePath', route.query.filePath)
		sessionStorage.setItem('ids', JSON.stringify(idList))
		agGridRef.value.onRemoveSelected()
		message.success(t('剪切成功') + '！')
		// agGridRef.value.saveAgtable(treeType.value)
	}
}

// 左侧树选择
/**
  1.母线（ bus ）
  2.发电机（ gen ）
  3.负荷（ load ）
  4.风电（ wind )
  5.光伏（ solar ）
  6.区外来电（ feedin ）
  7.抽蓄机组或储能电池换流器（ stogen ）
  8.抽蓄电站水库或储能电池（ storage ）
  9.水电机组（ hydropower ）
  10.水电站水库（ reservoir ）
  11.线路（ line ）
  12.主变（ trafo ）
  13.断面（ interface ）
  14.时序（ timeseries ）
 */
const treeType = ref('gen')
const selectTree = (val, treeName) => {
	treeType.value = val
	state.treeStructure = treeName
	agGridRef.value.getParamsAgTable(val)
}

const getParamsTreeData = (data) => {
	state.paramsTreeData = data
}
const getDefaultLabel = (val) => {
	state.treeStructure = val
}

// 监听多个响应式数据的变化 state.globalVisible || state.itemizedViewShow || state.fieldSetShow
watch(() => [state.globalVisible, state.itemizedViewShow, state.fieldSetShow], (newValues, oldValues) => {
	newValues.includes(true) ? storeRoute.setTabsModalVisible(route.fullPath, true) : storeRoute.setTabsModalVisible(route.fullPath, false)
})

defineExpose({ handleActionBar })

onMounted(() => {
	agGridRef.value.getParamsAgTable(treeType.value)
})

</script>

<style lang="scss" scoped>
  .detail-main {
    width: 100%;
    height: 100%;
    padding: 0 15px 2px 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    background-color: var(--theme-bg-color);
    position: relative;

    .fieldSetClass {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index:  100;
    }
    .main-actions {
      width: 100%;
      height: 50px;
      display: flex;
      font-weight: 500;
      color: #6d77ad;
      border-bottom: 1px solid #d4d6d1;
      div {
        height: 40px;
        margin: 5px 10px;
        padding: 2px;
        text-align: center;
        // font-size: 12px;
        @include add-size(12px, $size);
        img {
          height: 16px;
          width: 16px;
        }
      }
      .bgShadow {
        border-radius: 4px;
        background-color: #E0E2EB;
      }
    }

    .main_operate {
      width: 353px;
      height: 100%;
      padding-top: 4px;
      // background-color: #F8F8F8;
      background-color: var(--theme-bg-color);
      // border: 1px solid #A2B5CC;
    }

    .main_conent {
      width: calc(100% - 376px);
      height: 100%;
      position: relative;
      .breadcrumb{
        position: absolute;
        z-index: 30;
        @include add-size(14px, $size);
        color: #9B9EA8 ;
        line-height: 30px;
        width: 50%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .table_box {
        position: relative;
        width: 100%;
        height: 100%;
        // background-color: #4a8df0;
        border-radius: 5px;
        overflow-x: hidden;
        overflow-y: hidden;
      }

      .globalView {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 4px;
        right: 0;
        z-index: 30;
        background-color: var(--theme-bg-color);
      }

    }
    .main_display {
      width: 14px;
      height: 98%;
      background-color: #AABBD9;
      color: #fff;
      text-align: center;
      line-height: 80vh;
      border-radius: 4px;
    }
    .main_hidden {
      width: 14px;
      height: 98%;
      color: #fff;
      text-align: center;
      line-height: 80vh;
      border-radius: 4px;
    }

  }
  :deep(.ant-tabs .ant-tabs-tab)  {
    line-height: 4px;
    padding: 12px 15px;
  }

  :deep(.ant-modal .ant-modal-content) {
    padding: 15px !important;
  }
  .modal_confirm{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
            img {
              width: 36px;
              height: 36px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
