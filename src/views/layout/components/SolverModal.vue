<template>
  <a-modal wrapClassName="modal_solver" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t('求解器设置') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <div class="modal_solver_content">
        <div>
          <a-radio-group v-model:value="solverType">
            <a-radio value="COPT" disabled>
              <img src="@/assets/components/CPOT.png" alt="">
              <span>COPT</span>
            </a-radio>
            <a-radio value="HiGHS" disabled>
              <img src="@/assets/components/HiGHS.png" alt="">
              <span>HiGHS</span>
            </a-radio>
            <a-radio value="CPLEX" disabled>
              <img src="@/assets/components/CPLEX.png" alt="">
              <span>CPLEX</span>
            </a-radio>
            <a-radio value="SCIP" disabled>
              <img src="@/assets/components/SCIP.png" alt="">
              <span>SCIP</span>
            </a-radio>
            <a-radio value="Gurobi" disabled>
              <img src="@/assets/components/Gurobi.png" alt="">
              <span>Gurobi</span>
            </a-radio>
            <a-radio value="Tj_Solver">
              <img src="@/assets/components/Tj Solver.png" alt="">
              <span>Tj Solver</span>
            </a-radio>
          </a-radio-group>
        </div>
        <div class="modal_btn">
          <a-button @click="confirm" type="primary">{{ $t('确认') }}</a-button>
          <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
        </div>
      </div>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import { reactive } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const store = settingStore()
const { solverType } = storeToRefs(store)
const state = reactive({
	ifShow: true
})
const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}
const confirm = () => {
	emit('close')
}
</script>
<style lang="scss">
  .modal_solver{
    .ant-modal{
      width: auto!important;
      .ant-modal-body{
        >div{
          .modal_solver_content{
                padding: 30px 30px 66px;
                >div:first-child{
                  padding-bottom: 30px;
                  .ant-radio-group{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    grid-gap: 50px;
                    .ant-radio-wrapper{
                      margin: 0;
                    }
                    label{
                      background: #F4F7FB;
                      padding: 10px 20px;
                      display: flex;
                      align-items: center;
                      >span:last-child{
                        display: flex;
                        align-items: center;
                        span{
                          font-size: 18px;
                          padding: 10px;
                        }
                      }
                      img{
                        height:70px;
                        width: 70px;
                        margin: 0 20px 0 10px;
                      }
                    }
                  }
                }
          }
        }
      }
    }
  }
</style>
