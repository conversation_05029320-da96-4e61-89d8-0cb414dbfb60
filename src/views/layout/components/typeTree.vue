<!-- 可能存在问题 -->
<template>
  <div v-if="state.activeTab == 'dataCase'" class="tree_box" ref="treeBoxRef">
    <a-tree
      ref="treeRef"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :loadedKeys="loadedKeys"
      :tree-data="treeData"
      :field-names="fieldNames"
      :autoExpandParent="autoExpandParent"
      :show-icon="true"
      :height="state.treeHeight"
      @select="onSelect"
    >
      <template #icon="{ value, f_node }">
        <template v-if="value == 'timeseries'">
          <img src="@/assets/tree-icon/时序.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="value == '网侧'">
          <img src="@/assets/tree-icon/网侧.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == '源侧'">
          <img src="@/assets/tree-icon/源侧.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == '荷侧'">
          <img src="@/assets/tree-icon/荷侧.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == '储能'">
          <img src="@/assets/tree-icon/储能.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == '源侧待规划设备'">
          <img src="@/assets/tree-icon/待规划源.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == '储能待规划设备'">
          <img src="@/assets/tree-icon/待规划储.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == '网侧待规划设备'">
          <img src="@/assets/tree-icon/待规划网.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>

        <template v-else-if="f_node == '网侧' || f_node == '网侧待规划设备'">
          <img src="@/assets/tree-icon/网侧内部.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="f_node == '源侧' || f_node == '源侧待规划设备'">
          <img src="@/assets/tree-icon/源侧内部.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="f_node == '荷侧'">
          <img src="@/assets/tree-icon/荷侧内部.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="f_node == '储能' || f_node == '储能待规划设备'">
          <img src="@/assets/tree-icon/储能内部.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>

        <template v-else>
          <img src="@/assets/tree-icon/设备分区域.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
      </template>
      <template #title="{ label, failNumber }">
        <span v-if="label.indexOf(searchValue) > -1">
          {{ label.substr(0, label.indexOf(searchValue)) }}
          <span style="color: #f50">{{ searchValue }}</span>
          {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
          <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
          <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
        </span>
        <span v-else>{{ label }}</span>
      </template>
    </a-tree>
  </div>
  <div v-else ref="treeBoxRef" class="tree_box">
    <a-tree
      v-model:expandedKeys="expandedCalKeys"
      v-model:selectedKeys="selectedCalKeys"
      :tree-data="state.countWayTree"
      :field-names="fieldNames"
      :autoExpandParent="autoExpandParent"
      :show-icon="true"
      @select="onSelectcal"
    >
      <template #icon="{ value }">
        <!-- 基础场景 -->
        <template v-if="value == 'count_scene'">
          <img src="@/assets/tree-icon/计算场景.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="value == $t('平水年')|| value == $t('枯水年') || value == $t('（空）')">
          <img src="@/assets/tree-icon/单场景.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else>
          <img src="@/assets/tree-icon/设备分区域.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
      </template>
      <template #title="{ label, failNumber }">
        <span v-if="label.indexOf(searchValue) > -1">
          {{ label.substr(0, label.indexOf(searchValue)) }}
          <span style="color: #f50">{{ searchValue }}</span>
          {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
          <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
          <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
        </span>
        <span v-else>{{ label }}</span>
      </template>
    </a-tree>
  </div>

</template>
<script setup>

import Mitt from '@/utils/mitt.js'
import { ref, reactive, defineEmits, onMounted, onActivated, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { storeToRefs } from 'pinia'
import { routeStore } from '@/store/routeStore'
// import { getModifyDataApi, getTreeMenu, globalParameters } from '@/api/exampleApi'
import { getTreeMenu } from '@/api/exampleApi'
import { t } from '@/utils/common'
const route = useRoute()
const store = routeStore()
const { activeKey, routeTabs } = storeToRefs(store)

// tree数据
const treeData = ref([])
const treeStructure = ref({})

const emits = defineEmits(['selectTree'])
const expandedKeys = ref([])
const expandedCalKeys = ref(['count_scene'])

const selectedKeys = ref([routeTabs.value.find(item => item.key == activeKey.value).treeNode || sessionStorage.getItem('treeType') || 'bus'])
const selectedCalKeys = ref([])

const loadedKeys = ref([]) // 已经加载的节点会存在这个里面

// const props = defineProps({
// 	treeHeight: {
// 		type: Number,
// 		default: 620
// 	}
// })

const state = reactive({
	routePath: route.fullPath,
	activeTab: 'dataCase',
	scrollTop: 0,
	treeChildNode: [],
	treeHeight: 620,
	selectable: true,
	countWayTree: [
		{
			label: t('计算场景'),
			value: 'count_scene',
			disabled: false,
			children: [{
				label: t('（空）'),
				value: 'base_scene',
				failNumber: null
			}]
		}
	]
})

const treeBoxRef = ref()
const treeRef = ref()
const dataList = ref([])

const getTreeTabChange = (val) => {
	if (state.routePath !== route.fullPath) return
	dataList.value = []
	state.activeTab = val
	nextTick(() => {
		screenScale()
	})
}

const handleTreeNodeScroll = (val) => {
	if (state.routePath !== route.fullPath) return
	if (state.activeTab !== 'dataCase') return
	const treeNode = val || selectedKeys.value[0]
	treeRef.value.scrollTo({ key: treeNode })
}

const fieldNames = {
	key: 'value',
	title: 'label',
	failNumber: 'failNumber'
}

// 根据children来判断展开项
const traverseTreeForChild = (data) => {
	if (data === null || data === undefined) return // 当前节点为空，则直接返回
	for (let i = 0; i < data.length; i++) {
		dataList.value.push({
			value: data[i].value,
			label: data[i].label
		})
		if (data[i].children && data[i].children.length > 0) {
			expandedKeys.value.push(data[i].value)
			traverseTreeForChild(data[i].children) // 递归遍历子节点
		}
	}
}

// 根据amount来判断展开项
const traverseTree = (data) => {
	if (data === null || data === undefined) return // 当前节点为空，则直接返回
	for (let i = 0; i < data.length; i++) {
		dataList.value.push({
			value: data[i].value,
			label: data[i].label
		})
		if (!data[i].amount && !data[i].isLast) {
			expandedKeys.value.push(data[i].value)
			traverseTree(data[i].children) // 递归遍历子节点
		}
	}
}

const searchValue = ref('')
const autoExpandParent = ref(false)

const onSelect = (selectedKey, { selected, selectedNodes, node, event }) => {
	// 点击最外层父节点 仅 展开折叠功能
	if (node.isExpand) {
		const index = expandedKeys.value.indexOf(node.key)
		if (index === -1) {
			expandedKeys.value.push(node.key)
		} else {
			expandedKeys.value = expandedKeys.value.filter((item) => item !== node.key)
		}
		selectedKeys.value = [sessionStorage.getItem('treeType')]
		return
	}
	// 点击当前选中的节点 直接 return
	if (selectedKey.length === 0) { // 点击已选中节点
		// return selectedKeys.value[0] = sessionStorage.getItem('treeNode')
		return selectedKeys.value[0] = sessionStorage.getItem('treeType')
	}

	Mitt.emit('stopEditing')
	Mitt.emit('clearFilter')

	// setTimeout(() => {
	// 切换节点 当前算例有未保存的修改 提示先保存
	const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
	if (isUnsaved) {
		const targetNode = selectedKey[0] // 目标节点
		selectedKeys.value[0] = sessionStorage.getItem('treeType') // 当前节点

		if (sessionStorage.getItem('treeType').includes('-')) {
			// Mitt.emit('saveItemized', {
			// 	type: 'treeChange',
			// 	treeNode_new: targetNode,
			// 	treeNode_old: selectedKeys.value[0]
			// })

			Mitt.emit('treeChangeSaveItemized', {
				type: 'treeChange',
				treeNode_new: targetNode,
				treeNode_old: selectedKeys.value[0]
			})
		} else {
			// Mitt.emit('handleSaveAgtable', {
			// 	type: 'treeChange',
			// 	treeNode_new: targetNode
			// })
			Mitt.emit('treeChangesave', {
				type: 'treeChange',
				treeNode_new: targetNode
			})
		}
	} else {
		onSelectTreeChange(selectedKey[0])
	}
	localStorage.setItem('scrollTop', state.scrollTop)
	handleTreeNodeScroll()
}
const onSelectTreeChange = (selectedKey) => {
	// 上述条件通过 则切换节点成功 保存当前选中的节点
	sessionStorage.setItem('treeNode', selectedKey)
	sessionStorage.setItem('treeType', selectedKey)
	selectedKeys.value[0] = selectedKey
	if (selectedKey.includes('-')) {
		if (selectedKey.includes('integrated')) {
			selectedKeys.value = [sessionStorage.getItem('treeType')]
			return message.warning(t('一体化电站暂不支持此功能') + '！')
		}

		// 点击最里层子节点 打开逐项视图
		Mitt.emit('handleItemizedView', selectedKey)
		expandedKeys.value.push(selectedKey.split('-')[0])
	} else {
		// 点击设备节点 显示设备table（ 逐项视图需关闭 ）
		emits('selectTree', selectedKey, treeStructure.value[selectedKey])
		Mitt.emit('handleOverView', selectedKey)
	}
}
Mitt.on('onSelectTreeChange', onSelectTreeChange)

const onSelectcal = (selectedKey, { selected, selectedNodes, node, event }) => {
	if (selectedKey.length === 0) { // 点击已选中节点
		selectedCalKeys.value = [selectedKey[0]]
		return
	}

	if (selectedKey[0] !== 'count_scene') {
		Mitt.emit('handleScene', selectedKey[0])
	}

	// localStorage.setItem('scrollTop', state.scrollTop)
}

const changeTree = (val) => {
	selectedCalKeys.value = [val]
}
Mitt.on('changeTree', changeTree)

// 全局参数设置
// const getGlobalParameters = () => {
// 	if (state.routePath !== route.fullPath) return
// 	globalParameters(
// 		{
// 			'import_string_func': 'teapcase:read_from_tc',
// 			'func_arg_dict': {
// 				'file_name': route.query.filePath,
// 				'sheet_name': 'parameter'
// 			}
// 		}
// 	).then(res => {
// 		const { data } = res.func_result.parameter

// 		// expandedCalKeys
// 	})
// }

// 处理tree的名称结构
const getTreeStructure = (data) => {
	data.forEach(item => {
		treeStructure.value[item.value] = item.label
		if (item.amount || item.amount == 0) {
			item.label = `${item.label}（${item.amount}）`
		}
		if (item.children) {
			getTreeStructure(item.children)
		}
	})
}

const onSearch = (val) => {
	if (state.routePath !== route.fullPath) return
	treeData.value = []
	dataList.value = []
	searchValue.value = val
	if (searchValue.value == '') {
		expandedKeys.value = []
		getTreeMenuList()
		return
	}

	getTreeMenu({
		'import_string_func': 'teapcase:tc_structure_tree',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'filter_name': searchValue.value
		}
	}).then(res => {
		treeData.value = res.func_result.tc_structure_tree
		traverseTreeForChild(treeData.value)
		getTreeStructure(treeData.value)
	})
}

// 获取树形结构菜单
const getTreeMenuList = (val) => {
	if (state.routePath !== route.fullPath) return
	getTreeMenu({
		'import_string_func': 'teapcase:tc_structure_tree',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'filter_name': ''
		}
	}).then(res => {
		const { hydropower_scenario, scenario, tc_structure_tree } = res.func_result
		state.hydropower_scenario = hydropower_scenario.data
		state.scenario = scenario.data
		state.tc_structure_tree = tc_structure_tree

		treeData.value = tc_structure_tree.filter(item => !item.children || item.children.length > 0)
		if (val != 'saveRefresh') {
			traverseTree(treeData.value)
		}
		getTreeStructure(treeData.value)

		// 仿真计算参数tree
		state.countWayTree[0].children = res.func_result.scenario.data.map(item => {
			return {
				value: item,
				label: item,
				isLeaf: true
			}
		})
		selectedCalKeys.value = [res.func_result.scenario_selected]
	})
}
Mitt.on('getTreeMenuList', getTreeMenuList)

// 刷新
const handleRefresh = () => {
	getTreeMenuList()
}
Mitt.on('handleRefresh', handleRefresh)

const screenScale = () => {
	const resizeObserver = new ResizeObserver(entries => {
		for (const entry of entries) {
			const { height } = entry.contentRect
			// 根据元素的新尺寸执行操作
			state.treeHeight = height
		}
	})
	const targetElement = document.querySelector('.tree_box')
	resizeObserver.observe(targetElement)
}

// 刷新
const getTreeHeight = (val) => {
	// tree 组件虚拟滚动的高度计算
	screenScale() // 防止切换tab, tree组件空白问题

	if (val) {
		setTimeout(() => {
			handleTreeNodeScroll(val)
		}, 50)
	}
}
Mitt.on('getTreeHeight', getTreeHeight)

defineExpose({ getTreeMenuList, traverseTree, getTreeTabChange, getTreeHeight, onSearch })

onMounted(() => {
	// screenScale()
	// window.addEventListener('resize', screenScale)
	getTreeMenuList()
})

onActivated(() => { // 防止切换tab, tree组件空白问题
	// state.treeHeight = props.treeHeight
	screenScale()
})

</script>
<style  lang="scss" scoped>
  .tree_box {
    width: 100%;
    height: 100%;
    // margin-top: 2px;
    // padding: 5px 10px;
    // border: 1px solid #e6e9f0;
    // border: 1px solid #A2B5CC;
    // border-top: 0;
    // border-radius: 0 0 5px 5px;
    // box-sizing: border-box;
    // overflow: auto; // 超出这个最大高度的数据，会被隐藏起来，上下滑动
  }

  .tree_box :deep(.ant-tree)  {
    background: transparent;
    color: #1E3D59;
    // overflow: auto;
    white-space: nowrap;
    // font-size: 14px;
    @include add-size(14px, $size);
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper, .ant-tree .ant-tree-checkbox+span) {
    width: 100%;
    padding: 0 0px;
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle, .ant-tree .ant-tree-checkbox+span .ant-tree-iconEle) {
    // vertical-align: middle;
    padding-top: 2px;
  }

  .tree_box :deep(.ant-tree .ant-tree-treenode) {
      width: 100%;
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected, .ant-tree .ant-tree-checkbox+span.ant-tree-node-selected) {
    width: 100%;
    background-color: #B4CEE6;
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper:hover, ) {
    width: 100%;
  }
  .tree_box :deep(.ant-tree-list-scrollbar) {
    width: 12px!important;
  }
  .tree_box :deep(.ant-tree-list-scrollbar-thumb) {
    border-radius: 2px!important;
    background: #999!important;
  }

  // .tree_box :deep(.ant-tree-list-holder-inner) {
  //   transform: translateY(0)!important;
  // }
</style>
