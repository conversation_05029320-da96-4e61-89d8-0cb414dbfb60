<template>
    <a-modal wrapClassName="modal_config" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('配置管理') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <a-spin :spinning="state.isLoading" size="large" :tip="$t('数据加载中')">
            <div class="modal_content">
                <div>
                    <div ref="scroll">
                        <div v-for="(item,index) in state.dataList" :key="index" :class="state.activeItem.id==item.id?'active pointer':'pointer'" @click="active(item.id,item)">
                            <div class="edit_input" v-if="state.activeItem.id==item.id&&state.editId==item.id">
                                <a-input @click.stop="" v-model:value="state.activeItem.config_name" />
                            </div>
                            <div  v-else>
                                <a-tooltip :mouseLeaveDelay="0">
                                    <template #title>{{ item.config_name }}</template>
                                    <p class="ellipsis">{{ item.config_name }}</p>
                                </a-tooltip>
                                <div class="icon_list center">
                                    <a-tooltip  :mouseLeaveDelay="0">
                                        <template #title>{{$t('编辑')}}</template>
                                        <div class="wrap_span" @click.stop="editConfig(item.id,item)">
                                            <form-outlined />
                                        </div>
                                    </a-tooltip>
                                    <a-tooltip  :mouseLeaveDelay="0">
                                        <template #title>{{$t('下载')}}</template>
                                        <div class="wrap_span" @click.stop="downloadConfig(item.id)">
                                            <DownloadOutlined />
                                        </div>
                                    </a-tooltip>
                                    <a-tooltip  :mouseLeaveDelay="0">
                                        <template #title>{{$t('删除')}}</template>
                                        <div class="wrap_span">
                                            <delete-outlined @click.stop="deleteConfig(item.id)"/>
                                        </div>
                                    </a-tooltip>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="relative" v-show="!state.isAdd">
                        <div class="add_btn" @click="addConfig">
                            <plus-outlined />
                            <p>{{$t('新增配置')}}</p>
                        </div>
                        <a-upload
                                v-model:file-list="state.fileList"
                                :beforeUpload="()=>false"
                                :showUploadList="false"
                                accept=".bpaconf"
                                :multiple="false"
                                name="file"
                                @change="handleChange"
                            >
                            <a-button type="primary" class="upload_btn center">
                                <template #icon><UploadOutlined /></template>
                            {{ $t('上传配置文件') }}</a-button>
                        </a-upload>
                    </div>
                </div>
                <div class="relative">
                    <div class="form_list">
                        <div>
                            <div class="center">
                                {{ $t('分区配置') }}
                            </div>
                            <div class="form_item">
                                <div>
                                    <p>{{$t('火 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_coal.join(','):'' }}</p>
                                    <a-input @change="change('zone_coal',$event)" v-else v-model:value="state.config.zone_coal" />
                                </div>
                                <div>
                                    <p>{{$t('燃 气')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_gas.join(','):'' }}</p>
                                    <a-input @change="change('zone_gas',$event)" v-else v-model:value="state.config.zone_gas" />
                                </div>
                                <div>
                                    <p>{{$t('水 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_hydro.join(','):'' }}</p>
                                    <a-input @change="change('zone_hydro',$event)" v-else v-model:value="state.config.zone_hydro" />
                                </div>
                                <div>
                                    <p>{{$t('光 伏')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_solar.join(','):'' }}</p>
                                    <a-input @change="change('zone_solar',$event)" v-else v-model:value="state.config.zone_solar" />
                                </div>
                                <div>
                                    <p>{{$t('风 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_wind.join(','):'' }}</p>
                                    <a-input @change="change('zone_wind',$event)" v-else v-model:value="state.config.zone_wind" />
                                </div>
                                <div>
                                    <p>{{$t('核 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_nuclear.join(','):'' }}</p>
                                    <a-input @change="change('zone_nuclear',$event)" v-else v-model:value="state.config.zone_nuclear" />
                                </div>
                                <div>
                                    <p>{{$t('抽 蓄')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_pump_hydro.join(','):'' }}</p>
                                    <a-input @change="change('zone_pump_hydro',$event)" v-else v-model:value="state.config.zone_pump_hydro" />
                                </div>
                                <div>
                                    <p>{{$t('储 能')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.zone_energy_storage.join(','):'' }}</p>
                                    <a-input @change="change('zone_energy_storage',$event)" v-else v-model:value="state.config.zone_energy_storage" />
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="center">
                                {{ $t('关键词配置') }}
                            </div>
                            <div class="form_item">
                                <div>
                                    <p>{{$t('火 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.coal_key.join(','):'' }}</p>
                                    <a-input @change="change('coal_key',$event)" :placeholder="$t('火电,煤电,煤,火')" v-else v-model:value="state.config.coal_key" />
                                </div>
                                <div>
                                    <p>{{$t('燃 气')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.gas_key.join(','):'' }}</p>
                                    <a-input @change="change('gas_key',$event)" :placeholder="$t('燃气,气,然气,9E,9F,6E,6F')" v-else v-model:value="state.config.gas_key" />
                                </div>
                                <div>
                                    <p>{{$t('水 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.hydro_key.join(','):'' }}</p>
                                    <a-input @change="change('hydro_key',$event)" :placeholder="$t('水电,梯级,坝,一级,二级,三级,四级')" v-else v-model:value="state.config.hydro_key" />
                                </div>
                                <div>
                                    <p>{{$t('光 伏')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.solar_key.join(','):'' }}</p>
                                    <a-input @change="change('solar_key',$event)" :placeholder="$t('太阳,光伏,光')" v-else v-model:value="state.config.solar_key" />
                                </div>
                                <div>
                                    <p>{{$t('风 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.wind_key.join(','):'' }}</p>
                                    <a-input @change="change('wind_key',$event)" :placeholder="$t('风电,风')" v-else v-model:value="state.config.wind_key" />
                                </div>
                                <div>
                                    <p>{{$t('核 电')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.nuclear_key.join(','):'' }}</p>
                                    <a-input @change="change('nuclear_key',$event)" :placeholder="$t('核电,核')" v-else v-model:value="state.config.nuclear_key" />
                                </div>
                                <div>
                                    <p>{{$t('抽 蓄')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.pump_hydro_key.join(','):'' }}</p>
                                    <a-input @change="change('pump_hydro_key',$event)" :placeholder="$t('抽蓄,抽,蓄')" v-else v-model:value="state.config.pump_hydro_key" />
                                </div>
                                <div>
                                    <p>{{$t('储 能')}}</p>
                                    <p v-if="!state.editId">{{ state.activeItem.config_dict?state.activeItem.config_dict.energy_storage_key.join(','):'' }}</p>
                                    <a-input @change="change('energy_storage_key',$event)" :placeholder="$t('储能,储')" v-else v-model:value="state.config.energy_storage_key" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="config_checked">
                        <!-- <a-checkbox :disabled="state.disabled" v-model:checked="state.pz_flag">是否考虑PZ卡系数</a-checkbox> -->
                        <a-row>
                          <a-col :span="12">
                            <a-checkbox :disabled="state.disabled" v-model:checked="state.config.pz_load_p">{{$t('是否考虑负荷有功PZ卡的调节系数')}}</a-checkbox>
                          </a-col>
                          <a-col :span="12">
                            <a-checkbox :disabled="state.disabled" v-model:checked="state.config.pz_load_q">{{$t('考虑负荷无功PZ卡的调节系数')}}</a-checkbox>
                          </a-col>
                          <a-col :span="12">
                            <a-checkbox :disabled="state.disabled" v-model:checked="state.config.pz_gen_p">{{$t('是否考虑发电有功PZ卡的调节系数')}}</a-checkbox>
                          </a-col>
                          <a-col :span="12">
                            <a-checkbox :disabled="state.disabled" v-model:checked="state.config.pz_gen_q">{{$t('是否考虑发电无功PZ卡的调节系数')}}</a-checkbox>
                          </a-col>
                        </a-row>
                    </div>
                    <div v-show="state.editId">
                        <a-button @click="cancel">{{$t('取消')}}</a-button>
                        <a-button @click="save" type="primary">{{$t('保存')}}</a-button>
                    </div>
                </div>
            </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { CloseOutlined, FormOutlined, DownloadOutlined, DeleteOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { downloadApiFile, t } from '@/utils/common.js'
import { openModal } from '@/utils/teap.js'
import { UpdateConfig, AddConfig, DelConfig, GetAllConfig, DownloadConfig, UploadConfig } from '@/api/index'
import message from '@/utils/message'
const scroll = ref()
const state = reactive({
	ifShow: true,
	editId: undefined,
	isAdd: false,
	// pz_flag: false,
	disabled: true,
	isLoading: true,
	activeItem: {},
	fileList: [],
	dataList: [
	],
	config: {
		zone_coal: '',
		zone_gas: '',
		zone_hydro: '',
		zone_solar: '',
		zone_wind: '',
		zone_nuclear: '',
		zone_pump_hydro: '',
		zone_energy_storage: '',
		exp_key: '',
		coal_key: '',
		gas_key: '',
		hydro_key: '',
		solar_key: '',
		wind_key: '',
		nuclear_key: '',
		pump_hydro_key: '',
		energy_storage_key: '',
		pz_load_p: true,
		pz_load_q: true,
		pz_gen_p: true,
		pz_gen_q: true
	}
})
const emit = defineEmits(['close', 'update'])
const active = (id, item) => {
	if (state.isAdd) return
	if (state.activeItem.id == id) return
	state.activeItem = Object.assign({}, item)
	state.editId = undefined
	state.disabled = true

	// state.pz_flag = !!state.activeItem.pz_flag
	state.config.pz_load_p = item.config_dict.pz_load_p == undefined ? true : item.config_dict.pz_load_p
	state.config.pz_load_q = item.config_dict.pz_load_q == undefined ? true : item.config_dict.pz_load_q
	state.config.pz_gen_p = item.config_dict.pz_gen_p == undefined ? true : item.config_dict.pz_gen_p
	state.config.pz_gen_q = item.config_dict.pz_gen_q == undefined ? true : item.config_dict.pz_gen_q
}
const addConfig = () => {
	state.editId = undefined
	state.isAdd = true
	state.dataList.push({
		config_name: t('新增配置'),
		id: 'edit'
	})
	state.activeItem = {
		config_name: t('新增配置'),
		id: 'edit'
	}
	state.config = {
		zone_coal: '',
		zone_gas: '',
		zone_hydro: '',
		zone_solar: '',
		zone_wind: '',
		zone_nuclear: '',
		zone_pump_hydro: '',
		zone_energy_storage: '',
		coal_key: '',
		gas_key: '',
		hydro_key: '',
		solar_key: '',
		wind_key: '',
		nuclear_key: '',
		pump_hydro_key: '',
		energy_storage_key: '',
		// coal_key: '火电,煤电,煤,火',
		// gas_key: '燃气,气,然气,9E,9F,6E,6F',
		// hydro_key: '水电,梯级,坝,一级,二级,三级,四级',
		// solar_key: '太阳,光伏,光',
		// wind_key: '风电,风',
		// nuclear_key: '核电,核',
		// pump_hydro_key: '抽蓄,抽,蓄',
		// energy_storage_key: '储能,储',
		pz_load_p: true,
		pz_load_q: true,
		pz_gen_p: true,
		pz_gen_q: true
	}
	// state.pz_flag = true

	state.editId = 'edit'
	state.disabled = false
	nextTick(() => {
		scroll.value.scrollTop = scroll.value.scrollHeight
	})
}
const closeModal = () => {
	emit('close')
}
const downloadConfig = (id) => {
	state.isLoading = true
	DownloadConfig({
		config_id: id
	}).then(res => {
		downloadApiFile(res)
		state.isLoading = false
	})
}
const change = (value, e) => {
	// state.config[value] = e.target.value.replace(/[^a-zA-Z0-9\u4E00-\u9FA5]/g,",")
	state.config[value] = e.target.value.replace(/，/g, ',')
}
const editConfig = (id, item) => {
	if (state.isAdd) return
	state.activeItem = Object.assign({}, item)
	state.editId = id
	state.disabled = false
	state.config.zone_coal = state.activeItem.config_dict.zone_coal.join(',')
	state.config.zone_gas = state.activeItem.config_dict.zone_gas.join(',')
	state.config.zone_hydro = state.activeItem.config_dict.zone_hydro.join(',')
	state.config.zone_solar = state.activeItem.config_dict.zone_solar.join(',')
	state.config.zone_wind = state.activeItem.config_dict.zone_wind.join(',')
	state.config.zone_nuclear = state.activeItem.config_dict.zone_nuclear.join(',')
	state.config.zone_pump_hydro = state.activeItem.config_dict.zone_pump_hydro.join(',')
	state.config.zone_energy_storage = state.activeItem.config_dict.zone_energy_storage.join(',')
	state.config.coal_key = state.activeItem.config_dict.coal_key.join(',')
	state.config.gas_key = state.activeItem.config_dict.gas_key.join(',')
	state.config.hydro_key = state.activeItem.config_dict.hydro_key.join(',')
	state.config.solar_key = state.activeItem.config_dict.solar_key.join(',')
	state.config.wind_key = state.activeItem.config_dict.wind_key.join(',')
	state.config.nuclear_key = state.activeItem.config_dict.nuclear_key.join(',')
	state.config.pump_hydro_key = state.activeItem.config_dict.pump_hydro_key.join(',')
	state.config.energy_storage_key = state.activeItem.config_dict.energy_storage_key.join(',')

	state.config.pz_load_p = state.activeItem.config_dict.pz_load_p == undefined ? true : state.activeItem.config_dict.pz_load_p
	state.config.pz_load_q = state.activeItem.config_dict.pz_load_q == undefined ? true : state.activeItem.config_dict.pz_load_q
	state.config.pz_gen_p = state.activeItem.config_dict.pz_gen_p == undefined ? true : state.activeItem.config_dict.pz_gen_p
	state.config.pz_gen_q = state.activeItem.config_dict.pz_gen_q == undefined ? true : state.activeItem.config_dict.pz_gen_q
	// state.pz_flag = !!state.activeItem.pz_flag
}
const save = () => {
	state.disabled = true
	if (state.isAdd) {
		AddConfig({
			config_name: state.activeItem.config_name,
			// pz_flag: state.pz_flag ? 1 : 0,
			config_dict: {
				zone_coal: state.config.zone_coal ? state.config.zone_coal.split(',').filter(item => item) : [],
				zone_gas: state.config.zone_gas ? state.config.zone_gas.split(',').filter(item => item) : [],
				zone_hydro: state.config.zone_hydro ? state.config.zone_hydro.split(',').filter(item => item) : [],
				zone_solar: state.config.zone_solar ? state.config.zone_solar.split(',').filter(item => item) : [],
				zone_wind: state.config.zone_wind ? state.config.zone_wind.split(',').filter(item => item) : [],
				zone_nuclear: state.config.zone_nuclear ? state.config.zone_nuclear.split(',').filter(item => item) : [],
				zone_pump_hydro: state.config.zone_pump_hydro ? state.config.zone_pump_hydro.split(',').filter(item => item) : [],
				zone_energy_storage: state.config.zone_energy_storage ? state.config.zone_energy_storage.split(',').filter(item => item) : [],
				exp_key: ['直流', '厂', '场', '站', '蓄', '电', '光'],
				coal_key: state.config.coal_key ? state.config.coal_key.split(',').filter(item => item) : [],
				gas_key: state.config.gas_key ? state.config.gas_key.split(',').filter(item => item) : [],
				hydro_key: state.config.hydro_key ? state.config.hydro_key.split(',').filter(item => item) : [],
				solar_key: state.config.solar_key ? state.config.solar_key.split(',').filter(item => item) : [],
				wind_key: state.config.wind_key ? state.config.wind_key.split(',').filter(item => item) : [],
				nuclear_key: state.config.nuclear_key ? state.config.nuclear_key.split(',').filter(item => item) : [],
				pump_hydro_key: state.config.pump_hydro_key ? state.config.pump_hydro_key.split(',').filter(item => item) : [],
				energy_storage_key: state.config.energy_storage_key ? state.config.energy_storage_key.split(',').filter(item => item) : [],
				pz_load_p: state.config.pz_load_p,
				pz_load_q: state.config.pz_load_q,
				pz_gen_p: state.config.pz_gen_p,
				pz_gen_q: state.config.pz_gen_q
			}
		}).then(res => {
			if (res.code == 1) {
				state.isAdd = false
				message.success(res.message)
				initData()
			}
		})
	} else {
		UpdateConfig({
			config_name: state.activeItem.config_name,
			config_id: state.activeItem.id,
			// pz_flag: state.pz_flag ? 1 : 0,
			config_dict: {
				zone_coal: state.config.zone_coal ? state.config.zone_coal.split(',').filter(item => item) : [],
				zone_gas: state.config.zone_gas ? state.config.zone_gas.split(',').filter(item => item) : [],
				zone_hydro: state.config.zone_hydro ? state.config.zone_hydro.split(',').filter(item => item) : [],
				zone_solar: state.config.zone_solar ? state.config.zone_solar.split(',').filter(item => item) : [],
				zone_wind: state.config.zone_wind ? state.config.zone_wind.split(',').filter(item => item) : [],
				zone_nuclear: state.config.zone_nuclear ? state.config.zone_nuclear.split(',').filter(item => item) : [],
				zone_pump_hydro: state.config.zone_pump_hydro ? state.config.zone_pump_hydro.split(',').filter(item => item) : [],
				zone_energy_storage: state.config.zone_energy_storage ? state.config.zone_energy_storage.split(',').filter(item => item) : [],
				exp_key: ['直流', '厂', '场', '站', '蓄', '电', '光'],
				coal_key: state.config.coal_key ? state.config.coal_key.split(',').filter(item => item) : [],
				gas_key: state.config.gas_key ? state.config.gas_key.split(',').filter(item => item) : [],
				hydro_key: state.config.hydro_key ? state.config.hydro_key.split(',').filter(item => item) : [],
				solar_key: state.config.solar_key ? state.config.solar_key.split(',').filter(item => item) : [],
				wind_key: state.config.wind_key ? state.config.wind_key.split(',').filter(item => item) : [],
				nuclear_key: state.config.nuclear_key ? state.config.nuclear_key.split(',').filter(item => item) : [],
				pump_hydro_key: state.config.pump_hydro_key ? state.config.pump_hydro_key.split(',').filter(item => item) : [],
				energy_storage_key: state.config.energy_storage_key ? state.config.energy_storage_key.split(',').filter(item => item) : [],
				pz_load_p: state.config.pz_load_p,
				pz_load_q: state.config.pz_load_q,
				pz_gen_p: state.config.pz_gen_p,
				pz_gen_q: state.config.pz_gen_q
			}
		}).then(res => {
			if (res.code == 1) {
				message.success(res.message)
				initData()
			}
		})
	}
}
const cancel = () => {
	state.disabled = true
	if (state.isAdd) {
		state.activeItem = {}
		state.editId = undefined
		state.isAdd = false
		state.dataList.pop()
	} else {
		state.editId = undefined
	}
}
const handleChange = (info) => {
	state.isLoading = true
	const formdata = new FormData()
	formdata.append('file', info.file)
	formdata.append('config_name', info.file.name)
	UploadConfig({}, formdata).then(res => {
		state.isLoading = false
		if (res.code == 1) {
			message.success(res.message)
			initData()
		}
	})
}
const deleteConfig = (id) => {
	if (state.isAdd) return
	openModal(t('提示') + ': ' + t('确认删除选中文件') + '?').then(res => {
		DelConfig({
			config_id: id
		}).then(res => {
			if (res.code == 1) {
				message.success(res.message)
				initData()
			}
		})
	}).catch(error => {
		console.log(error)
	})
}
const initData = () => {
	state.isLoading = true
	GetAllConfig({}).then(res => {
		if (res.code == 1) {
			state.dataList = res.data
			state.activeItem = {}
			state.editId = undefined
			state.isLoading = false
		} else {
			state.isLoading = false
		}
	})
}
onMounted(() => {
	initData()
})
</script>
<style lang="scss">
  .modal_config{
    .ant-modal{
        width: auto!important;
        .ant-modal-body{
            >div{
                .modal_content{
                    width: 1105px!important;
                    height: 720px;
                    display: grid;
                    grid-template-columns: 1fr 2fr;
                    grid-gap: 40px;
                    padding:28px;
                    >div{
                        border: 1px solid #d9d9d9;
                        border-radius: 10px;
                        padding: 10px;
                    }
                    >div:first-child{
                        >div:first-child{
                            height: 600px;
                            overflow-y: overlay;
                            .active{
                                background-color: rgb(232, 233, 233);
                                p{
                                    color: var(--base-color);
                                }
                            }
                            >div>div{
                                margin-bottom: 5px;
                                font-size: 16px;
                                line-height: 32px;
                                display: grid;
                                grid-template-columns: 4fr 1fr;
                                padding: 0 10px;
                                &:hover{
                                    background-color: rgb(232, 233, 233);
                                    p{
                                        color: var(--base-color);
                                    }
                                }
                                .wrap_span{
                                    width: 22px;
                                    height: 22px;
                                    border-radius: 5px;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    &:hover{
                                        background-color: rgb(232, 233, 233);
                                        cursor: pointer;
                                        color: var(--base-color);
                                    }
                                }
                            }
                            .edit_input{
                                grid-template-columns: 1fr;
                                padding: 0;
                            }
                        }
                        >div:last-child{
                            margin-top: 5px;
                            line-height: 32px;
                            font-size: 16px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            .add_btn{
                                display: flex;
                                &:hover{
                                    cursor: pointer;
                                    color: var(--base-color);
                                }
                            }
                            .upload_btn{
                                // left: 40px;
                                right: 15px;
                                // top: -12px;
                            }

                        }
                    }
                    >div:last-child{
                        .form_list{
                            >div{
                                border: 1px solid #d9d9d9;
                                display: flex;
                                input{
                                    border: none;
                                    width: 440px;
                                    border-bottom: 1px solid #d9d9d9;
                                }
                                .ant-input:focus, .ant-input-focused{
                                    box-shadow: none;
                                }
                                >div:first-child{
                                    width: 50px;
                                    font-size: 22px;
                                    letter-spacing: 5px;
                                    font-weight: bolder;
                                    writing-mode: vertical-rl;
                                    text-align: center;
                                    background-color: #d9d9d9;
                                    border-radius: 10px 0 0 10px;
                                }
                                >div:last-child{
                                    padding: 10px;
                                }
                            }
                            >div{
                                border-radius: 10px;
                            }
                            >div:first-child{
                                margin-bottom: 10px;
                            }
                            .form_item{
                                >div{
                                    display: flex;
                                    line-height: 31px;
                                    >p:first-child{
                                        text-align: center;
                                        width: 100px;
                                        font-weight: bolder;
                                    }
                                    >p:last-child{
                                        width: 460px;
                                        overflow: hidden;
                                        text-overflow: ellipsis; // 显示省略符号来代表被修剪的文本。
                                        white-space: nowrap;// white-space
                                    }
                                    p{
                                        font-size: 14px;
                                    }
                                }
                            }
                        }
                        .config_checked{
                            // position: absolute;
                            // bottom: 6px;
                            // width: 100%;
                            margin-top: 2px;
                            span{
                                font-size: 14px;
                            }
                        }
                        >div:last-child{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            height: 50px;
                            position: absolute;
                            right: 12px;
                            bottom: 4px;
                            button{
                                height: 36px;
                                font-size: 16px;
                                width: 120px;
                                margin-left: 20px;
                            }
                        }
                    }
                }
            }
        }
    }
  }
</style>
