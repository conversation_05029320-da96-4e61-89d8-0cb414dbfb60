<template>
    <div class="user-select modal_result">
        <div class="resultTabs">
            <!-- <a-tabs v-model:activeKey="activeKey" @change="tabsChange">
                <a-tab-pane v-for="item in state.tabs" :key="item.value" :tab="item.label"></a-tab-pane>
            </a-tabs> -->
            <handle-tab v-model="activeKey" :tabData="state.tabs" @change="tabsChange"></handle-tab>
        </div>
        <div class="resultMain">
            <a-spin size="large" :spinning="state.loading">
                <div v-if="activeKey == '1'" class="modal_result_data">
                    <gis-system @loading="state.loading = true" @cancel="state.loading = false" v-if="state.activeKeyShow1"></gis-system>
                </div>
                <div v-if="activeKey == '2'" class="modal_result_data">
                    <gis-table @loading="state.loading = true" @cancel="state.loading = false" v-if="state.activeKeyShow2"></gis-table>
                </div>
                <div v-if="activeKey == '3'" class="modal_result_data">
                    <gis-result @startReady="startReady" :isFirstOpen="state.isFirstOpen" @openTg="state.TopologyMatchShow=true" :tgFilePath="state.tgFilePath" @loading="state.loading = true" @cancel="state.loading = false" v-if="state.activeKeyShow3"></gis-result>
                </div>
            </a-spin>
        </div>
    </div>
    <topology-match v-if="state.TopologyMatchShow" @close="close" @confirm="confirm"></topology-match>
  </template>
<script>
export default {
	name: 'resultGis'
}
</script>
<script setup>
/* eslint-disable no-unreachable */
/* eslint-disable no-empty */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
import { ref, reactive, onMounted, nextTick, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GetLineJobTaskResult } from '@/api/gis'
import { DownloadTempFile } from '@/api/index'
import { downloadApiFile, t } from '@/utils/common.js'
import { parseFilePath } from '@/utils/gis.js'
import Mitt from '@/utils/mitt.js'
const route = useRoute()
const state = reactive({
	routePath: route.fullPath,
	tgFilePath: undefined,
	loading: false,
	isFirstOpen: true,
	activeIndex: undefined,
	activeKeyShow1: true,
	activeKeyShow2: route.query.type != 'short_circuit',
	activeKeyShow3: false,
	TopologyMatchShow: false,
	tabs: route.query.type == 'short_circuit' ? [
		{ label: t('计算参数'), value: '1' },
		{ label: t('计算结果'), value: '2' },
		{ label: t('拓扑'), value: '3' }
	] : [
		{ label: t('计算结果'), value: '2' },
		{ label: t('拓扑'), value: '3' }
	]
})
const refresh = (val) => {
	state.loading = true
	state['activeKeyShow' + val] = false
	nextTick(() => {
		state['activeKeyShow' + val] = true
	})
}
const handleActionBar = (val) => {
	if (state.routePath !== route.fullPath) return
	if (val == 'paramView' && activeKey.value == '1') return
	if (val == 'paramView' && route.query.type == 'short_circuit') {
		activeKey.value = '1'
		state.activeKeyShow1 = true
	} else if (val == 'downloadResultH5') {
		GetLineJobTaskResult({
			// task_record_id: +route.query.id,
			result_file_path: route.query.filePath,
			group: 'case_file'
		}).then(res => {
			DownloadTempFile(parseFilePath(res.data)).then(res1 => {
				downloadApiFile(res1)
			})
		})
	}
}
Mitt.on('handleActionBar', handleActionBar)
const close = () => {
	state.TopologyMatchShow = false
}
const confirm = (val) => {
	state.TopologyMatchShow = false
	state.tgFilePath = val
	if (activeKey.value !== '3') activeKey.value = '3'
	if (state.activeKeyShow3 == true) {
		state.isFirstOpen = true
		refresh(3)
		return
	}
	state.activeKeyShow3 = true
}
const startReady = () => {
	state.isFirstOpen = false
}
const activeKey = ref(route.query.type == 'short_circuit' ? '1' : '2')
const tabsChange = (key) => {
	if (key == '3' && !state.activeKeyShow3) {
		state.TopologyMatchShow = true
		return
	}
	activeKey.value = key
	if (!state['activeKeyShow' + key]) {
		state['activeKeyShow' + key] = true
	}
}
onActivated(() => {
	if (activeKey.value == '3') {
		if (!state.activeKeyShow3) {
			state.TopologyMatchShow = true
		}
	}
})
onMounted(() => {
	// state.loading = true
})
</script>
<style lang="scss" scoped>
    .user-select {
        height: 100%;
        padding: 5px 10px 5px;
        background-color: var(--theme-bg-color);
        .resultTabs {
            width: 100%;
            height: 30px;
            padding: 0 20px;
            background-color: #F6F8FA;
            border: 1px solid #9B9EA8;
            border-radius: 6px;
        }
        .resultMain {
            width: 100%;
            height: calc(100% - 32px);
            margin-top: 2px;
            border: 1px solid #9B9EA8;
            border-radius: 6px;
            // background-color: #047cff;
        }
        .modal_result_data {
            width: 100%;
            height: 100%;
            color: #424246;
            padding: 15px;
            box-sizing: border-box;
            .modal_result_data_title {
                width: 100%;
                height: 30px;
                display: flex;
                line-height: 30px;
                border-bottom: 1px solid #B5B8CA;
                div {
                    width: 80px;
                    text-align: center;
                }
            }
            .modal_result_data_list {
                display: flex;
                div {
                    width: 80px;
                    height: 30px;
                    text-align: center;
                    line-height: 30px;
                }
            }
        }
    }
    :deep(.ant-tabs .ant-tabs-tab)  {
        line-height: 4px;
        padding: 12px 15px;
    }
</style>
