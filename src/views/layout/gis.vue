<template>
	<div :class="props.mode==0?'gis_main':'gis_show_main'">
		<div class="gis_tool">
			<div class="tool_div" :class="state.toolShow?'active':''" @click="state.toolShow = !state.toolShow;" @mouseleave="state.toolShow=false">
				<p>{{ $t('文件') }}</p>
				<div v-if="state.toolShow" @mouseleave="state.toolShow=false">
					<p @click="selectTool('save')">{{ $t('另存为tg') }}</p>
					<p @click="selectTool('exportPNG')">{{ $t('导出PNG') }}</p>
					<p @click="selectTool('exportSVG')">{{ $t('导出SVG') }}</p>
				</div>
			</div>
			<div class="tool_box">
				<p>{{ $t('地图') }}</p>
				<MinusOutlined :class="gisState.scale==20?'disabled':''" @click="changeScaleValue(-10)" />
				<a-slider :tooltip-open="false" v-model:value="gisState.scale" :min="20" :max="200" :step="10" @change="changeScale" />
				<PlusOutlined :class="gisState.scale==200?'disabled':''" @click="changeScaleValue(10)" />
				<p>{{ $t('元件') }}</p>
				<MinusOutlined :class="gisState.iconZoom==0.1?'disabled':''" @click="changeIconScaleValue(-0.1)" />
				<a-slider :tooltip-open="false" v-model:value="gisState.iconZoom" :min="0.1" :max="gisState.iconZoomMax" :step="0.1" @change="changeIconScale" />
				<PlusOutlined :class="gisState.iconZoom==gisState.iconZoomMax?'disabled':''" @click="changeIconScaleValue(0.1)" />
				<p>{{ $t('显示场站名称') }}</p><a-switch v-model:checked="gisState.showStationName" :checked-children="$t('是')" :un-checked-children="$t('否')" />
			</div>
			<!-- <p>电压层级: {{ state.zlevelId?'35kV及以下':'35kV及以上' }}</p> -->
			<p v-if="state.mode==0">{{ $t('关联tc文件') }} <span>{{ state.trPath }}</span></p>
		</div>
		<div class="gis_content">
			<gis-children
				v-if="state.isReady"
				ref="gisChildrenRef"
				@changeScale="changeMapScale"
				@changeZlevel="changeZlevel"
				:mode="state.mode"
				:gisData="state.gisData"
				:busData="state.busData"
				:isBlank="gisState.isBlank"
				:mapZoom="gisState.mapZoom"
				:scale="gisState.scale"
				:iconZoom="gisState.iconZoom"
				:originCenter="gisState.originCenter"
				:geoScale="gisState.geoScale"
				:partition="gisState.partition"
				:showStationName="gisState.showStationName"
			></gis-children>
			<div class="gis_show" ref="gis_show">

			</div>
			<div v-if="storeModal.loading" class="gis_loading">
				<div class="loader-wrapper">
					<div class="loader">
						<div class="roller"></div>
						<div class="roller"></div>
					</div>
					<div id="loader2" class="loader">
						<div class="roller"></div>
						<div class="roller"></div>
					</div>
					<div id="loader3" class="loader">
						<div class="roller"></div>
						<div class="roller"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<gis-message v-if="state.messageShow" @close="state.messageShow=false" :data="state.warningMessage"></gis-message>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import { markRaw, reactive, ref, onUnmounted, inject, onMounted, onActivated, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { deepClone, getMapScaleByBbox, getGeoJson, parseFilePath } from '@/utils/gis'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
import { loadingStore } from '@/store/loadingStore'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import Mitt from '@/utils/mitt.js'
import dayjs from 'dayjs'
import { init } from 'echarts'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const storeModal = loadingStore()
const echarts = inject('ec')
const route = useRoute()
const props = defineProps({
	mode: {
		type: Number,
		default: 0
	}
})
const state = reactive({
	routePath: route.fullPath,
	trPath: undefined,
	toolShow: false,
	gisData: {},
	isReady: false,
	scales: 1,
	zlevel: 0,
	mode: props.mode,
	busData: [],
	warningMessage: [],
	messageShow: false,
	zlevelId: undefined

})
const gisState = reactive({
	partition: undefined,
	scale: 100,
	mapZoom: 5,
	iconZoom: 1,
	iconZoomMax: 3,
	geoScale: undefined,
	originCenter: [],
	isBlank: false,
	showStationName: true
})
const gisChildrenRef = ref()
const gis_show = ref()
const mapChartShow = ref()
const changeMapScale = (val) => {
	gisState.scale = val
}
const changeScaleValue = (val) => {
	const scale = gisState.scale + val
	if (scale > 200) {
		gisState.scale = 200
		return
	} else if (scale < 20) {
		gisState.scale = 20
		return
	} else {
		gisState.scale = scale
		changeScale()
	}
}
const changeScale = () => {
	gisChildrenRef.value.changeScale(gisState.scale)
}
const changeIconScaleValue = (val) => {
	const iconZoom = gisState.iconZoom + val
	if (iconZoom > 3) {
		gisState.iconZoom = 3
		return
	} else if (iconZoom < 0.1) {
		gisState.iconZoom = 0.1
		return
	} else {
		gisState.iconZoom = iconZoom
		changeIconScale()
	}
}
const changeIconScale = () => {
	gisChildrenRef.value.changeIconScale(gisState.iconZoom)
}
const saveGis = (type) => {
	if (state.routePath !== route.fullPath) return
	nextTick(() => {
		gisChildrenRef.value.save(type)
	})
}
Mitt.on('saveGis', saveGis)
const selectTool = (val) => {
	if (val == 'save') {
		if (state.mode == 0) {
			gisChildrenRef.value.save('saveAsTg')
		} else {
			gisChildrenRef.value.save('saveAsTc')
		}
	} else if (val == 'exportSVG') {
		const option = deepClone(gisChildrenRef.value.mapOption)
		option.series[0].itemStyle.areaColor = 'transparent'
		option.series[0].itemStyle.borderColor = 'transparent'
		mapChartShow.value.setOption(option)
		const svgElement = gis_show.value
		const serializer = new XMLSerializer()
		const svgString = serializer.serializeToString(svgElement)
		const blob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' })
		const link = document.createElement('a')
		const url = URL.createObjectURL(blob)
		link.href = url
		link.download = 'gis_' + dayjs(new Date()).format('YYYYMMDD_HHmmss') + '.svg'
		link.click() // 触发下载
		URL.revokeObjectURL(url)
		mapChartShow.value.setOption({}, true)
	} else if (val == 'exportPNG') {
		const imgData = (gisChildrenRef.value.mapChart).getDataURL({
			type: 'png', // 支持的类型包括 'png', 'jpeg', 'jpg'
			pixelRatio: 2, // 清晰度倍数
			backgroundColor: 'transparent' // 设置背景颜色
		})
		var link = document.createElement('a')
		link.href = imgData
		link.download = 'gis_' + dayjs(new Date()).format('YYYYMMDD_HHmmss') + '.png'
		link.click() // 触发下载
	}
	state.toolShow = false
}
onActivated(() => {
	//
})
const initGis = async(jsonData, geoData) => {
	echarts.registerMap(gisState.partition, jsonData)
	gisState.geoScale = (0.75 / getMapScaleByBbox(jsonData, gis_show.value.clientWidth * state.scales, gis_show.value.clientHeight * state.scales).scale / 5)
	gisState.originCenter = getMapScaleByBbox(jsonData, gis_show.value.clientWidth * state.scales, gis_show.value.clientHeight * state.scales).center
	state.gisData = geoData
	if (state.mode == 0) {
		state.busData = (await basicApi({
			'import_string_func': 'teapgis:read_from_tc',
			'func_arg_dict': {
				'file_path': route.query.filePath,
				'sheet_name': 'bus'
			}
		})).func_result.bus.data
	}
	state.isReady = true
	storeModal.hiddenModal()
}
const changeZlevel = (val) => {
	state.zlevelId = val
	initData(true)
}
const initData = (val) => {
	storeModal.showModal()
	if (!val) {
		mapChartShow.value = markRaw(echarts.init(gis_show.value, '', { renderer: 'svg' }))
	}
	basicApi({
		'import_string_func': 'teapgis:get_station_and_channel',
		'func_arg_dict': Object.assign({
			parent_station_id: state.zlevelId
		}, state.mode == 1 ? {
			'tc_file_path': route.query.filePath
		} : {
			'tg_file_path': route.query.filePath
		})
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			state.trPath = parseFilePath(res.func_result.data.tc_file_path).file_name
			if (isChromeHigh.value) {
				state.scales = document.getElementsByClassName('home-body')[0].style.zoom || 1
			} else {
				state.scales = document.body.style.zoom
			}
			if (res.func_result.data.geojson_name) {
				gisState.partition = res.func_result.data.geojson_name
				basicApi({
					'import_string_func': 'teapgis:get_geojson',
					'func_arg_dict': {
						'tg_file_path': route.query.filePath,
						'geojson_name': res.func_result.data.geojson_name
					}
				}).then(res1 => {
					if (res1.code == 1 && res1.func_result.code == 1) {
						initGis(res1.func_result.data, res.func_result.data)
					}
				})
			} else {
				gisState.partition = route.query.name.split('.')[0] + Date.now()
				gisState.isBlank = true
				const jsonData = getGeoJson(res.func_result.data)
				initGis(jsonData, res.func_result.data)
			}
		}
	}).catch(() => {
		storeModal.hiddenModal()
	})
}
onMounted(async() => {
	initData()
})
</script>
<style lang="scss" scoped>
	.gis_show_main{
		position: absolute;
		z-index: 50;
	}
	.gis_main{
		position: relative;
	}
	.gis_main,.gis_show_main{
		height: 100%;
		width: 100%;
		user-select: none;
		background-color: #fff;
		.gis_tool{
			position: relative;
			display: flex;
			align-items: center;
			height: 34px;
			border: 1px solid rgb(170, 170, 170);
			background-color: rgb(251, 251, 251);
			padding-left: 15px;
			.active{
				background: rgb(219, 233, 245);
			}
			.disabled{
				opacity: 0.1;
				pointer-events: none;
			}
			>img{
				height: 25px;
				width: 25px;
				margin: 0 8px;
				padding: 1px;
				&:hover{
					cursor: pointer;
					background: rgb(219, 233, 245);
				}
			}
			.tool_div{
				position: relative;
				margin: 0 8px;
				>p{
					color: rgb(71, 71, 71);
					// font-family: Source Han Sans SC;
					font-size: 16px;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 28px;
					text-align: center;
					min-width: 60px;
					padding: 0 10px;
				}
				>div{
					box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.25);
					background: rgb(242, 242, 242);
					position: absolute;
					width: 130px;
					padding: 8px;
					top: 28px;
					left: 0px;
					z-index: 3;
					p{
						color: rgb(71, 71, 71);
						// font-family: Source Han Sans SC;
						font-size: 16px;
						font-weight: 400;
						letter-spacing: 0px;
						line-height: 30px;
						padding-left: 10px;
						&:hover{
							cursor: pointer;
							background: rgb(219, 233, 245);
						}
					}
				}
				&:hover{
					cursor: pointer;
					background: rgb(219, 233, 245);
				}
			}
			.tool_box{
				// width: 320px;
				margin-left: 50px;
				display: flex;
				justify-content: space-around;
				align-items: center;
				>p{
					padding: 0 10px;
				}
				>span{
					font-size: 16px;
					padding: 2px;
					&:hover{
						cursor: pointer;
						background: rgb(219, 233, 245);
					}
				}
				.ant-slider{
					width: 150px!important;

				}
			}
			>p{
				margin-left: 20px!important;
				font-size: 14px;
				span{
					font-weight: bolder;
					font-size: 16px;
					margin-left: 10px;
				}
			}
		}
		.gis_content{
			position: relative;
			height: calc(100% - 34px);
			width: 100%;
			.gis_show{
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 100%;
				// background-color: seagreen;
				z-index: -1;
				transform-origin: 0 0;
			}
			.gis_loading{
				position: absolute;
				display: flex;
				justify-content: center;
				align-items: center;
				left: 0;
				top: 0;
				height: 100%;
				width: 100%;
				z-index: 2;
				$body-bg: #55c1FA;
				$loader-duration: 1.2s;
				$loader-iteration-count: infinite;
				$loader-direction: normal;
				.loader-wrapper{
					width:148px;
					height: 100px;
					position: absolute;
				}
				.loader{
					width:148px;
					height: 100px;
					top: 0;
					left: 0;
					position: absolute;
					&:after{
						content: "";
						top: auto;
						position: absolute;
						display: block;
						animation: shadow $loader-duration $loader-iteration-count linear;
						-moz-animation: shadow $loader-duration $loader-iteration-count linear;
						bottom: 0em;
						left: 0;
						height: .25em;
						width: 1em;
						border-radius: 50%;
						background-color: darken($body-bg, 45%);
						opacity: 0.3;
					}
				}
				.roller,
				.roller:last-child{
					width: 70px;
					height: 70px;
					position: absolute;
					top: 0;
					left: 0;
					-webkit-animation: rollercoaster $loader-duration $loader-iteration-count linear;
					-webkit-transform: rotate(135deg);
					-moz-animation: rollercoaster $loader-duration $loader-iteration-count linear;
					-moz-transform: rotate(135deg);
					animation: rollercoaster $loader-duration $loader-iteration-count linear;
					transform: rotate(135deg);
				}
				.roller:last-child{
					left: auto;
					right: 0;
					-webkit-transform: rotate(-45deg);
					-webkit-animation: rollercoaster2 $loader-duration $loader-iteration-count linear;
					-moz-transform: rotate(-45deg);
					-moz-animation: rollercoaster2 $loader-duration $loader-iteration-count linear;
					transform: rotate(-45deg);
					animation: rollercoaster2 $loader-duration $loader-iteration-count linear;
				}
				.roller:before,
				.roller:last-child:before{
					content:"";
					display:block;
					width: 15px;
					height: 15px;
					background: black;
					border-radius: 50%;
				}
				@-webkit-keyframes rollercoaster {

					0%   {
						-webkit-transform: rotate(135deg);
					}
					8%  {
						-webkit-transform: rotate(240deg);
					}

					20%{
						-webkit-transform: rotate(300deg);
					}

					40%{
						-webkit-transform: rotate(380deg);
					}
					45%{
						-webkit-transform: rotate(440deg);
					}
					50%{
						-webkit-transform: rotate(495deg);
						opacity: 1;
					}
					50.1%{
						-webkit-transform: rotate(495deg);
						opacity: 0;
					}
					100%{
						-webkit-transform: rotate(495deg);
						opacity: 0;
					}

				}
				@-webkit-keyframes rollercoaster2 {
					0%{
						opacity: 0;
					}
					49.9%{
						opacity: 0;
					}
					50%   {
						opacity:1;
						-webkit-transform: rotate(-45deg);
					}
					58%  {
						-webkit-transform: rotate(-160deg);
					}

					70%{
						-webkit-transform: rotate(-240deg);
					}

					80%{
						-webkit-transform: rotate(-300deg);
					}

					90%{
						-webkit-transform: rotate(-340deg);
					}

					100%{
						-webkit-transform: rotate(-405deg);
					}

				}
				@-webkit-keyframes shadow {

					0%   {
						opacity:.3;
						-webkit-transform: translateX(65px) scale(0.5,0.5);
					}
					8%  {
						-webkit-transform: translateX(30px) scale(2,2);
					}

					13%{
						-webkit-transform: translateX(0px) scale(1.3,1.3);
					}

					30%{
						-webkit-transform: translateX(-15px) scale(0.5,0.5);
						opacity: 0.1;
					}
					50%{
						-webkit-transform: translateX(60px) scale(1.2,1.2);
						opacity: 0.3;
					}

					60%{
						-webkit-transform: translateX(130px) scale(2,2);
						opacity: 0.05;
					}
					65%{
						-webkit-transform: translateX(145px) scale(1.2,1.2);
					}
					80%{
						-webkit-transform: translateX(120px) scale(0.5,0.5);
						opacity: 0.1;
					}
					90%{
						-webkit-transform: translateX(80px) scale(0.8,0.8);
					}
					100%{
						-webkit-transform: translateX(60px);
						opacity: 0.3;
					}

				}
				/* No-prefix */
				@keyframes rollercoaster {
					0%    { transform: rotate(135deg);}
					8%    { transform: rotate(240deg);}
					20%   { transform: rotate(300deg);}
					40%   { transform: rotate(380deg);}
					45%   { transform: rotate(440deg);}
					50%   { transform: rotate(495deg); opacity: 1;}
					50.1% { transform: rotate(495deg); opacity: 0;}
					100%  { transform: rotate(495deg); opacity: 0;}
				}
				@keyframes rollercoaster2 {
					0%    { opacity: 0;}
					49.9% { opacity: 0;}
					50%   { opacity:1; transform: rotate(-45deg);}
					58%   { transform: rotate(-160deg);}
					70%   { transform: rotate(-240deg);}
					80%   { transform: rotate(-300deg);}
					90%   { transform: rotate(-340deg);}
					100%  { transform: rotate(-405deg);}
				}
				@keyframes shadow {
					0%   { opacity:.3; transform: translateX(65px) scale(0.5,0.5);}
					8%   { transform: translateX(30px) scale(2,2);}
					13%  { transform: translateX(0px) scale(1.3,1.3);}
					30%  { transform: translateX(-15px) scale(0.5,0.5); opacity: 0.1;}
					50%  { transform: translateX(60px) scale(1.2,1.2); opacity: 0.3;}
					60%  { transform: translateX(130px) scale(2,2); opacity: 0.05;}
					65%  { transform: translateX(145px) scale(1.2,1.2);}
					80%  { transform: translateX(120px) scale(0.5,0.5); opacity: 0.1;}
					90%  { transform: translateX(80px) scale(0.8,0.8);}
					100% { transform: translateX(60px); opacity: 0.3;}
				}
				#loader2 {
					&:after{
						-webkit-animation-delay: 0.15s;
						animation-delay: 0.15s;
					}

					.roller{
						-webkit-animation-delay: 0.15s;
						animation-delay: 0.15s;
					}
				}
				#loader3 {
					&:after{
						-webkit-animation-delay: 0.3s;
						animation-delay: 0.3s;
					}

					.roller{
						-webkit-animation-delay: 0.3s;
						animation-delay: 0.3s;
					}
				}
			}
		}
	}
</style>
<style lang="scss">

</style>
