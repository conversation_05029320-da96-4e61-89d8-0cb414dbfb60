<template>
  <div class="detail-main">
    <div class="main_operate">
      <div class="typeTree">
        <a-input-search v-model:value="state.searchValue" style="margin-bottom: 8px" @search="onTreeSearch" allow-clear :placeholder="$t('请输入')" />
        <div class="tree_tabs">
          <div
            @click="handleTreeTabChange('dataCase')"
            :class="state.activeTab == 'dataCase' ? 'activeTab' : ''"
          >
            {{ $t('算例数据') }}
          </div>
          <div
            @click="handleTreeTabChange('allTabs')"
            :class="state.activeTab == 'allTabs' ? 'activeTab' : ''"
          >
            {{ $t('仿真计算参数') }}
          </div>
        </div>
        <div class="tree_content" ref="treeBoxRef">
          <TypeTree ref="treeRef" @selectTree="selectTree"></TypeTree>
        </div>
      </div>
    </div>
    <div class="main_content">
      <div class="breadcrumb" v-show="!state.globalVisible">
        <!-- <a-tooltip>
          <template #title>{{ route.query.name.substring(4) }} / {{ treeStructure }}</template> -->
          {{ route.query.name.substring(4) }} / {{ treeStructure }}
        <!-- </a-tooltip> -->
      </div>
      <div class="timeseriesBtn" v-show="treeType == 'timeseries'">
        <span>{{ $t('时间范围') }}：{{ state.startTime }}  - {{ state.endTime }}</span>
        &nbsp;
        <span>{{ $t('间隔') }}: {{ state.data_freq == 'H' ? $t('小时') : $t('分钟') }}</span>
        &nbsp;
        <a-button @click="state.timeseriesSetVisible = true" :style="{'marginRight': '8px'}" >
          <template #icon>
            <SettingOutlined :style="{ color: '#050'}"/>
          </template>
          {{ $t('时序设置') }}
        </a-button>
        <a-button @click="checkCurve" :style="{'marginRight': '8px'}" >
          {{ $t('绑定情况检查') }}
        </a-button>
      </div>
      <div class="table_box">
        <div class="ag-grid-box">
          <AgGrid :isInputSearch="true" ref="agGridRef" :isEdit="'editor'"></AgGrid>
        </div>

        <!-- 逐项视图 -->
        <div v-if="state.itemizedViewShow" class="itemizedViewClass">
          <itemized-view ref="itemizedRef"></itemized-view>
        </div>

        <!-- 全局参数设置 -->
        <div v-if="state.globalVisible" class="globalView">
          <Global
            v-if="state.globalVisible"
            ref="globalRef"
            @confirm="handleSaveGlobal"
            @cancel="handleCloseGlobal">
          </Global>
        </div>
      </div>

    </div>
	<!-- <gis-show v-if="state.gisShow"></gis-show> -->
	<gis :mode="1" v-if="state.gisShow"></gis>
    <!-- 字段设置 -->
    <div v-if="state.fieldSetShow" class="fieldSetClass">
      <field-setting ref="FieldSettingRef"></field-setting>
    </div>
  </div>

  <!-- 平衡目标 -->
  <BalanceGoals v-if="state.balanceGoalsVisible"
    ref="balanceGoalsRef"
    v-model:open="state.balanceGoalsVisible"
    @cancel="state.balanceGoalsVisible = false"
    @confirm="handleBalanceGoals"
  />
   <!-- 时序设置 -->
   <TimeseriesSet v-if="state.timeseriesSetVisible"
    v-model:open="state.timeseriesSetVisible"
    @cancel="state.timeseriesSetVisible = false"
    @confirm="handleTimeseriesSet"
    >
  </TimeseriesSet>
  <!-- 新增时序 -->
  <time-series
    v-if="state.timeShow"
    :isItemizedView="state.itemizedViewShow"
    :treeNode="state.treeNode"
    :treeNodeId="state.treeNode_id"
    @refresh="handleTimeseriesAddRefresh"
    @close="handelTimeseriesAddClose"
  >
  </time-series>
  <!-- 装机统计 -->
  <statistic-table v-if="state.statisticShow"
    :type="state.statisticType"
    @close="state.statisticShow=false">
  </statistic-table>
  <!-- 曲线预览 / 绑定情况检查 -->
  <curve-table v-if="state.curveShow"
    ref="curveTableRef"
    :type="state.curveType"
    @close="state.curveShow=false">
  </curve-table>
  <!-- 算例拆分 -->
  <case-split v-if="state.splitShow"
    ref="caseSplitRef"
    @cancel="state.splitShow=false"
    @confirm="handleSplitCase">
  </case-split>

</template>
<script>
export default {
	name: 'detail'
}
</script>
<script setup>
import Mitt from '@/utils/mitt.js'
import { storeToRefs } from 'pinia'
import { ref, reactive, onMounted, nextTick, onUnmounted, watch } from 'vue'
import { Modal } from 'ant-design-vue'
import message from '@/utils/message'
import { basicApi, exportXl, importXl, getBaseDataApi } from '@/api/exampleApi'
// import { getBaseDataApi, getModifyDataApi } from '@/api/exampleApi'
// import { timeseriesColumns, splitGroup, hoursTitle, getCurveType, fileBlobFun } from '@/utils/common.js'
import { downloadApiFile } from '@/utils/common.js'
import { useRoute } from 'vue-router'
import { loadingStore } from '@/store/loadingStore'
import { routeStore } from '@/store/routeStore'
// import ActionBar from './components/actionBar.vue'
import TypeTree from './components/typeTree.vue'
import Global from './components/global.vue'
import TimeseriesSet from './components/TimeseriesSet.vue'
import Gis from '@/views/layout/gis.vue'
import { t } from '@/utils/common'

const route = useRoute()
const storeLoading = loadingStore()
const storeRoute = routeStore()
const { activeKey, routeTabs } = storeToRefs(storeRoute)

// AG-Grid表格
const agGridRef = ref(null)
const treeRef = ref()
const itemizedRef = ref()
const balanceGoalsRef = ref()
const curveTableRef = ref()

const treeBoxRef = ref()

// 全局参数ref
const globalRef = ref('')

const treeStructure = ref('')

const state = reactive({
	routePath: route.fullPath,
	activeTab: 'dataCase',
	// treeHeight: 620,
	isDetail: false,
	searchValue: null,
	globalVisible: false, // 全局参数设置
	timeseriesSetVisible: false, // 时序设置
	itemizedViewShow: false, // 逐项视图
	fieldSetShow: false, // 字段设置
	gisShow: false, // gis
	ShowResultModal: false,
	istimeseriesAdd: false, // 判断时序是否为新增
	actionsMove: '',
	areasOptions: [],
	dataTypeOptions: [],
	statisticShow: false,
	curveShow: false,
	splitShow: false, // 算例拆分
	balanceGoalsVisible: false, // 平衡目标弹框
	timeShow: false,
	treeNode: '',
	curveType: 'preview',
	treeNode_id: null,
	startTime: '',
	endTime: '',
	data_freq: 'H',
	statisticType: ''
})

// 顶部操作栏
const handleActionBar = (val) => {
	if (state.routePath !== route.fullPath) return
	const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
	// const tempTree = routeTabs.value.find(item => item.key == activeKey.value).treeNode
	if (val == 'isNewBuilt') { // 新建算例保存
		if (state.gisShow) {
			Mitt.emit('saveGis', 'saveTc')
		} else {
			if (state.itemizedViewShow) return itemizedRef.value.saveItemized('ignoreTempSave')
			Mitt.emit('handleOpenNewBuilt', 'newBuilt')
		}
	} else if (val == 'newBuiltToCalculate') { // 新建算例去计算
		// if (state.itemizedViewShow) return itemizedRef.value.saveItemized()
		// Mitt.emit('handleOpenNewBuilt', 'toCalculate')
	} else if (val == 'firstSave') {
		if (state.gisShow) {
			Mitt.emit('saveGis', 'saveTc')
		} else {
			nextTick(() => {
				agGridRef.value.saveAgtable(treeType.value, 'ignoreTempSave')
			})
		}
	} else if (val == 'save') {
		if (state.globalVisible) {
			// Mitt.emit('handleGlobalSave', 'ignoreTempSave')
			nextTick(() => {
				globalRef.value.handleGlobalSave('ignoreTempSave')
			})
		} else if (state.itemizedViewShow) {
			nextTick(() => {
				itemizedRef.value.saveItemized('ignoreTempSave')
			})
		} else if (state.gisShow) {
			Mitt.emit('saveGis', 'saveTc')
		} else {
			nextTick(() => {
				agGridRef.value.saveAgtable(treeType.value, 'ignoreTempSave')
			})
		}
	} else if (val == 'saveTemp') {
		if (!isUnsaved) return
		nextTick(() => {
			if (!state.itemizedViewShow) {
				agGridRef.value.saveAgtable(treeType.value)
			} else {
				itemizedRef.value.saveItemized()
			}
		})
	} else if (val == 'unitChange') {
		if (!isUnsaved) {
			nextTick(() => {
				agGridRef.value.getAgTableData(treeType.value)
			})
			return
		}
		nextTick(() => {
			if (!state.itemizedViewShow) {
				agGridRef.value.saveAgtable(treeType.value, 'unitChange')
			} else {
				itemizedRef.value.saveItemized()
			}
		})
	} else if (val == 'globalToSave') { // 仿真前保存到真实文件
		const tempQuery = route.query.type
		nextTick(() => {
			if (!state.itemizedViewShow) {
				if (tempQuery == 'isNewBuilt' || tempQuery == 'isResultBuilt') {
					Mitt.emit('handleOpenNewBuilt', 'newBuiltToCalculate')
				} else {
					agGridRef.value.saveAgtable(treeType.value, 'globalToSave')
				}
			} else {
				itemizedRef.value.saveItemized('globalToSave')
			}
		})
	} else if (val == 'globalToSaveTemp') { // 仿真前保存到临时文件
		nextTick(() => {
			if (!state.itemizedViewShow) {
				agGridRef.value.saveAgtable(treeType.value, 'globalToSaveTemp')
			} else {
				itemizedRef.value.saveItemized('globalToSaveTemp')
			}
		})
	} else if (val == 'saveMainTable') {
		nextTick(() => {
			agGridRef.value.saveAgtable(treeType.value, 'uploadCaseFile')
		})
	} else if (val == 'xlsx' || val == 'tc' || val == 'xml' || val == 'yml' || val == 'v2Xlsx') { // 另存为
		fileExport(val)
	} else if (val == 'long_term' || val == 'mid_term' || val == 'capacity_balance' || val == 'short_term') {
		handleTreeTabChange(val)
		// 计算 (协同规划、电力电量平衡、时序运行模拟)
		// state.globalVisible = true
		// nextTick(() => {
		// 	// globalRef.value.handleTabChange(val)
		// 	Mitt.emit('handleTabChange', val)
	  // })
	} else if (val == 'reset') { // 重置
		nextTick(() => {
			agGridRef.value.getAgTableData(treeType.value)
		})
	} else if (val == 'deleteRow') { // 删除行
		nextTick(() => {
			agGridRef.value.onRemoveSelected(treeType.value)
		})
	} else if (val == 'insertRow') { // 插入行
		nextTick(() => {
			if (treeType.value == 'timeseries') {
				agGridRef.value.timeseriesAdd(false)
			} else {
				agGridRef.value.insertRow(1)
			}
		})
	} else if (val == 'revoke') { // 撤销
		nextTick(() => {
			agGridRef.value.undo()
		})
	} else if (val == 'forward') { // 恢复
		nextTick(() => {
			agGridRef.value.redo()
		})
	} else if (val == 'clearFilter') { // 清空筛选条件
		nextTick(() => {
			agGridRef.value.clearFilter()
		})
	} else if (val == 'paramsReplen') { // 参数补充
		if (state.itemizedViewShow) return message.warning(t('请在算例数据页面操作！'))
		nextTick(() => {
			if (isUnsaved) {
				agGridRef.value.saveAgtable(treeType.value, 'paramsReplen')
			} else {
				agGridRef.value.handleParamsReplen(treeType.value)
			}
		})
	} else if (val == 'dataReplace') { // 替换
		nextTick(() => {
			agGridRef.value.openReplace()
		})
	} else if (val == 'dataHandle') { // 计算
		nextTick(() => {
			agGridRef.value.onCount()
		})
	} else if (val == 'tableUpload') { // 当前表格导入
		// if (state.globalVisible || state.itemizedViewShow || state.fieldSetShow) return message.warning('请在算例数据页面操作！')
		// importXls()
	} else if (val == 'tableDownload') { // 当前表格导出
		if (state.globalVisible || state.itemizedViewShow || state.fieldSetShow) return message.warning(t('请在算例数据页面操作！'))
		nextTick(() => {
			if (isUnsaved) {
				agGridRef.value.saveAgtable(treeType.value, 'tableDownload')
			} else {
				exportXls()
			}
		})
	} else if (val == 'typeChange') { // 智能关联
		nextTick(() => {
			agGridRef.value.handleTypeChange()
		})
	} else if (val == 'timeMatch') { // 智能关联
		nextTick(() => {
			agGridRef.value.handleTimeMatch()
		})
	} else if (val == 'fieldSet') { // 字段设置
		state.fieldSetShow = !state.fieldSetShow
	} else if (val == 'addCurve') { // 新增曲线
		state.timeShow = true
	} else if (val == 'itemizedView') { // 逐项视图
		// if (treeType.value == 'timeseries') return state.itemizedViewShow = false
		nextTick(() => {
			if (state.itemizedViewShow || treeType.value == 'timeseries') {
				state.itemizedViewShow = false
				Mitt.emit('onSelectTreeChange', treeType.value)
				agGridRef.value.getAgTableData(treeType.value)
				return
			}
			const agselectArr = agGridRef.value.getSelected()
			handleItemizedDel(agselectArr)
		})
	} else if (val == 'balanceGoals') { // 平衡目标
		state.balanceGoalsVisible = true
		nextTick(() => {
			balanceGoalsRef.value.getBalanceGoalsData(treeType.value)
	  })
	} else if (val == 'copyData') {
		if (state.itemizedViewShow || state.globalVisible || state.fieldSetShow) return message.warning(t('请在算例数据页面操作！')) // 逐项视图、全局参数设置、字段设置 下不允许复制
		nextTick(() => {
			if (isUnsaved) {
				agGridRef.value.saveAgtable(treeType.value, 'copyData')
			} else {
				handleCopyData()
			}
		})
	} else if (val == 'pasteData') {
		if (state.itemizedViewShow || state.globalVisible || state.fieldSetShow) return message.warning(t('请在算例数据页面操作！')) // 逐项视图、全局参数设置、字段设置 下不允许粘贴
		nextTick(() => {
			if (isUnsaved) {
				agGridRef.value.saveAgtable(treeType.value, 'pasteData')
			} else {
				handlePasteData()
			}
		})
	} else if (val == 'cutData') {
		const agselectArr = agGridRef.value.getSelected()
		handleCuteData(agselectArr)
	} else if (val == 'zhuangjiStatistic' || val == 'capacityStatistic') { // 装机统计
		state.statisticType = val
		nextTick(() => {
			if (isUnsaved) {
				agGridRef.value.saveAgtable(treeType.value, 'statistic')
			} else {
				state.statisticShow = true
			}
		})
	} else if (val == 'curvePreview') { // 曲线预览
		if (treeType.value == 'timeseries') return
		if (!state.itemizedViewShow) {
			if (isUnsaved) {
				agGridRef.value.saveAgtable(treeType.value, 'curvePreview')
			} else {
				handleCurve()
			}
		} else {
			// Mitt.emit('openCurvePreview')
			nextTick(() => {
				itemizedRef.value.openCurvePreview()
			})
		}
	} else if (val == 'statistic') { // 统计信息
		// const agselectArr = agGridRef.value.getSelected()
		// handleCuteData(agselectArr)
		state.statisticShow = true
	} else if (val == 'caseSplit') {
		state.splitShow = true
		// handleSplitCase(val)
	} else if (val == 'showGis') {
		if (isUnsaved) {
			nextTick(() => {
				agGridRef.value.saveAgtable(treeType.value, 'showGis')
			})
		} else {
			state.gisShow = true
		}
	}
}
Mitt.on('handleActionBar', handleActionBar)
const showGis = () => {
	if (state.routePath !== route.fullPath) return
	if (state.gisShow) {
		Mitt.emit('saveGis', 'temp')
		// const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
		// if (isUnsaved) {
		// 	basicApi({
		// 		'import_string_func': 'teapgis:check_tg_in_tc_file',
		// 		'func_arg_dict': {
		// 			'tc_file_path': route.query.filePath
		// 		}
		// 	}).then(res => {
		// 		nextTick(() => {
		// 			agGridRef.value.getAgTableData(treeType.value)
		// 			treeRef.value.getTreeMenuList()
		// 		})
		// 	})
		// } else {
		// 	state.gisShow = false
		// }
	} else {
		handleActionBar('showGis')
	}
}
const closeGisShow = (val) => {
	if (state.routePath !== route.fullPath) return
	if (val) {
		nextTick(() => {
			agGridRef.value.getAgTableData(treeType.value)
			treeRef.value.getTreeMenuList()
			state.gisShow = false
		})
	} else {
		state.gisShow = false
	}
}
Mitt.on('closeGisShow', closeGisShow)
const gisReadyShow = () => {
	if (state.routePath !== route.fullPath) return
	state.gisShow = true
}
Mitt.on('gisReadyShow', gisReadyShow)
Mitt.on('showGis', showGis)
const saveGisFinished = () => {
	if (state.routePath !== route.fullPath) return
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
		treeRef.value.getTreeMenuList()
	})
}
Mitt.on('saveGisFinished', saveGisFinished)
const handleTreeTabChange = (val) => {
	if (state.routePath !== route.fullPath) return
	if (val == 'dataCase') {
		// 算例数据
		state.globalVisible = false
		state.activeTab = 'dataCase'
	} else {
		// 仿真计算 (协同规划、电力电量平衡、时序运行模拟)
		state.activeTab = 'allTabs'
		state.globalVisible = true
		nextTick(() => {
			// globalRef.value.handleTabChange(val)
			Mitt.emit('handleTabChange', val)
	  })
	}
	treeRef.value.getTreeTabChange(val)
}
Mitt.on('handleTreeTabChange', handleTreeTabChange)

const onTreeSearch = (val) => {
	treeRef.value.onSearch(val)
}
// 保存并关闭
const handleSaveAndClose = (val) => {
	if (state.routePath !== route.fullPath) return
	const tempTree = routeTabs.value.find(item => item.key == activeKey.value).treeNode
	nextTick(() => {
		if (!state.itemizedViewShow) {
			agGridRef.value.saveAgtable(tempTree, 'saveAndClose', val)
		} else {
			itemizedRef.value.saveItemized('ignoreTempSave')
		}
	})
}
Mitt.on('handleSaveAndClose', handleSaveAndClose)

const handleStatistic = (val) => {
	if (state.routePath !== route.fullPath) return
	nextTick(() => {
		state.statisticShow = true
	})
}
Mitt.on('handleStatistic', handleStatistic)

const treeChangesave = (val) => {
	if (state.routePath !== route.fullPath) return
	nextTick(() => {
		agGridRef.value.handleSaveAgtable(val)
	})
}
Mitt.on('treeChangesave', treeChangesave)

const toDetailSave = (val) => {
	if (state.routePath !== route.fullPath) return
	console.log('toDetailSave', val)
	nextTick(() => {
		agGridRef.value.handleSaveAgtable(val)
	})
}
Mitt.on('toDetailSave', toDetailSave)

const treeChangeSaveItemized = (val) => {
	if (state.routePath !== route.fullPath) return
	itemizedRef.value.saveItemized(val)
}
Mitt.on('treeChangeSaveItemized', treeChangeSaveItemized)

const handleCloseModal = () => {
	if (state.routePath !== route.fullPath) return
	state.globalVisible = false
	state.itemizedViewShow = false
}
Mitt.on('handleCloseModal', handleCloseModal)

// 点击最里层树节点
const handleItemizedView = (val) => {
	if (state.routePath !== route.fullPath) return
	state.itemizedViewShow = true
	// Mitt.emit('handleItemizedIcon', true)
	const tempArr = val.split('-')
	state.treeNode = tempArr[0]
	state.treeNode_id = tempArr[1]
	nextTick(() => {
		itemizedRef.value.deviceRowClick(tempArr[0], tempArr[1])
	})
}
Mitt.on('handleItemizedView', handleItemizedView)

const handleOverView = (value) => {
	if (state.routePath !== route.fullPath) return
	state.itemizedViewShow = false
}
Mitt.on('handleOverView', handleOverView)

const handleItemizedDel = (data) => {
	if (treeType.value.includes('integrated')) return message.warning(t('一体化电站暂不支持此功能！'))
	if (data.length <= 0) {
		return message.warning(t('请选择一条数据！'))
	} else {
		const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
		if (data[0].index < 0) return
		if (isUnsaved) {
			treeChangesave({
				type: 'treeChange',
				treeNode_new: treeType.value + '-' + data[0].index
			})
		} else {
			state.itemizedViewShow = true
			Mitt.emit('onSelectTreeChange', treeType.value + '-' + data[0].index)
		}
	}
}

// 时序引用跳转逐项视图
const handleItemizedDelQuote = (data) => {
	if (data.relation_type.includes('integrated')) return message.warning(t('一体化电站暂不支持此功能！'))
	const tempTreeNode = data.relation_type + '-' + data.relation_index
	state.itemizedViewShow = true
	nextTick(() => {
		itemizedRef.value.deviceRowClick(data.relation_type, data.relation_index)
	})
	Mitt.emit('onSelectTreeChange', tempTreeNode)
}
Mitt.on('handleItemizedDelQuote', handleItemizedDelQuote)

// 复制
const handleCopyData = (data) => {
	nextTick(() => {
		const data = agGridRef.value.getSelected()
		if (data.length <= 0) {
			agGridRef.value.onCopyClick()
			sessionStorage.removeItem('fullPath')
		} else {
			const idList = data.map(item => item.index)
			sessionStorage.setItem('fullPath', route.fullPath)
			sessionStorage.setItem('filePath', route.query.filePath)
			sessionStorage.setItem('copyTreeNode', treeType.value)
			sessionStorage.setItem('ids', JSON.stringify(idList))
			message.success(t('复制成功！'))
		}
	})
}
Mitt.on('handleCopyData', handleCopyData)

// 粘贴
const handlePasteData = (data) => {
	const fullPath = sessionStorage.getItem('fullPath')
	const filePath = sessionStorage.getItem('filePath')
	const copyTreeNode = sessionStorage.getItem('copyTreeNode')
	const idList = JSON.parse(sessionStorage.getItem('ids'))
	if (!fullPath) return agGridRef.value.onPasteClick()
	// if (copyTreeNode !== treeType.value) return message.warning('不同类型设备无法粘贴！')
	if (treeType.value !== copyTreeNode) {
		Modal.confirm({
			title: t('注意'),
			content: t('设备类别不一致，是否强制粘贴'),
			okText: t('粘贴'),
			cancelText: t('取消'),
			onOk() {
				toPaste(fullPath, filePath, copyTreeNode, idList)
			},
			onCancel() {
			}
		})
	} else {
		toPaste(fullPath, filePath, copyTreeNode, idList)
	}
}

const toPaste = (fullPath, filePath, copyTreeNode, idList) => {
	if (route.fullPath == fullPath) {
		basicApi({
			'import_string_func': 'teapcase:duplicates_rows_in_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': copyTreeNode,
				'target_sheet_name': treeType.value,
				'selected_row_id_list': idList
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				agGridRef.value.getAgTableData(treeType.value)
				Mitt.emit('getTreeMenuList')
				message.success(t('已粘贴至表末端！'))
			}
		})
	} else {
		basicApi({
			'import_string_func': 'teapcase:copy_rows_from_tc_to_tc',
			'func_arg_dict': {
				'src_file_name': filePath,
				'dest_file_name': route.query.filePath,
				'sheet_name': copyTreeNode,
				'target_sheet_name': treeType.value,
				'selected_row_id_list': idList
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				agGridRef.value.getAgTableData(treeType.value)
				Mitt.emit('getTreeMenuList')
				message.success(t('已粘贴至表末端！'))
			}
		})
	}
}
Mitt.on('handlePasteData', handlePasteData)

// 剪切
const handleCuteData = (data) => {
	if (state.itemizedViewShow || state.globalVisible || state.fieldSetShow) return // 逐项视图、全局参数设置、字段设置 下不允许复制
	if (data.length <= 0) {
		nextTick(() => {
			agGridRef.value.onCutClick()
		})
		sessionStorage.removeItem('fullPath')
	} else {
		const idList = data.map(item => item.index)
		sessionStorage.setItem('fullPath', route.fullPath)
		sessionStorage.setItem('filePath', route.query.filePath)
		sessionStorage.setItem('ids', JSON.stringify(idList))
		nextTick(() => {
			agGridRef.value.onRemoveSelected(treeType.value)
		})
		message.success(t('剪切成功！'))
		// agGridRef.value.saveAgtable(treeType.value)
	}
}
// 字段设置
const handleCloseFieldSetting = (val) => {
	state.fieldSetShow = false
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
		treeRef.value.getTreeMenuList()
	})
}
Mitt.on('handleCloseFieldSetting', handleCloseFieldSetting)
const handleUpdateFieldSetting = (val) => {
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
		treeRef.value.getTreeMenuList()
	})
}
Mitt.on('handleUpdateFieldSetting', handleUpdateFieldSetting)

// 时序新增刷新
const handleTimeseriesAddRefresh = (val) => {
	state.timeShow = false
	nextTick(() => {
		if (state.itemizedViewShow) {
			itemizedRef.value.deviceRowClick(state.treeNode, state.treeNode_id)
			Mitt.emit('getTreeMenuList')
		} else {
			agGridRef.value.getAgTableData()
			Mitt.emit('getTreeMenuList')
		}
	})
}

const handelTimeseriesAddClose = () => {
	if (state.routePath !== route.fullPath) return
	state.timeShow = false
}

// 表格交互 导入
const importXls = (file) => {
	if (state.routePath !== route.fullPath) return
	if (state.globalVisible || state.itemizedViewShow || state.fieldSetShow) return message.warning(t('请在算例数据页面操作！'))
	const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
	state.importFile = file
	nextTick(() => {
		if (isUnsaved) {
			agGridRef.value.saveAgtable(treeType.value, 'tableUpload')
		} else {
			handleImportFile()
		}
	})
}
Mitt.on('importXls', importXls)

const handleImportFile = () => {
	const formdata = new FormData()
	formdata.append('tc_filename', route.query.filePath)
	formdata.append('sheet_name', treeType.value)
	formdata.append('file', state.importFile[0].originFileObj)

	importXl(formdata, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1) {
			message.success(res.message || t('导入成功'))
			agGridRef.value.getAgTableData(treeType.value)
			treeRef.value.getTreeMenuList()
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}
Mitt.on('handleImportFile', handleImportFile)

// 表格交互 下载
const exportXls = (fileType) => {
	if (state.routePath !== route.fullPath) return
	if (!route.query.filePath) return message.warning(t('请先项目导入'))
	const formdata = new FormData()
	formdata.append('tc_filename', route.query.filePath)
	formdata.append('sheet_name', treeType.value)
	exportXl(formdata, true).then(res => {
		downloadApiFile(res)
		storeLoading.hiddenModal()
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}
Mitt.on('exportXls', exportXls)

// 仿真计算打开时的另存为
const handleGlobalSaveas = (val) => {
	if (val == 'ignoreTempSave') {
		nextTick(() => {
			agGridRef.value.saveAgtable(treeType.value, 'ignoreTempSave')
		})
	} else {
		nextTick(() => {
			agGridRef.value.saveAgtable(treeType.value, val.type, val.fileType)
		})
	}
}
Mitt.on('handleGlobalSaveas', handleGlobalSaveas)

// 另存为 tc、excel、yml、v2版本xlsx
const fileExport = (fileType) => {
	if (!route.query.filePath) return message.warning(t('请先项目导入'))
	// const filename = route.query.name.substr(4)

	nextTick(() => {
		if (state.globalVisible) {
			// Mitt.emit('handleGlobalSave', {
			// 	type: 'saveAs',
			// 	fileType
			// })

			globalRef.value.handleGlobalSave({
				type: 'saveAs',
				fileType
			})
			return
		}
		if (!state.itemizedViewShow) {
			agGridRef.value.saveAgtable(treeType.value, 'saveAs', fileType)
		} else {
			itemizedRef.value.saveItemized('saveAs', {
				type: 'saveAs',
				fileType
			})
		}
	})
}

// 左侧树选择
/**
  1.母线（ bus ）
  2.发电机（ gen ）
  3.负荷（ load ）
  4.风电（ wind )
  5.光伏（ solar ）
  6.区外来电（ feedin ）
  7.抽蓄机组或储能电池换流器（ stogen ）
  8.抽蓄电站水库或储能电池（ storage ）
  9.水电机组（ hydropower ）
  10.水电站水库（ reservoir ）
  11.线路（ line ）
  12.主变（ trafo ）
  13.断面（ interface ）
  14.时序（ timeseries ）
 */
const treeType = ref('bus')
const selectTree = (val, treeName, status) => {
	treeType.value = val
	treeStructure.value = treeName
	// sessionStorage.setItem('treeType', val)
	nextTick(() => {
		agGridRef.value.getAgTableData(val)
	})

	Mitt.emit('setTreeNode', val)
}

// 全局参数
const handleSaveGlobal = () => {
	state.globalVisible = false
}
const handleCloseGlobal = (val) => {
	state.globalVisible = false
	handleTreeTabChange('dataCase')
}

// 逐项视图 单条保存成功
const updateOneRow = () => {
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
	})
}
Mitt.on('updateOneRow', updateOneRow)

// 全局参数设置
const getGlobalParameters = (parameter) => {
	if (state.routePath !== route.fullPath) return
	state.startTime = parameter.start_datetime.split(' ')[0]
	state.endTime = parameter.end_datetime.split(' ')[0]
	state.data_freq = parameter.data_freq
}
Mitt.on('getGlobalParameters', getGlobalParameters)

// 检修生成 刷新
const handleRefresh = () => {
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
		if (state.itemizedViewShow) {
			itemizedRef.value.deviceRowClick(state.treeNode, state.treeNode_id)
		}
	})
}
Mitt.on('handleRefresh', handleRefresh)

// 曲线预览
const handleCurve = () => {
	getBaseDataApi(
		{
			'import_string_func': 'teapcase:read_relation_ts_from_tc',
			'func_arg_dict': {
				'sheet_name': treeType.value,
				'file_name': route.query.filePath
			}
		}
	).then(res => {
		if (res.code == 1) {
			state.curveType = 'preview'
			state.curveShow = true
			const { func_result } = res

			nextTick(() => {
				curveTableRef.value.getInitData(func_result.timeseries)
			})
		}
	}).catch(err => {
		console.log(err)
	})
}

// 时序绑定情况检查
const checkCurve = () => {
	getBaseDataApi({
		'import_string_func': 'teapcase:read_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath, // # 要带扩展名.h5
			'sheet_name': treeType.value // # 此参数可以不传，也可以为空列表，两种情况都会返回所有表
		}
	}).then(res => {
		if (res.code == 1) {
			state.curveType = 'check'
			state.curveShow = true
			const { func_result } = res
			nextTick(() => {
				curveTableRef.value.getInitData(func_result.timeseries)
			})
		}
	}).catch(err => {
		console.log(err)
	})
}

// 算例拆分
const handleSplitCase = (val) => {
	state.splitShow = false
	Mitt.emit('getSplitCase', val)
}

const handleTimeseriesSet = () => {
	state.timeseriesSetVisible = false
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
	})
}

// 透视图修改刷新
const handleBalanceGoals = () => {
	nextTick(() => {
		agGridRef.value.getAgTableData(treeType.value)
	})
}

// 监听多个响应式数据的变化 state.globalVisible || state.itemizedViewShow || state.fieldSetShow
watch(() => [state.globalVisible, state.itemizedViewShow, state.fieldSetShow], (newValues, oldValues) => {
	newValues.includes(true) ? storeRoute.setTabsModalVisible(route.fullPath, true) : storeRoute.setTabsModalVisible(route.fullPath, false)
})

defineExpose({ handleActionBar })

onUnmounted(() => {

})

const screenScale = (val) => {
	if (document.querySelector('.ag-grid-box').style) document.querySelector('.ag-grid-box').style.zoom = window.innerWidth <= 1200 ? 1920 / 1200 : 1920 / window.innerWidth
}

// window.addEventListener('resize', screenScale)
window.addEventListener('resize', function() {
	screenScale()
	// state.treeHeight = treeBoxRef.value.clientHeight
})

onMounted(() => {
	screenScale()
	const tempTree = routeTabs.value.find(item => item.key == activeKey.value).treeNode
	if (tempTree) {
		treeType.value = tempTree
		sessionStorage.setItem('treeType', tempTree)
	} else if (sessionStorage.getItem('treeType')) {
		treeType.value = sessionStorage.getItem('treeType')
	} else {
		treeType.value = 'bus'
		sessionStorage.setItem('treeType', 'bus')
	}
	agGridRef.value.getAgTableData(treeType.value)
})

</script>

<style lang="scss" scoped>
  .detail-main {
    width: 100%;
    height: 100%;
    padding: 0 15px 2px 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    // display: grid;
    // grid-template-columns: 2fr 8fr;
    // grid-column-gap: 20px;
    background-color: var(--theme-bg-color);
    position: relative;

    .fieldSetClass {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index:  100;
    }
    .main-actions {
      width: 100%;
      height: 50px;
      display: flex;
      font-weight: 500;
      color: #6d77ad;
      border-bottom: 1px solid #d4d6d1;
      div {
        height: 40px;
        margin: 5px 10px;
        padding: 2px;
        text-align: center;
        // font-size: 12px;
        @include add-size(12px, $size);
        img {
          height: 16px;
          width: 16px;
        }
      }
      .bgShadow {
        border-radius: 4px;
        background-color: #E0E2EB;
      }
    }

    .main_operate {
      width: 353px;
      height: 100%;
      padding-top: 4px;
      // background-color: #F8F8F8;
      background-color: var(--theme-bg-color);
      .typeTree {
        width: 100%;
        height: 100%;
        background-color: var(--theme-bg-color);
        .tree_tabs {
          display: flex;
          // padding: 2px 4px 0 4px;
          height: 36px;
          padding: 0 7px 0 7px;
          // box-sizing: border-box;
          border: 1px solid #A2B5CC;
          border-radius: 5px 5px 0 0;
          background: #E0E2E3;
          background: rgba(2, 84, 159, 0.1608);
          // font-size: 15px;
          @include add-size(15px, $size);
          font-weight: normal;
          color: #474747;
          position: sticky;
          top: 0;
          z-index: 10;
          div {
            width: 50%;
            height: 36px;
            margin-top: 5px;
            border-radius: 4px 4px 0 0;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
          }
          .activeTab {
            height: 30px;
            border-radius: 5px 5px 0 0;
            background: linear-gradient(180deg, #7AAAE2 -3%, #C8D4E2 3%, #DFE3E9 62%, #F4F4F4 100%);
            box-sizing: border-box;
            border: 1px solid #ACCCEA;
            border-bottom: 0;
          }
        }
        .tree_content {
          width: 100%;
          height: calc(100% - 75px);
          // margin-top: 2px;
          padding: 5px 10px;
          // border: 1px solid #e6e9f0;
          border: 1px solid #A2B5CC;
          border-top: 0;
          border-radius: 0 0 5px 5px;
          box-sizing: border-box;
          // overflow: auto; // 超出这个最大高度的数据，会被隐藏起来，上下滑动
        }
      }
    }

    .main_content {
      width: calc(100% - 376px);
      height: 100%;
      position: relative;
      .breadcrumb{
        position: absolute;
        z-index: 30;
        @include add-size(14px, $size);
        color: #9B9EA8 ;
        line-height: 30px;
        width: 50%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .timeseriesBtn {
        position: absolute;
        right: 360px;
        top: 1.5px;
        z-index: 30;
		button{
			padding: 2px 10px;
			height: 28px;
		}
      }
      .table_box {
        position: relative;
        width: 100%;
        height: 100%;
        // background-color: #4a8df0;
        border-radius: 5px;
        overflow-x: hidden;
        overflow-y: hidden;
        .ag-grid-box {
          width: 100%;
          height: 100%;
        }
      }
      .itemizedViewClass {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        z-index: 30;
        background-color: var(--theme-bg-color);
      }
      .globalView {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 4px;
        right: 0;
        z-index: 30;
        background-color: var(--theme-bg-color);
      }

    }
    .main_display {
      width: 14px;
      height: 98%;
      background-color: #AABBD9;
      color: #fff;
      text-align: center;
      line-height: 80vh;
      border-radius: 4px;
    }
    .main_hidden {
      width: 14px;
      height: 98%;
      color: #fff;
      text-align: center;
      line-height: 80vh;
      border-radius: 4px;
    }

  }
  :deep(.ant-tabs .ant-tabs-tab)  {
    line-height: 4px;
    padding: 12px 15px;
  }

  :deep(.ant-modal .ant-modal-content) {
    padding: 15px !important;
  }

</style>
