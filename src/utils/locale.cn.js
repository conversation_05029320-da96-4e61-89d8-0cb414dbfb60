// Example locale file for English, give this to your locale team to translate

export const AG_GRID_LOCALE_ZH = {
	// Set Filter
	selectAll: '(全选)',
	selectAllSearchResults: '(选择所有搜索结果)',
	addCurrentSelectionToFilter: '将当前选择添加到过滤器',
	searchOoo: '搜索...',
	blanks: '(空白)',
	noMatches: '没有匹配项',

	// Number Filter & Text Filter
	filterOoo: '过滤...',
	equals: '等于',
	notEqual: '不等于',
	blank: '空白',
	notBlank: '非空白',
	empty: '请选择一个',

	// Number Filter
	lessThan: '小于',
	greaterThan: '大于',
	lessThanOrEqual: '小于或等于',
	greaterThanOrEqual: '大于或等于',
	inRange: '在...之间',
	inRangeStart: '从',
	inRangeEnd: '到',

	// Text Filter
	contains: '包含',
	notContains: '不包含',
	startsWith: '开始于',
	endsWith: '结束于',

	// Date Filter
	dateFormatOoo: 'yyyy-mm-dd',
	before: '之前',
	after: '之后',

	// Filter Conditions
	andCondition: '和',
	orCondition: '或',

	// Filter Buttons
	applyFilter: '应用',
	resetFilter: '重置',
	clearFilter: '清除',
	cancelFilter: '取消',

	// Filter Titles
	textFilter: '文本过滤器',
	numberFilter: '数字过滤器',
	dateFilter: '日期过滤器',
	setFilter: '设置过滤器',

	// Group Column Filter
	groupFilterSelect: '选择字段:',

	// Advanced Filter
	advancedFilterContains: '包含',
	advancedFilterNotContains: '不包含',
	advancedFilterTextEquals: '等于',
	advancedFilterTextNotEqual: '不等于',
	advancedFilterStartsWith: '开始于',
	advancedFilterEndsWith: '结束于',
	advancedFilterBlank: '为空',
	advancedFilterNotBlank: '不为空',
	advancedFilterEquals: '=',
	advancedFilterNotEqual: '!=',
	advancedFilterGreaterThan: '>',
	advancedFilterGreaterThanOrEqual: '>=',
	advancedFilterLessThan: '<',
	advancedFilterLessThanOrEqual: '<=',
	advancedFilterTrue: '为真',
	advancedFilterFalse: '为假',
	advancedFilterAnd: '和',
	advancedFilterOr: '或',
	advancedFilterApply: '应用',
	advancedFilterBuilder: '构建器',
	advancedFilterValidationMissingColumn: '缺少列',
	advancedFilterValidationMissingOption: '缺少选项',
	advancedFilterValidationMissingValue: '缺少值',
	advancedFilterValidationInvalidColumn: '列未找到',
	advancedFilterValidationInvalidOption: '选项未找到',
	advancedFilterValidationMissingQuote: '值缺少结束引号',
	advancedFilterValidationNotANumber: '值不是数字',
	advancedFilterValidationInvalidDate: '值不是有效日期',
	advancedFilterValidationMissingCondition: '缺少条件',
	advancedFilterValidationJoinOperatorMismatch: '条件内的连接运算符必须相同',
	advancedFilterValidationInvalidJoinOperator: '找不到连接运算符',
	advancedFilterValidationMissingEndBracket: '缺少结束括号',
	advancedFilterValidationExtraEndBracket: '结束括号过多',
	advancedFilterValidationMessage: '表达式有错误。${variable} - ${variable}。',
	advancedFilterValidationMessageAtEnd: '表达式有错误。表达式结束处的${variable}。',
	advancedFilterBuilderTitle: '高级过滤器',
	advancedFilterBuilderApply: '应用',
	advancedFilterBuilderCancel: '取消',
	advancedFilterBuilderAddButtonTooltip: '添加过滤器或组',
	advancedFilterBuilderRemoveButtonTooltip: '移除',
	advancedFilterBuilderMoveUpButtonTooltip: '上移',
	advancedFilterBuilderMoveDownButtonTooltip: '下移',
	advancedFilterBuilderAddJoin: '添加组',
	advancedFilterBuilderAddCondition: '添加过滤器',
	advancedFilterBuilderSelectColumn: '选择一列',
	advancedFilterBuilderSelectOption: '选择一个选项',
	advancedFilterBuilderEnterValue: '输入一个值...',
	advancedFilterBuilderValidationAlreadyApplied: '当前过滤器已应用。',
	advancedFilterBuilderValidationIncomplete: '并非所有条件都已完成。',
	advancedFilterBuilderValidationSelectColumn: '必须选择一列。',
	advancedFilterBuilderValidationSelectOption: '必须选择一个选项。',
	advancedFilterBuilderValidationEnterValue: '必须输入一个值。',

	// Side Bar
	columns: '列',
	filters: '过滤器',

	// columns tool panel
	pivotMode: '数据透视模式',
	groups: '行组',
	rowGroupColumnsEmptyMessage: '拖动到此处以设置行组',
	values: '值',
	valueColumnsEmptyMessage: '拖动到此处以聚合',
	pivots: '列标签',
	pivotColumnsEmptyMessage: '拖动到此处以设置列标签',

	// Header of the Default Group Column
	group: '组',

	// Row Drag
	rowDragRow: '行',
	rowDragRows: '行',

	// Other
	loadingOoo: '加载中...',
	loadingError: '加载错误',
	noRowsToShow: '没有数据',
	enabled: '启用',

	// Menu
	pinColumn: '固定列',
	pinLeft: '左侧固定',
	pinRight: '右侧固定',
	noPin: '无固定',
	valueAggregation: '值聚合',
	noAggregation: '无',
	autosizeThiscolumn: '自动调整此列大小',
	autosizeAllColumns: '自动调整所有列大小',
	groupBy: '按此分组',
	ungroupBy: '取消按此分组',
	ungroupAll: '取消所有分组',
	addToValues: '添加 ${variable} 到值',
	removeFromValues: '从值中移除 ${variable}',
	addToLabels: '添加 ${variable} 到标签',
	removeFromLabels: '从标签中移除 ${variable}',
	resetColumns: '重置列',
	expandAll: '展开所有行组',
	collapseAll: '关闭所有行组',
	copy: '复制',
	ctrlC: 'Ctrl+C',
	ctrlX: 'Ctrl+X',
	copyWithHeaders: '连同标题复制',
	copyWithGroupHeaders: '连同组标题复制',
	cut: '剪切',
	paste: '粘贴',
	ctrlV: 'Ctrl+V',
	export: '导出',
	csvExport: '导出为 CSV',
	excelExport: '导出为 Excel',

	// Enterprise Menu Aggregation and Status Bar
	sum: '总和',
	first: '第一',
	last: '最后',
	min: '最小',
	max: '最大',
	none: '无',
	count: '计数',
	avg: '平均',
	filteredRows: '已过滤行',
	selectedRows: '已选行',
	totalRows: '总行数',
	totalAndFilteredRows: '行',
	more: '更多',
	to: '到',
	of: '的',
	page: '页',
	pageLastRowUnknown: '?',
	nextPage: '下一页',
	lastPage: '最后一页',
	firstPage: '第一页',
	previousPage: '上一页',
	pageSizeSelectorLabel: '每页行数:',
	footerTotal: '总计',

	// Pivoting
	pivotColumnGroupTotals: '总计',

	// Enterprise Menu (Charts)
	pivotChartAndPivotMode: '数据透视图 & 数据透视模式',
	pivotChart: '数据透视图',
	chartRange: '图表范围',

	columnChart: '柱状图',
	groupedColumn: '分组',
	stackedColumn: '堆叠',
	normalizedColumn: '100% 堆叠',

	barChart: '条形图',
	groupedBar: '分组',
	stackedBar: '堆叠',
	normalizedBar: '100% 堆叠',

	pieChart: '饼图',
	pie: '饼图',
	doughnut: '甜甜圈图',

	line: '线图',

	xyChart: 'X Y (散点图)',
	scatter: '散点图',
	bubble: '气泡图',

	areaChart: '面积图',
	area: '面积',
	stackedArea: '堆叠',
	normalizedArea: '100% 堆叠',

	histogramChart: '直方图',
	histogramFrequency: '频率',

	combinationChart: '组合图',
	columnLineCombo: '柱状图 & 线图',
	AreaColumnCombo: '面积图 & 柱状图',

	// Charts
	pivotChartTitle: '数据透视图',
	rangeChartTitle: '范围图',
	settings: '设置',
	data: '数据',
	format: '格式',
	categories: '类别',
	defaultCategory: '(无)',
	series: '系列',
	xyValues: 'X Y 值',
	paired: '配对模式',
	axis: '轴',
	navigator: '导航器',
	color: '颜色',
	thickness: '厚度',
	xType: 'X 类型',
	automatic: '自动',
	category: '类别',
	number: '数字',
	time: '时间',
	autoRotate: '自动旋转',
	xRotation: 'X 旋转',
	yRotation: 'Y 旋转',
	ticks: '刻度',
	width: '宽度',
	height: '高度',
	length: '长度',
	padding: '填充',
	spacing: '间距',
	chart: '图表',
	title: '标题',
	titlePlaceholder: '图表标题 - 双击以编辑',
	background: '背景',
	font: '字体',
	top: '顶部',
	right: '右侧',
	bottom: '底部',
	left: '左侧',
	labels: '标签',
	size: '大小',
	minSize: '最小大小',
	maxSize: '最大大小',
	legend: '图例',
	position: '位置',
	markerSize: '标记大小',
	markerStroke: '标记描边',
	markerPadding: '标记填充',
	itemSpacing: '项目间距',
	itemPaddingX: '项目填充 X',
	itemPaddingY: '项目填充 Y',
	layoutHorizontalSpacing: '水平间距',
	layoutVerticalSpacing: '垂直间距',
	strokeWidth: '描边宽度',
	lineDash: '虚线',
	offset: '偏移',
	offsets: '偏移',
	tooltips: '工具提示',
	callout: '标注',
	markers: '标记',
	shadow: '阴影',
	blur: '模糊',
	xOffset: 'X 偏移',
	yOffset: 'Y 偏移',
	lineWidth: '线宽',
	normal: '正常',
	bold: '粗体',
	italic: '斜体',
	boldItalic: '粗斜体',
	predefined: '预定义',
	fillOpacity: '填充不透明度',
	strokeOpacity: '描边不透明度',
	histogramBinCount: '箱数',
	columnGroup: '柱状图',
	barGroup: '条形图',
	pieGroup: '饼图',
	lineGroup: '线图',
	scatterGroup: '散点图',
	areaGroup: '面积图',
	histogramGroup: '直方图',
	combinationGroup: '组合图',
	groupedColumnTooltip: '分组',
	stackedColumnTooltip: '堆叠',
	normalizedColumnTooltip: '100% 堆叠',
	groupedBarTooltip: '分组',
	stackedBarTooltip: '堆叠',
	normalizedBarTooltip: '100% 堆叠',
	pieTooltip: '饼图',
	doughnutTooltip: '甜甜圈图',
	lineTooltip: '线图',
	groupedAreaTooltip: '面积',
	stackedAreaTooltip: '堆叠',
	normalizedAreaTooltip: '100% 堆叠',
	scatterTooltip: '散点图',
	bubbleTooltip: '气泡图',
	histogramTooltip: '直方图',
	columnLineComboTooltip: '柱状图 & 线图',
	areaColumnComboTooltip: '区域和列',
	customComboTooltip: '自定义组合',
	noDataToChart: '没有可供绘图的数据。',
	pivotChartRequiresPivotMode: '数据透视图需要启用数据透视模式。',
	chartSettingsToolbarTooltip: '菜单',
	chartLinkToolbarTooltip: '链接到网格',
	chartUnlinkToolbarTooltip: '从网格解除链接',
	chartDownloadToolbarTooltip: '下载图表',
	seriesChartType: '系列图表类型',
	seriesType: '系列类型',
	secondaryAxis: '次坐标轴',

	// ARIA
	ariaAdvancedFilterBuilderItem: '${variable}。级别${variable}。按ENTER键进行编辑。',
	ariaAdvancedFilterBuilderItemValidation: '${variable}。级别${variable}。${variable}按ENTER键进行编辑。',
	ariaAdvancedFilterBuilderList: '高级过滤器构建器列表',
	ariaAdvancedFilterBuilderFilterItem: '过滤条件',
	ariaAdvancedFilterBuilderGroupItem: '过滤组',
	ariaAdvancedFilterBuilderColumn: '列',
	ariaAdvancedFilterBuilderOption: '选项',
	ariaAdvancedFilterBuilderValueP: '值',
	ariaAdvancedFilterBuilderJoinOperator: '连接操作符',
	ariaAdvancedFilterInput: '高级过滤输入',
	ariaChecked: '已检查',
	ariaColumn: '列',
	ariaColumnGroup: '列组',
	ariaColumnFiltered: '已过滤列',
	ariaColumnSelectAll: '切换全选所有列',
	ariaDateFilterInput: '日期过滤输入',
	ariaDefaultListName: '列表',
	ariaFilterColumnsInput: '过滤列输入',
	ariaFilterFromValue: '从值过滤',
	ariaFilterInput: '过滤输入',
	ariaFilterList: '过滤列表',
	ariaFilterToValue: '过滤到值',
	ariaFilterValue: '过滤值',
	ariaFilterMenuOpen: '打开过滤菜单',
	ariaFilteringOperator: '过滤操作符',
	ariaHidden: '隐藏',
	ariaIndeterminate: '不确定',
	ariaInputEditor: '输入编辑器',
	ariaMenuColumn: '按CTRL ENTER打开列菜单',
	ariaRowDeselect: '按空格键取消选择此行',
	ariaRowSelectAll: '按空格键切换所有行选择',
	ariaRowToggleSelection: '按空格键切换行选择',
	ariaRowSelect: '按空格键选择此行',
	ariaSearch: '搜索',
	ariaSortableColumn: '按ENTER键排序',
	ariaToggleVisibility: '按空格键切换可见性',
	ariaToggleCellValue: '按空格键切换单元格值',
	ariaUnchecked: '未检查',
	ariaVisible: '可见',
	ariaSearchFilterValues: '搜索过滤值',
	ariaPageSizeSelectorLabel: '页面大小',

	// ARIA Labels for Drop Zones
	ariaRowGroupDropZonePanelLabel: '行组',
	ariaValuesDropZonePanelLabel: '值',
	ariaPivotDropZonePanelLabel: '列标签',
	ariaDropZoneColumnComponentDescription: '按DELETE键删除',
	ariaDropZoneColumnValueItemDescription: '按ENTER键更改聚合类型',
	ariaDropZoneColumnGroupItemDescription: '按ENTER键排序',
	// used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
	ariaDropZoneColumnComponentAggFuncSeparator: '的',
	ariaDropZoneColumnComponentSortAscending: '升序',
	ariaDropZoneColumnComponentSortDescending: '降序',

	// ARIA Labels for Dialogs
	ariaLabelColumnMenu: '列菜单',
	ariaLabelCellEditor: '单元格编辑器',
	ariaLabelDialog: '对话框',
	ariaLabelSelectField: '选择字段',
	ariaLabelRichSelectField: '丰富的选择字段',
	ariaLabelTooltip: '工具提示',
	ariaLabelContextMenu: '上下文菜单',
	ariaLabelSubMenu: '子菜单',
	ariaLabelAggregationFunction: '聚合函数',
	ariaLabelAdvancedFilterAutocomplete: '高级过滤器自动完成',
	ariaLabelAdvancedFilterBuilderAddField: '高级过滤器构建器添加字段',
	ariaLabelAdvancedFilterBuilderColumnSelectField: '高级过滤器构建器列选择字段',
	ariaLabelAdvancedFilterBuilderOptionSelectField: '高级过滤器构建器选项选择字段',
	ariaLabelAdvancedFilterBuilderJoinSelectField: '高级过滤器构建器连接操作符选择字段',

	// ARIA Labels for the Side Bar
	ariaColumnPanelList: '列列表',
	ariaFilterPanelList: '过滤器列表',

	// Number Format (Status Bar, Pagination Panel)
	thousandSeparator: ', ',
	decimalSeparator: '.',

	// Data types
	true: '是',
	false: '否',
	invalidDate: '无效日期',
	invalidNumber: '无效数字',
	january: '一月',
	february: '二月',
	march: '三月',
	april: '四月',
	may: '五月',
	june: '六月',
	july: '七月',
	august: '八月',
	september: '九月',
	october: '十月',
	november: '十一月',
	december: '十二月'
}
