import * as antd from 'ant-design-vue'
// 创建单例对象
const message = {
	error(content) {
		antd.message.error(/[\u4e00-\u9fa5]/.test(content) ? 'Error' : content)
	},
	success(content) {
		antd.message.success(/[\u4e00-\u9fa5]/.test(content) ? 'Success' : content)
	},
	warning(content) {
		antd.message.warning(content)
	},
	info(content) {
		antd.message.info(content)
	}
}
// 导出单例对象
export default message
