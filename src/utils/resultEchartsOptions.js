import dayjs from 'dayjs'
import { get_canvas } from '@/utils/teap'
import { fixInteger, echartsResize } from '@/utils/gis'
import { t } from '@/utils/common'
// 结果查看 统计图表 饼图
export const getPieSeries = (charts_data_arr, unit, charts_obj, charts_index, add_watermark, watermark_text) => {
	const option = {
		backgroundColor: add_watermark ? {
			type: 'pattern',
			image: get_canvas(watermark_text),
			repeat: 'repeat'
		} : undefined,
		animation: false,
		tooltip: {
			show: true,
			backgroundColor: 'rgba(255,255,255,0.8)',
			formatter: function(datas) {
				let format_value = datas.value
				if (format_value !== 0) { format_value = format_value.toFixed(2) }
				let res = datas.marker + datas.name + ': ' + format_value.toString() + unit
				if (charts_obj.Chart_level === 3) {
					return res + '(100%)'
				} else {
					let total = 0
					if (charts_obj.Chart_level === 2) {
						for (let i = 0; i < datas.treePathInfo.length; i++) {
							if (datas.treePathInfo[i].name !== '') {
								total = datas.treePathInfo[i].value; break
							}
						}
						if (total !== 0) {
							res = res + '(' + (datas.value / total * 100).toFixed(2).toString() + '%' + ')'
						} else {
							return res + '(100%)'
						}
					} else if (charts_obj.Chart_level === 1) {
						for (let i = 0; i < datas.treePathInfo.length; i++) {
							if (datas.treePathInfo[i].name === '') { total = datas.treePathInfo[i].value }
						}
						if (total !== 0) {
							res = res + '(' + (datas.value / total * 100).toFixed(2).toString() + '%' + ')'
						}
					}
					return res
				}
			}
		},
		toolbox: {
			show: true,
			top: 20,
			right: 150,
			emphasis: {
				iconStyle: {
					textPosition: 'top'
				}
			},
			feature: {
				mark: { show: true },
				// dataView: {
				//     buttonColor: '#3085d6',
				//     readOnly: true,
				//     icon: "image://"+new URL(`@/assets/table.png`, import.meta.url).href,
				//     lang: ['数据视图', '关闭', '刷新']
				// },
				saveAsImage: {
					show: true,
					title: t('保存为图片'),
					icon: 'image://' + new URL(`@/assets/download.png`, import.meta.url).href,
					name: ['', t('装机容量占比') + '_', t('发电量占比' + '_')][charts_index] + dayjs(new Date()).format('YYYYMMDD_HHmmss')
				},
				myReload: {
					show: true,
					title: t('刷新'),
					icon: 'image://' + new URL(`@/assets/refresh.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						charts_obj.clear()
						charts_obj.setOption(opt)
					}
				}
				// myFull: {
				//     show: true,
				//     title: '全屏',
				//     icon: "image://"+new URL(`@/assets/full.png`, import.meta.url).href,
				//     onclick: () => {
				//         // do_full(charts_index);
				//     },
				// },
			}
		},
		series: {
			type: 'sunburst',
			sort: null,
			emphasis: {
				focus: 'ancestor',
				itemStyle: {
					color: 'red'
				}
			},
			highlight: {
				itemStyle: {
					color: 'orange'
				}
			},
			downplay: {
				itemStyle: {
					color: '#ccc'
				}
			},
			data: charts_data_arr,
			radius: [0, '85%'],
			label: {
				rotate: 'radial'
			},
			minAngle: 10
		}
		// minAngle: 50
	}
	return option
}

// 结果查看 成本分析 饼图
export const getPieCost = (charts_data_arr, unit, charts_obj, charts_index, add_watermark, watermark_text) => {
	const option = {
		// tooltip: {
		// 	show: true,
		// 	backgroundColor: 'rgba(255,255,255,0.8)',
		// 	formatter: function(datas) {
		// 		let format_value = datas.value
		// 		if (format_value !== 0) { format_value = format_value.toFixed(2) }
		// 		let res = datas.marker + datas.name + ': ' + format_value.toString() + unit
		// 		if (charts_obj.Chart_level === 3) {
		// 			return res + '(100%)'
		// 		} else {
		// 			let total = 0
		// 			if (charts_obj.Chart_level === 2) {
		// 				for (let i = 0; i < datas.treePathInfo.length; i++) {
		// 					if (datas.treePathInfo[i].name !== '') {
		// 						total = datas.treePathInfo[i].value; break
		// 					}
		// 				}
		// 				if (total !== 0) {
		// 					res = res + '(' + (datas.value / total * 100).toFixed(2).toString() + '%' + ')'
		// 				} else {
		// 					return res + '(100%)'
		// 				}
		// 			} else if (charts_obj.Chart_level === 1) {
		// 				for (let i = 0; i < datas.treePathInfo.length; i++) {
		// 					if (datas.treePathInfo[i].name === '') { total = datas.treePathInfo[i].value }
		// 				}
		// 				if (total !== 0) {
		// 					res = res + '(' + (datas.value / total * 100).toFixed(2).toString() + '%' + ')'
		// 				}
		// 			}
		// 			return res
		// 		}
		// 	}
		// },
		legend: {
			data: charts_data_arr.map(item => item.name),
			top: 10,
			left: 'center'
		},
		toolbox: {
			show: true,
			top: 30,
			right: 0,
			emphasis: {
				iconStyle: {
					textPosition: 'top'
				}
			},
			feature: {
				mark: { show: true },
				saveAsImage: {
					show: true,
					title: t('保存为图片'),
					icon: 'image://' + new URL(`@/assets/download.png`, import.meta.url).href,
					name: t('成本分析') + dayjs(new Date()).format('YYYYMMDD_HHmmss')
				},
				myReload: {
					show: true,
					title: t('刷新'),
					icon: 'image://' + new URL(`@/assets/refresh.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						charts_obj.clear()
						charts_obj.setOption(opt)
					}
				}
			}
		},
		series: {
			type: 'pie',
			sort: null,
			emphasis: {
				focus: 'ancestor',
				itemStyle: {
					color: 'red'
				}
			},
			highlight: {
				itemStyle: {
					color: 'orange'
				}
			},
			downplay: {
				itemStyle: {
					color: '#ccc'
				}
			},
			data: charts_data_arr.filter(item => item.value !== 0),
			radius: [0, '85%'],
			label: {
				rotate: 'radial'
			},
			minAngle: 10
		}
	}
	return option
}

// 结果查看 规划 堆叠柱状图
export const get_plan_bar = (data, maxData, zoneType, config, zoom) => {
	const xData = maxData.map(item => config[item.index])
	const tempActualArr = data.map(item => item[zoneType])
	const tempMaxArr = maxData.map(item => item[zoneType]).map((item1, index1) => item1 - tempActualArr[index1])
	return {
		tooltip: {
			trigger: 'axis',
			formatter: (params) => {
				let tooltipContent = `<p>${params[0].name}</p>`
				params.forEach((item, index) => {
					tooltipContent += '<p style="text-align:left">' + item.marker + item.seriesName + ' : ' + (index == 1 ? fixInteger((item.data || 0) + (params[0].data || 0), 3) : fixInteger((item.data || 0), 3)) + '</p>'
				})
				return tooltipContent
			}
		},
		legend: {
			selectedMode: false
		},
		grid: {
			x: echartsResize(70 * zoom),
			y: echartsResize(70 * zoom),
			x2: echartsResize(30 * zoom),
			y2: echartsResize(30 * zoom)
		},
		xAxis: [
			{
				type: 'category',
				data: xData,
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0 // 设置为0以确保所有标签都显示
				}
			}

		],
		yAxis: [
			{
				name: 'MW/MWh',
				type: 'value'
			}
		],
		series: [
			{
				name: t('实际投运容量'),
				type: 'bar',
				stack: 'total',
				emphasis: {
					focus: 'series'
				},
				itemStyle: {
					color: '#328BF6'
				},
				data: tempActualArr
			},
			{
				name: t('最大可投运容量'),
				type: 'bar',
				stack: 'total',
				emphasis: {
					focus: 'series'
				},
				itemStyle: {
					color: '#B2CFEA'
				},
				data: tempMaxArr
			}
		]
	}
}

// 结果查看 规划 折线图
export const get_plan_line = (data, zoom) => {
	const xData = Object.keys(data)
	const valueArr = Object.values(data)
	return {
		tooltip: {
			trigger: 'axis',
			valueFormatter: (value) => {
				return fixInteger(value, 3)
			}
		},
		legend: {},
		grid: {
			x: echartsResize(70 * zoom),
			y: echartsResize(70 * zoom),
			x2: echartsResize(30 * zoom),
			y2: echartsResize(30 * zoom)
		},
		// toolbox: {
		// 	show: true,
		// 	feature: {
		// 		dataZoom: {
		// 			yAxisIndex: 'none'
		// 		},
		// 		dataView: { readOnly: false },
		// 		magicType: { type: ['line', 'bar'] },
		// 		restore: {},
		// 		saveAsImage: {}
		// 	}
		// },
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: xData,
			axisLabel: {
				interval: 0 // 设置为0以确保所有标签都显示
			}
		},
		yAxis: {
			name: 'MW',
			type: 'value',
			nameTextStyle: {
				padding: [0, 60, 0, 0]
			},
			axisLabel: {
				formatter: '{value}   '
			}
		},
		series: [
			{
				name: t('投运时间曲线'),
				type: 'line',
				data: valueArr,
				itemStyle: {
					color: '#328BF6'
				},
				markPoint: {
					label: {
						formatter: (params) => {
							return fixInteger(params.data.value, 0)
						}
					},
					data: [
						{ type: 'max', name: 'Max' },
						{ type: 'min', name: 'Min' }
					]
				}
				// markLine: {
				// 	data: [{ type: 'average', name: 'Avg' }]
				// }
			}
		]
	}
}
