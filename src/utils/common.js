import { saveAs } from 'file-saver'
import message from '@/utils/message'
import i18n from '@/i18n'
export function t(name) {
	return i18n.global.t(name)
}
export function removePrefixSuffix(filename) {
	// 移除"Result_"前缀和文件扩展名
	return filename.replace(/^Result_/, '').replace(/\..*$/, '')
}
export const decodeURIComponentInit = (str) => {
	const tempStr = str.replace(/%(?![0-9a-fA-F]{2})/g, '%25')
	return decodeURIComponent(tempStr)
}
export const downloadApiFile = ({ data, headers }) => {
	if (!headers['content-disposition']) {
		// 使用 FileReader 来读取 Blob 数据
		const reader = new FileReader()
		// 定义当读取完成时的回调函数
		reader.onload = function(event) {
			const jsonString = event.target.result // 获取读取的字符串数据
			const jsonData = JSON.parse(jsonString) // 将字符串解析为 JSON 对象
			message.error(jsonData.message)
		}
		reader.readAsText(data)
		return
	}
	const fileName = decodeURIComponentInit(headers['content-disposition'].split(';')[1].split('=')[1])
	saveAs(data, fileName)
}
// 文件流输出方法
export const fileBlobFun = (data, fileName) => {
	const blob = new Blob([data], {
		type: 'application/vnd.ms-excel'
	})
	const url = window.URL.createObjectURL(blob)
	const a = document.createElement('a')
	a.style.display = 'none'
	a.href = url
	a.download = fileName
	document.body.appendChild(a)
	a.click()
	document.body.removeChild(a)
}
// 数据处理 数组按长度截取
// export const splitGroup = (array, subGroupLength) => {
// 	let index = 0
// 	const newArray = []
// 	while (index < array.length) {
// 		newArray.push(array.slice(index, index += subGroupLength))
// 	}
// 	return newArray
// }

export const transformColor = (hex) => {
	// 去除可能包含的 "#" 字符
	hex = hex.replace(/^#/, '')
	// 拆分颜色通道
	const redHex = hex.slice(0, 2)
	const greenHex = hex.slice(2, 4)
	const blueHex = hex.slice(4, 6)
	// 将 16 进制值转换为十进制
	const redDecimal = parseInt(redHex, 16)
	const greenDecimal = parseInt(greenHex, 16)
	const blueDecimal = parseInt(blueHex, 16)
	// 构建 RGB 颜色值
	const rgbColor = `${redDecimal},${greenDecimal},${blueDecimal}`
	return rgbColor
}

// 十六进制颜色随机
export const randomColor = () => {
	const r = Math.floor(Math.random() * 256)
	const g = Math.floor(Math.random() * 256)
	const b = Math.floor(Math.random() * 256)
	const color = '#' + r.toString(16) + g.toString(16) + b.toString(16)
	return color
}

// RGBA格式随机色
export const randomRgbaColor = () => {
	var r = Math.floor(Math.random() * 256) // 随机生成256以内r值
	var g = Math.floor(Math.random() * 256) // 随机生成256以内g值
	var b = Math.floor(Math.random() * 256) // 随机生成256以内b值
	var alpha = Math.random() // 随机生成1以内a值
	return `rgb(${r},${g},${b},${alpha})` // 返回rgba(r,g,b,a)格式颜色
}
export const getDayOption = () => {
	const monthData = Array.from({ length: 12 }, (_, monthIndex) => {
		const month = (monthIndex + 1).toString().padStart(2, '0')
		const daysCount = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][monthIndex]
		return {
		  value: month,
		  label: `${month}` + t('月'),
		  children: Array.from({ length: daysCount }, (_, dayIndex) => {
				const day = (dayIndex + 1).toString().padStart(2, '0')
				return {
			  label: `${day}` + t('日'),
			  value: `${month}-${day}`
				}
		  })
		}
	  })
	return monthData
}
