<script setup>
import network from '@/config/teap.config'
import { loadingStore } from '@/store/loadingStore'
import { settingStore } from '@/store/settingStore'
import { routeStore } from '@/store/routeStore'
// import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enUS from 'ant-design-vue/es/locale/en_US'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import * as echarts from 'echarts'
import { storeToRefs } from 'pinia'
import { notification } from 'ant-design-vue'
import { onMounted, onUnmounted, provide, ref, reactive, nextTick } from 'vue'
import { openErrorModal, getNotify } from '@/utils/teap.js'
import { getConfigApi } from '@/api/index'
import Mitt from '@/utils/mitt.js'
import { debounce } from '@/utils/gis'
import { basicApi } from '@/api/exampleApi'
import { useRouter } from 'vue-router'
import { t } from '@/utils/common'
const storeModal = loadingStore()
const store = settingStore()
const storeRoute = routeStore()
const router = useRouter()
// eslint-disable-next-line no-unused-vars
const { loading, loadingTimeData } = storeToRefs(storeModal)
const { color, colorRGB, fontSize, isNotify, tscanShow, isChromeHigh } = storeToRefs(store)
const { routeTabs, activeKey, routeCache } = storeToRefs(storeRoute)
provide('ec', echarts)
const locale = ref(enUS)
const ws = ref()
const notifyCase = ref(localStorage.getItem('notifyCase') ? JSON.parse(localStorage.getItem('notifyCase')) : {})
// dayjs.locale('zh-cn')
dayjs.locale('en')
const state = reactive({
	applyVisible: false
})
const screenScale = () => {
	document.body.style.zoom = window.innerWidth / 1920
}
const debouncedScreenScale = debounce(screenScale, 200)
onMounted(() => {
	// zoomPlugin()
	isChromeHigh.value = isChromeHighVersion()
	const root = document.documentElement
	// root.style.setProperty('--base-fontSize', fontSize.value)
	root.setAttribute('data-size', fontSize.value)
	root.style.setProperty('--base-color', color.value)
	root.style.setProperty('--base-color-rgb', colorRGB.value)

	if (!isChromeHigh.value) {
		document.body.style.zoom = window.innerWidth / 1920
		window.addEventListener('resize', debouncedScreenScale)
	}
	basicApi({
		'import_string_func': 'teapcase:check_file_is_exist',
		'func_arg_dict': {
			'file_names': routeTabs.value.map(item => item.filePath)
		}
	}).then(res => {
		if (res.code === 1) {
			routeTabs.value = routeTabs.value.filter(item => res.func_result.includes(item.filePath))
			if (routeTabs.value.length === 0) {
				router.push('/')
				routeCache.value = []
				activeKey.value = ''
			} else {
				if (!routeTabs.value.find(item => item.key === activeKey.value)) {
					activeKey.value = routeTabs.value[0].key
					router.push(activeKey.value)
				}
			}
		}
	})
	getNotifyWs()
	checkDongle()
})
const getNotifyWs = () => {
	const wsprefix = navigator.userAgent.includes('Electron') ? 'ws://' : location.protocol === 'https:' ? 'wss://' : 'ws://'
	ws.value = new WebSocket(wsprefix + network.websocket_Prefix + '/backend/teap_global_ws/')
	ws.value.onmessage = function(event) {
		const data = JSON.parse(event.data)
		// 获取计算中的算例名称和等待中的算例个数
		store.getTaskFile({
			calculating_task_name: data.calculating_task_name,
			latest_waiting_task_count: data.latest_waiting_task_count,
			latest_waiting_task_names: data.latest_waiting_task_names,
			calculating_task_exec_percent: data.calculating_task_exec_percent
		})

		if (data.recent_finished_case_name && !(notifyCase.value.recent_finished_case_name == data.recent_finished_case_name && notifyCase.value.recent_finished_case_id == data.recent_finished_case_id)) {
			notifyCase.value = data
			if (isNotify.value) {
				notification.success({
					message: t('计算通知'),
					description: t('案例') + '"' + data.recent_finished_case_name.replace(/.xlsx/g, '') + '"' + t('已计算完成'),
					placement: 'bottomRight',
					duration: 15
				})
			}
		}
		if (data.recent_finished_case_name) getNotify()
		ws.value.send('teap')
	}
	ws.value.onopen = function() { ws.value.send('teap') }
	ws.value.onclose = function(e) {}
}
const checkDongle = (val) => {
	getConfigApi({}).then(res => {
		if (res.code == 1) {
			if (!res.auth_expired_flag) {
				const msg = res.reactivate_flag ? t('软证书定期校验失败，请确保您所处的网络环境可以连接证书申请时使用的服务器后重试。') : res.auth_expired_msg
				openErrorModal(msg, res.reactivate_flag).then(res => {
					checkDongle('success')
				}).catch(() => {
					state.applyVisible = true
				})
			}
			store.changeSystem(res.cpu_vendor_info, res.app_version, res.os_version_info, res.virtual_memory, res.physical_cpu_count, res.logical_cpu_count)
			store.changeWatermark(res.add_watermark, res.watermark_text)
			store.changeWsAcceptType(res.show_error_log, res.show_info_log, res.show_warning_log)
			store.changeNotify(res.finish_notify)
			res.debug ? store.changeDebugMode(true) : store.changeDebugMode(false)
			const root = document.documentElement
			store.changeAppTitle(res.app_title)
			store.changeAppTheme(res.front_end_version)
			store.getPermissionList(res.front_end_valid_modules)
			store.changeRecommendedParallel(res.recommended_parallel_number)
			store.getSimJobConfig(res.sim_job_config)

			document.title = res.app_title

			if (res.front_end_version == 'PRSAS') {
				// document.title = '新型电力系统规划研究仿真分析软件'
				root.style.setProperty('--theme-header-color', '#EBF0F4')
				root.style.setProperty('--theme-header-color1', '#A3C6E0')
				root.style.setProperty('--theme-toolbar-color', '#F3F6F8')
				root.style.setProperty('--theme-toolbar-color1', '#CBDFED')
				root.style.setProperty('--theme-bg-color', '#D6E3EC')
				root.style.setProperty('--theme-tabs-color', '#ACC3D4')
			} else if (res.front_end_version == 'TEAPZJ') {
				// document.title = '新型电力系统生产模拟平台'
				root.style.setProperty('--theme-header-color', '#E7ECF3')
				root.style.setProperty('--theme-header-color1', '#C5D8F3')
				root.style.setProperty('--theme-toolbar-color', '#F6F9FE')
				root.style.setProperty('--theme-toolbar-color1', '#D0DFF5')
				root.style.setProperty('--theme-bg-color', '#DDE6F1')
				root.style.setProperty('--theme-tabs-color', '#628ECC')
			} else if (res.front_end_version == 'TEAPJS') {
				// document.title = '江苏电力保供仿真推演数据管理平台'
				root.style.setProperty('--theme-header-color', '#007BFF')
				root.style.setProperty('--theme-header-color1', '#eaedf4')
				root.style.setProperty('--theme-toolbar-color', '#fff')
				root.style.setProperty('--theme-toolbar-color1', '#fff')
				root.style.setProperty('--theme-bg-color', '#fff')
				root.style.setProperty('--theme-tabs-color', '#E2EEFA')
			} else if (res.front_end_version == 'TEAP') {
				// document.title = 'TEAP新型电力系统规划仿真平台'
				root.style.setProperty('--theme-header-color', '#EBF0F4')
				root.style.setProperty('--theme-header-color1', '#A3C6E0')
				root.style.setProperty('--theme-toolbar-color', '#F3F6F8')
				root.style.setProperty('--theme-toolbar-color1', '#CBDFED')
				root.style.setProperty('--theme-bg-color', '#D6E3EC')
				root.style.setProperty('--theme-tabs-color', '#ACC3D4')
			} else {
				// document.title = 'TEAP新型电力系统规划仿真平台'
				root.style.setProperty('--theme-header-color', '#EBF0F4')
				root.style.setProperty('--theme-header-color1', '#A3C6E0')
				root.style.setProperty('--theme-toolbar-color', '#F3F6F8')
				root.style.setProperty('--theme-toolbar-color1', '#CBDFED')
				root.style.setProperty('--theme-bg-color', '#D6E3EC')
				root.style.setProperty('--theme-tabs-color', '#ACC3D4')
			}
			if (val == 'success') {
				nextTick(() => {
					Mitt.emit('softLicSuccess')
				})
			}
		} else {
			// console.log();
		}
	})
}
// 申请软件证书
const handleApplyModal = () => {
	state.applyVisible = false
	checkDongle('success')
}
const beforeDestroy = () => {
	if (ws.value) ws.value.close()
	localStorage.setItem('loadingTimeData', JSON.stringify(loadingTimeData.value))
	localStorage.setItem('notifyCase', JSON.stringify(notifyCase.value))
	if (navigator.userAgent.includes('Electron')) {
		sessionStorage.setItem('baseURL', network.baseURL)
		sessionStorage.setItem('websocket_url', network.websocket_Prefix)
		sessionStorage.setItem('webConfig', JSON.stringify(window.config.webConfig))
	}
}
onUnmounted(() => {
	window.removeEventListener('beforeunload', beforeDestroy)
	if (!isChromeHigh.value) window.removeEventListener('resize', debouncedScreenScale)
})
window.addEventListener('beforeunload', beforeDestroy)

const isChromeHighVersion = () => {
	const ua = navigator.userAgent.toLowerCase()
	const chromeIndex = ua.indexOf('chrome')
	if (chromeIndex > -1) {
		const version = ua.substring(chromeIndex + 7)
		const majorVersion = parseInt(version.split('.')[0], 10)
		return majorVersion > 127
	}
	return false
}
// function zoomPlugin() {
// 	const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect
// 	if (!isChromeHighVersion()) {
// 		return
// 	}
// 	Element.prototype.getBoundingClientRect = function() {
// 		const rect = originalGetBoundingClientRect.call(this)
// 		const zoom = Number(document.body.style.zoom || 1)

// 		const newRect = new DOMRect(
// 			rect.x / zoom + 100,
// 			rect.y / zoom + 100,
// 			rect.width / zoom,
// 			rect.height / zoom
// 		)
// 		return newRect
// 	}
// }
</script>
<template>
  <a-config-provider
    :locale="locale"
    :theme="{
      token: {
        colorPrimary: color,
      },
    }"
  >
    <a-spin :spinning="loading" size="large"  wrapperClassName="fullScreenLoading">
      <router-view />
    </a-spin>
  </a-config-provider>
  <ApplyModal v-if="state.applyVisible" @confirm="handleApplyModal"></ApplyModal>
  <tscan v-if="tscanShow"></tscan>
</template>
<style lang="scss">
/* teap3.0_new */
.fullScreenLoading{
  >div{
    opacity: 1!important;
    >.ant-spin-lg{
      opacity: 0;
    }
  }
  >div.ant-spin-container::after{
    background: transparent;
  }
  >div.ant-spin-container{
	pointer-events: unset;
  }
}
</style>
