import { request } from '@/request'

export const GetSimulationTaskResult = (url, data) => {
	return request({
		method: 'POST',
		url,
		data
	})
}
export const GetSimulationTaskResultBlob = (url, data) => {
	return request({
		method: 'POST',
		url,
		data,
		responseType: 'blob'
	})
}
export const LoadForecastModelParamUpdateView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_forecast_model_param/update/',
		data
	})
}
export const LoadForecastModelParamQueryView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_forecast_model_param/query/',
		data
	})
}
export const InsertTsFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/ts/insert_ts_file/',
		data
	})
}
export const LoadHistoryView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_history/',
		data
	})
}
export const LoadHistoryQueryView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_history/query/',
		data
	})
}
export const UploadTsFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/ts/upload_ts_file/',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const DownloadTsUploadTemplateFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/ts/download_ts_upload_template_file/',
		data,
		responseType: 'blob'
	})
}
export const LoadHistoryUpdateView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_history/update/',
		data
	})
}
export const LoadHistoryDeleteView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_history/delete/',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const LoadHistoryUploadView = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_history/upload/',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const TmyDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/tmy/download/',
		data
	})
}
export const TmyWindAndSolarDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/tmy_wind_and_solar/download/',
		data
	})
}

export const LoadForecastDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_forecast/download/',
		data
	})
}
export const LoadForecastInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/load_forecast/insert/',
		data
	})
}
export const AllLoadForecastArea = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/all_load_forecast_area/',
		data
	})
}
export const DownloadTrFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/download_tr_file/',
		data,
		responseType: 'blob'
	})
}
export const TmyInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/tmy/insert/',
		data
	})
}
export const TmyWindAndSolarInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/tmy_wind_and_solar/insert/',
		data
	})
}
export const GenMaintenancePlanConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan_config/',
		data
	})
}
export const GenYearsMaintenancePlanDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_years_maintenance_plan/download/',
		data
	})
}
export const GenYearsMaintenancePlanInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_years_maintenance_plan/insert/',
		data
	})
}
export const GenMaintenancePlanDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan/download/',
		data
	})
}
export const GenMaintenancePlanGenAndInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan/gen_and_insert/',
		data
	})
}
export const GenMaintenancePlanGenAndInsertAndDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan/gen_and_insert_and_download/',
		data
	})
}
export const GenMaintenancePlanUploadAndInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan/upload_and_insert/',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

// 年内检修相关操作的API
export const GenMaintenancePlan = (url, data) => {
	return request({
		method: 'POST',
		url,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

// 生成分区检修计划并插入算例文件并下载检修计划文件
export const GenMaintenancePlanPartitionGenAndInsertAndDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan_partition/gen_and_insert_and_download/',
		data
	})
}
// 生成并下载分区检修计划文件
export const GenMaintenancePlanPartitionDownload = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan_partition/download/',
		data
	})
}
// 生成分区检修计划并插入算例文件
export const GenMaintenancePlanPartitionGenAndInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan_partition/gen_and_insert/',
		data
	})
}
// 上传分区检修计划并插入算例文件
export const GenMaintenancePlanPartitionUploadAndInsert = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/gen_maintenance_plan_partition/upload_and_insert/',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

export const DownloadTempFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/download_temp_file/',
		data,
		responseType: 'blob'
	})
}

export const uploadCaseApi = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/upload_case_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const uploadTeapApi = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/upload_teap_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const activeCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/activate_case_file/',
		data
	})
}
export const InactivateCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/inactivate_case_file/',
		data
	})
}
export const getTaskTableApi = (data, cancel) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/task_table_view/',
		cancelToken: cancel,
		data
	})
}
export const EditNoteApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/update_case_file_note/',
		data
	})
}
export const EditNameApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/update_case_file_name/',
		data
	})
}
export const CopyCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/duplicate_case_file/',
		data
	})
}
export const SortCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/sort_case_file/',
		data
	})
}
export const LockCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/lock_case_file/',
		data
	})
}
export const UnLockCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/unlock_case_file/',
		data
	})
}
export const DeleteCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/delete_case_file/',
		data
	})
}
export const OpenExampleCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/open_example_case_file/',
		data
	})
}
export const DownloadExampleCaseFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/download_example_case_file/',
		responseType: 'blob',
		data
	})
}
export const DownLoadCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/download_case_file/',
		responseType: 'blob',
		data
	})
}
export const CheckCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/check_case_file/',
		responseType: 'blob',
		data
	})
}
export const CheckDogApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/check_auth_device/',
		data
	})
}
export const CheckAuthExpire = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/check_auth_expire/',
		data
	})
}
export const DownLoadLogApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/download_task_log/',
		responseType: 'blob',
		data
	})
}
export const DownLoadResultApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/download_task_result/' + data + '/',
		responseType: 'blob'
	})
}
export const DownLoadResultsApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/download_many_task_result/',
		data,
		responseType: 'blob'
	})
}
export const UploadTempFile = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/upload_temp_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const StartCalCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/start_case_file/',
		data
	})
}
export const SelectAllResultApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_all_finished_task_id/',
		data
	})
}
export const SelectAllTaskApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_all_unfinished_task_id/',
		data
	})
}
export const DownloadBPAApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/net_2_bpa/',
		data
	})
}
export const DownloadV3BPAApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/net_2_bpa/',
		data
	})
}

export const StopCalCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/stop_case_file/',
		data
	})
}
export const uploadBPAApi = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/upload_bpa_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const getConfigApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/',
		data
	})
}
export const getDetailResultApi = (id, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_detail_result/' + id + '/',
		data
	})
}
export const getResultApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_result/',
		data
	})
}
export const getLogApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_log/',
		data
	})
}
export const getLogApiNew = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_log_and_level/',
		data
	})
}
export const getSystemConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_system_config/',
		data
	})
}
export const updateSystemConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/update_system_config/',
		data
	})
}

export const downloadSystemConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/download_system_config/',
		data,
		responseType: 'blob'
	})
}

export const checkMemory = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/check_os_disk_info/',
		data
	})
}
export const checkBeforeStart = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/before_start_case_file/',
		data
	})
}
export const GetAllConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/get_all_config/',
		data
	})
}
export const UploadConfig = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/upload_config/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const DownloadConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/download_config/',
		data,
		responseType: 'blob'
	})
}
export const DelConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/del_config/',
		data
	})
}
export const AddConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/add_config/',
		data
	})
}
export const UpdateConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/update_config/',
		data
	})
}
export const applySoftLic = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/apply_soft_lic/',
		data
	})
}

// 平衡表下载
export const GetMidTermTaskResult = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_mid_term_task_result/',
		responseType: 'blob',
		data
	})
}

export const regenerateBalanceReports = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/regenerate_balance_reports/',
		data
	})
}

// bpa列表
export const getBpaList = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/list',
		data
	})
}
// bpa版本上传
export const uploadFileInit = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/file/upload/init',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

// bpa 分片上传
export const uploadFileSlice = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/file/upload/slice',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

// bpa 合并分片
export const uploadFileMerge = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/file/upload/merge',
		data
	})
}

// bpa 合并分片
export const getBpaVersionList = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/list',
		data
	})
}

// bpa 新增
export const bpaAdd = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/add',
		data
	})
}

// bpa 标准版本设置
export const setStandard = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/set/standard',
		data
	})
}

// bpa 修改密码
export const modifyPwd = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/modify/pwd',
		data
	})
}

// bpa 删除
export const bpaDelete = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/delete',
		data
	})
}

// bpa 下载
export const bpaDownload = (id) => {
	return request({
		method: 'get',
		url: `/backend/teap_bpavc/download?bpa_id=${id}`,
		responseType: 'blob'
	})
}

// bpa 密码验证
export const bpaValidPwd = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_bpavc/valid/pwd',
		data
	})
}

// BpaAnaConfig(查询BPA解析配置的API)
export const getBpaAnaConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/bpa_ana_config/',
		data
	})
}

// 下载BPA的API
export const DownloadBpaFileApi = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: `/backend/teap_api/download_file/`,
		data,
		isShowLoading,
		responseType: 'blob'
	})
}
