import { request } from '@/request'

// 万能接口
export const basicApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

// 万能接口  获取表格初始数据
export const getBaseDataApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  保存表格初始数据
export const saveBaseDataApi = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		isShowLoading,
		data
	})
}

//  万能接口  获取修改后的表格数据
export const getModifyDataApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  数据校核
export const checkDataApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  全局参数
export const globalParameters = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  全局参数 保存
export const SaveParameters = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  新建
export const createEmptyHdf = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		isShowLoading,
		data
	})
}

//  万能接口  树形结构菜单
export const getTreeMenu = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  读取h5文件某个元素的名称列和id列数据列表
export const getReadNameCol = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口  读取h5文件某个元素的某一行记录详情和外表有关联的详细信息
export const getReadOneRow = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  万能接口 参数补充
export const paramsReplenApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

//  远程搜索/模糊查询 母线选择接口
export const filterBusNameApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/filter_name/',
		data
	})
}

//  远程搜索/模糊查询 时序选择接口
export const filterTimeNameApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/filter_timeseries/',
		data
	})
}

// 项目GIS 获取拓扑图
export const topoGraph = data => {
	return request({
		method: 'GET',
		url: '/backend/teap_api_v3/topo_graph/',
		params: data
		// responseType: 'blob'
	})
}

// 获取时序表表数据
export const getTimeseriesApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/parse_timeseries/',
		data
	})
}

// 孤岛检测
export const islandCheckApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/topo_diagnostic/',
		data
	})
}

// 结果查看
export const getDetailResultApi = (id, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_detail_result/' + id + '/',
		data
	})
}
export const getResultApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_result/',
		data
	})
}

// 更新全局参数
// export const SaveParameterToXl = (data) => {
// 	return request({
// 		method: 'POST',
// 		url: '/backend/teap_api_v3/save_parameter_to_xl/',
// 		data
// 	})
// }

// 将文件上传到任务表的API
export const UploadCaseFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/upload_case_file/',
		data
	})
}

// 获取'电力电量平衡'任务计算结果的API
export const GetMidTermTaskResult = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_mid_term_task_result/',
		data
	})
}

// 下载 结果文件的API
export const DownloadTaskResult2 = id => {
	return request({
		method: 'POST',
		url: `/backend/teap_api/download_task_result_2/${id}/`,
		// data,
		responseType: 'blob'
	})
}

// 下载算例的API
export const DownloadCaseApi = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: `/backend/teap_api_v3/download_file/`,
		data,
		isShowLoading,
		responseType: 'blob'
	})
}

// 表格交互 导入
export const importXl = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/import_xl/',
		data,
		isShowLoading,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
// 表格交互 导出
export const exportXl = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: `/backend/teap_api_v3/export_xl/`,
		data,
		isShowLoading,
		headers: {
			'Content-Type': 'multipart/form-data'
		},
		responseType: 'blob'
	})
}

// 结果查看 下载算例
export const GetH5FromTeapFile = data => {
	return request({
		method: 'POST',
		url: `/backend/teap_api_v3/get_tc_from_tr/`,
		data,
		responseType: 'blob'
	})
}
// 结果查看 打开算例算例
export const getTcFromTr = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_tc_from_tr/',
		isShowLoading,
		data
	})
}

// 参数管理 左侧设备列表
export const deviceInitParam = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/device_init_param/query/',
		data
	})
}

// 参数管理 保存
export const updateDeviceInitParam = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/device_init_param/update/',
		data
	})
}

// 参数管理 恢复默认
export const resetDeviceInitParam = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/device_init_param/reset/',
		data
	})
}

// 算例拆分
export const splitCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/split_case/',
		data
	})
}
