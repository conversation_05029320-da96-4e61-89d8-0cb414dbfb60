import { request } from '@/request'
export const GetFaultLineList = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_fault_line_list/',
		data
	})
}
export const UploadFile = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/upload_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const UploadBpaFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/upload_bpa_file/',
		data
	})
}
export const GetBpaInfo = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_bpa_info/',
		data
	})
}
export const GetTrRelationTgFiles = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_tr_relation_tg_files/',
		data
	})
}
export const GetLineJobTaskResult = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_line_job_task_result/',
		data
	})
}
export const GetBpaClientDownloadUrl = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_bpa_client_download_url/',
		data
	})
}
export const DownloadTgFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/download_tg_file/',
		data,
		responseType: 'blob'
	})
}
export const UploadTgFile = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/upload_gis_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
