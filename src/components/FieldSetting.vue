<template>
  <div class="fieldSet-main">
    <div class="fieldSet-main-title">
      <div class="fieldSet-main-title-left">{{ $t('编辑器') }} / {{ $t('字段管理') }}</div>
      <div class="fieldSet-main-title-right">
        <a-input-search v-model:value="searchValue" style="width: 220px" :placeholder="$t('请输入')" />
        <a-button type="primary" :style="{'width': '96px'}" @click="handleSave">{{ $t('保存') }}</a-button>
        <a-button type="primary" :style="{'width': '96px'}" ghost @click="handleReset">{{ $t('重置') }}</a-button>
        <a-button :style="{'width': '96px'}" @click="handleCancel">{{ $t('关闭') }}</a-button>
      </div>
    </div>
    <div class="fieldSet-main-content">
      <div class="fieldSet-main-content-title">{{ $t('选择显示字段') }}</div>
      <div class="fieldSet-main-content-content">
        <div class="fieldSet-main-tree-left">
          <a-tree
            class="treeStyle"
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            v-model:checkedKeys="checkedKeys"
            :tree-data="treeData"
            :field-names="fieldNames"
            :show-icon="true"
            checkable
            checkStrictly
            @check="onCheck"
            @select="onSelect"
            @expand="onExpand"
          >
            <template #title="{ label, failNumber }">
              <span v-if="label.indexOf(searchValue) > -1">
                {{ label.substr(0, label.indexOf(searchValue)) }}
                <span style="color: #f50">{{ searchValue }}</span>
                {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
                <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
                <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
              </span>
              <span v-else>{{ label }}</span>
            </template>
          </a-tree>
        </div>
        <div  class="fieldSet-main-tree-right">
          <a-tree
            class="treeStyle"
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            v-model:checkedKeys="checkedPlanKeys"
            :tree-data="treePlanData"
            :field-names="fieldNames"
            :show-icon="true"
			:selectable="false"
            checkable
            checkStrictly
            @check="onCheckPlan"
          >
            <template #title="{ label, failNumber }">
              <span v-if="label.indexOf(searchValue) > -1">
                {{ label.substr(0, label.indexOf(searchValue)) }}
                <span style="color: #f50">{{ searchValue }}</span>
                {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
                <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
                <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
              </span>
              <span v-else>{{ label }}</span>
            </template>
          </a-tree>
        </div>
      </div>
      <div class="fieldSet-main-content-footer">
        <a-checkbox
          v-model:checked="state.checkAll"
          :indeterminate="state.indeterminate"
          @change="onCheckAllChange"
        >{{ $t('全选') }}</a-checkbox>
        <a-checkbox
          v-model:checked="state.checkDevice"
          :indeterminate="state.indeterminate1"
          @change="onCheckDeviceChange"
        >{{ $t('设备全选') }}</a-checkbox>
        <a-checkbox
          v-model:checked="state.checkAtt"
          :indeterminate="state.indeterminate2"
          @change="onCheckAttChange"
        >{{ $t('属性全选') }}</a-checkbox>
        <a-checkbox
          v-model:checked="state.checkStock"
          :indeterminate="state.indeterminate3"
          @change="onCheckStockChange"
        >{{ $t('存量设备全选') }}</a-checkbox>
        <a-checkbox
          v-model:checked="state.checkPlan"
          :indeterminate="state.indeterminate4"
          @change="onCheckPlanChange"
        >{{ $t('待规划设备全选') }}</a-checkbox>
      </div>
    </div>
  </div>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
const route = useRoute()
const expandedKeys = ref([])
const selectedKeys = ref(['bus'])
const checkedKeys = ref([]) // 存量设备
const checkedPlanKeys = ref([]) // 待规划设备

const state = reactive({
	checkAll: false,
	checkDevice: false, // 设备全选
	checkAtt: false, // 属性全选
	checkStock: false, // 存量设备全选
	checkPlan: false, // 待规划设备全选
	indeterminate: true,
	indeterminate1: true,
	indeterminate2: true,
	indeterminate3: true,
	indeterminate4: true,
	stockList: [],
	planList: [],
	treeBtmList: {},
	plan_value_list: [],
	stock_value_list: []
})

// const dataList = ref([])
const treeData = ref([])
const treePlanData = ref([])

const fieldNames = {
	key: 'value',
	title: 'label'
}

const onCheckAllChange = e => {
	state.indeterminate = false
	state.indeterminate1 = false
	state.indeterminate2 = false
	state.indeterminate3 = false
	state.indeterminate4 = false
	if (state.checkAll) {
		state.checkDevice = true
		state.checkAtt = true
		state.checkStock = true
		state.checkPlan = true
		checkedKeys.value = state.stockList
		const tempArr = []
		for (const key in state.treeBtmList) {
			state.treeBtmList[key].forEach(item => {
				tempArr.push(item.value)
			})
		}
		checkedPlanKeys.value = tempArr
	} else {
		state.checkDevice = false
		state.checkAtt = false
		state.checkStock = false
		state.checkPlan = false
		checkedKeys.value =	[]
		checkedPlanKeys.value =	[]
	}
}
const onCheckDeviceChange = e => {
	state.indeterminate1 = false
	state.indeterminate3 = false
	state.indeterminate4 = false
	if (state.checkDevice) {
		state.checkStock = true
		state.checkPlan = true
		checkedKeys.value = state.stockList
	} else {
		state.checkAll = false
		state.indeterminate = true
		state.checkStock = false
		state.checkPlan = false
		checkedKeys.value =	[]
	}
}
const onCheckAttChange = e => {
	state.indeterminate2 = false
	if (state.checkAtt) {
		const tempArr = []
		for (const key in state.treeBtmList) {
			state.treeBtmList[key].forEach(item => {
				tempArr.push(item.value)
			})
		}
		checkedPlanKeys.value = tempArr
	} else {
		state.checkAll = false
		state.indeterminate = true
		checkedPlanKeys.value = []
	}
}

const onCheckStockChange = e => {
	state.indeterminate3 = false
	if (state.checkStock) {
		state.stock_value_list.forEach(item => {
			if (!checkedKeys.value.includes(item)) {
				checkedKeys.value.push(item)
			}
		})
	} else {
		state.checkAll = false
		state.indeterminate = true
		state.checkDevice = false
		state.indeterminate1 = true
		checkedKeys.value =	checkedKeys.value.filter(item => !state.stock_value_list.includes(item))
	}
}

const onCheckPlanChange = e => {
	state.indeterminate4 = false
	if (state.checkPlan) {
		state.plan_value_list.forEach(item => {
			if (!checkedKeys.value.includes(item)) {
				checkedKeys.value.push(item)
			}
		})
	} else {
		state.checkAll = false
		state.indeterminate = true
		state.checkDevice = false
		state.indeterminate1 = true
		checkedKeys.value =	checkedKeys.value.filter(item => !state.plan_value_list.includes(item))
	}
}

watch(() => [state.checkDevice, state.checkAtt, state.checkStock, state.checkPlan], (newValues, oldValues) => {
	if (newValues.includes(true) && !newValues.includes(false)) {
		state.checkAll = true
		state.indeterminate = false
	} else if (newValues.includes(false) && !newValues.includes(true)) {
		state.checkAll = false
		state.indeterminate = false
	} else {
		state.indeterminate = true
	}

	if (newValues[2] && newValues[3]) {
		state.checkDevice = true
		state.indeterminate1 = false
	} else if (!newValues[2] && !newValues[3]) {
		state.checkDevice = false
		state.indeterminate1 = false
	} else {
		state.indeterminate1 = true
	}
})

const traverseTreeStock = (data) => {
	if (data === null) return // 当前节点为空，则直接返回
	for (let i = 0; i < data.length; i++) {
		state.stockList.push(data[i].value)
		if (data[i].show) {
			checkedKeys.value.push(data[i].value)
		}
		if (data[i].children) {
			expandedKeys.value.push(data[i].value)
			traverseTreeStock(data[i].children) // 递归遍历子节点
		}
	}
}

// const traverseTreePlan = (data) => {
// 	if (data === null) return // 当前节点为空，则直接返回
// 	for (let i = 0; i < data.length; i++) {
// 		state.planList.push(data[i].value)
// 		if (data[i].show) {
// 			checkedPlanKeys.value.push(data[i].value)
// 		}
// 		if (data[i].children) {
// 			checkedPlanKeys.value.push(data[i].value)
// 			traverseTreePlan(data[i].children) // 递归遍历子节点
// 		}
// 	}
// }

const onCheck = () => {
	checkedKeys.value = checkedKeys.value.checked
}

const onCheckPlan = () => {
	checkedPlanKeys.value = checkedPlanKeys.value.checked
}

const onSelect = (selectedKey, { selected, selectedNodes, node, event }) => {
	// if (node.isExpand) {
	// 	const index = expandedKeys.value.indexOf(node.key)
	// 	if (index === -1) {
	// 		expandedKeys.value.push(node.key)
	// 	} else {
	// 		expandedKeys.value = expandedKeys.value.filter((item) => item !== node.key)
	// 	}
	// }
	if (state.treeBtmList[node.key] && selected) {
		treePlanData.value = state.treeBtmList[node.key]
	} else {
		treePlanData.value = []
	}
}

// const getParentKey = (value, tree) => {
// 	let parentKey
// 	for (let i = 0; i < tree.length; i++) {
// 		const node = tree[i]
// 		if (node.children) {
// 			if (node.children.some(item => item.value === value)) {
// 				parentKey = node.value
// 			} else if (getParentKey(value, node.children)) {
// 				parentKey = getParentKey(value, node.children)
// 			}
// 		}
// 	}
// 	return parentKey
// }

const searchValue = ref('')

const onExpand = keys => {
	expandedKeys.value = keys
}

// watch(searchValue, value => {
// 	const expanded = dataList.value.map(item => {
// 		if (item.label.indexOf(value) > -1) {
// 			return getParentKey(item.value, treeData.value)
// 		}
// 		return null
// 	}).filter((item, i, self) => item && self.indexOf(item) === i)
// 	expandedKeys.value = expanded
// 	searchValue.value = value
// })

// 取消
const handleCancel = () => {
	Mitt.emit('handleCloseFieldSetting')
}

// 重置
const handleReset = () => {
	basicApi({
		'import_string_func': 'teapcase:write_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': 'meta_config',
			'data': {
				'visible_config': {}
			}
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message)
			checkedKeys.value = []
			checkedPlanKeys.value = []
			state.stockList = []
			state.planList = []
			getConfigTree()
		}
	})
}

const getConfigTree = () => {
	basicApi({
		'import_string_func': 'teapcase:visible_config_tree',
		'func_arg_dict': {
			'file_name': route.query.filePath
		}
	}).then(res => {
		const { top_level_visible_config_tree_list, btm_level_visible_config_tree_dict, plan_value_list, stock_value_list } = res.func_result
		treeData.value = top_level_visible_config_tree_list
		treePlanData.value = btm_level_visible_config_tree_dict.bus
		state.treeBtmList = btm_level_visible_config_tree_dict
		state.plan_value_list = plan_value_list
		state.stock_value_list = stock_value_list

		traverseTreeStock(treeData.value)
		// traverseTreePlan(treePlanData.value)

		const tempArr = []
		const tempShowArr = []
		for (const key in state.treeBtmList) {
			state.treeBtmList[key].forEach(item => {
				tempArr.push(item.value)
				if (item.show) {
					tempShowArr.push(item.value)
				}
			})
		}
		state.planList = tempArr
		checkedPlanKeys.value = tempShowArr
	})
}

const handleSave = () => {
	const tempObj = {}
	const checkedAllKeys = [...checkedKeys.value, ...checkedPlanKeys.value]
	const treeNodeList = [...state.stockList, ...state.planList]

	treeNodeList.forEach(item => {
		if (checkedAllKeys.find(item1 => item1 == item)) {
			tempObj[item] = true
		} else {
			tempObj[item] = false
		}
	})
	basicApi({
		'import_string_func': 'teapcase:write_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': 'meta_config',
			'data': {
				'visible_config': tempObj
			}
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message)
			Mitt.emit('handleUpdateFieldSetting')
		}
	})
}

defineExpose({ })

onMounted(() => {
	getConfigTree()
})
</script>
<style lang="scss" scoped>
.fieldSet-main {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: var(--theme-bg-color);
  .fieldSet-main-title {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    .fieldSet-main-title-left{
      color: #313339 ;
    }
    .fieldSet-main-title-right {
      display: flex;
      justify-content: space-between;
      width: 540px;
    }
  }
  .fieldSet-main-content {
    width: 88%;
    height: 88%;
    margin: 0 auto;
    border: 1px solid #999;
    border-radius: 6px;
    .fieldSet-main-content-title {
      width: 100%;
      height: 30px;
      padding: 0 15px;
      line-height: 30px;
      background-color: #fff;
      border-radius: 6px 6px 0 0;
    }
    .fieldSet-main-content-content {
      width: 100%;
      height: calc(100% - 60px);
      padding: 15px 30px;

      display: flex;
       :deep(.ant-tree)  {
          background-color: transparent;
          color: #1E3D59;
          overflow: auto;
          white-space: nowrap;
          // font-size: 14px;
          @include add-size(14px, $size);
        }
        >div {
          width: 50%;
          padding: 0 80px;
          overflow: auto; // 超出这个最大高度的数据，会被隐藏起来，上下滑动
        }
        // .fieldSet-main-tree-left {
          // border-right: 1px solid #f2f2f2;
        // }
    }
    .fieldSet-main-content-footer {
      width: 100%;
      height: 30px;
      padding: 0 15px;
      line-height: 30px;
      background-color: #fff;
      border-radius: 0 0 6px 6px;
    }
  }
}
</style>
