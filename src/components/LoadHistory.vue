<template>
  <a-modal wrapClassName="modal_loadHistory" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
      <div class="modal_top">
          <p>{{ $t('历史负荷管理') }}</p>
          <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large" :tip="$t('接口请求中')">
          <div class="modal_content relative">
              <div>
                  <p>{{ $t('选择区域') }}：</p>
                  <a-select v-model:value="state.area" :options="state.areaList" @change="changeArea">
                  </a-select>
              </div>
              <p>{{ $t('数据设置') }}</p>
              <div class="space-between">
                  <div class="left">
                      <div>
                          <p>{{ $t('年份') }}</p>
                          <p>{{ $t('用电量') }}</p>
                          <p>{{ $t('负荷曲线') }}</p>
                      </div>
                      <div class="scroll">
                          <div :class="state.year==item.label?'active':''" v-for="(item,index) in state.historyList" :key="index" @click="selectYear(item)">
                              <p>{{ item.label }}</p>
                              <p>
                                  <CheckOutlined v-if="item.value1" class="success" />
                                  <CloseOutlined v-else class="fail" />
                              </p>
                              <p>
                                  <CheckOutlined v-if="item.value2" class="success" />
                                  <CloseOutlined v-else class="fail" />
                              </p>
                          </div>
                      </div>
                  </div>
                  <div class="right">
                      <div class="right_top">
                          <div>
                              <p>{{ $t('用电量') }}</p>
                              <div v-if="state.year">
                                  <p @click="clear">{{ $t('清空') }}</p>
                                  <p @click="reset">{{ $t('重置') }}</p>
                              </div>
                          </div>
                          <div>
                              <div>
                                  <p>{{ $t('第一产业用电量') }}：</p>
                                  <a-input-number v-model:value="state.ipc1" :controls="false">
                                      <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                  </a-input-number>
                              </div>
                              <div>
                                  <p>{{ $t('第二产业用电量') }}：</p>
                                  <a-input-number v-model:value="state.ipc2" :controls="false">
                                      <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                  </a-input-number>
                              </div>
                              <div>
                                  <p>{{ $t('第三产业用电量') }}：</p>
                                  <a-input-number v-model:value="state.ipc3" :controls="false">
                                      <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                  </a-input-number>
                              </div>
                              <div>
                                  <p>{{ $t('城乡居民生活用电量') }}：</p>
                                  <a-input-number v-model:value="state.ipc4" :controls="false">
                                      <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                  </a-input-number>
                              </div>
                          </div>
                      </div>
                      <div class="right_bottom">
                          <p>{{ $t('时序负荷曲线') }}<span v-if="state.year" @click="downloadCsv">{{ $t('下载摸板') }}</span></p>
                          <div>
                              <a-upload
                                  v-model:file-list="state.fileList"
                                  name="file"
                                  @change="handleChange"
                                  accept=".csv"
                                  :multiple="false"
                                  :beforeUpload="()=>false"
                                  :showUploadList="false"
                                  >
                                  <a-button v-if="state.fileList.length==0" :disabled="!state.year">
                                      <upload-outlined></upload-outlined>
                                      {{ $t('点击上传') }}
                                  </a-button>
                                  <div class="modal_unit_upload_name" v-else>
                                      <!-- {{ state.fileList[0].name }} -->
                                      <div>
                                          <p class="ellipsis">{{ state.fileList[0].name }}</p>
                                          <close-outlined @click.stop="removeFile" class="pointer" />
                                      </div>
                                      <DownloadOutlined @click.stop="downloadYearCsv" v-if="!state.isUpload" />
                                  </div>
                              </a-upload>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
          <div class="modal_btn">
          <a-button :disabled="!state.year" @click="confirm" type="primary">{{ $t('保存') }}</a-button>
          <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
          </div>
      </a-spin>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive } from 'vue'
import { LoadHistoryView, LoadHistoryUploadView, LoadHistoryUpdateView, LoadHistoryDeleteView } from '@/api/index'
import { t } from '@/utils/common'
import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'
const props = defineProps({
	areaList: {
		type: Array,
		default: () => []
	}
})
const state = reactive({
	ifShow: true,
	loading: false,
	year: undefined,
	area: undefined,
	fileList: [],
	areaList: props.areaList,
	historyList: [],
	userList: {},
	userCsvList: {},
	systemList: {},
	csvStr: undefined,
	csvYearStr: undefined,
	ipc1: undefined,
	ipc2: undefined,
	ipc3: undefined,
	ipc4: undefined,
	isUpload: false
})
const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}
const updateList = (flag) => {
	LoadHistoryView({
		area: state.area
	}).then(res => {
		state.historyList = res.hist_years.map((item, index) => {
			return {
				label: item,
				value1: res.industry_power_consumption_flag[index],
				value2: res.load_hist_flag[index]
			}
		})
		state.csvStr = res.load_csv_template
		state.userList = res.custom_industry_power_consumption_dict
		state.systemList = res.built_in_industry_power_consumption_dict
		state.userCsvList = res.custom_load_hist_dict
		if (flag) {
			// selectYear()
			selectYear(state.historyList.find(item => item.label == state.year))
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}
const confirm = async() => {
	let isClear
	if (state.ipc1 == undefined && state.ipc2 == undefined && state.ipc3 == undefined && state.ipc4 == undefined) {
		isClear = true
	} else {
		if (state.ipc1 == undefined) {
			message.warning(t('请输入第一产业用电量') + '！')
			return
		}
		if (state.ipc2 == undefined) {
			message.warning(t('请输入第二产业用电量') + '！')
			return
		}
		if (state.ipc3 == undefined) {
			message.warning(t('请输入第三产业用电量') + '！')
			return
		}
		if (state.ipc4 == undefined) {
			message.warning(t('请输入城乡居民生活用电量') + '！')
			return
		}
		isClear = false
	}
	state.loading = true
	LoadHistoryUpdateView(Object.assign({
		area: state.area,
		hist_year: state.year
	}, isClear ? {} : { ipc1: state.ipc1,
		ipc2: state.ipc2,
		ipc3: state.ipc3,
		ipc4: state.ipc4 })).then(async res => {
		if (state.csvYearStr && state.fileList.length == 0) {
			const formdata = new FormData()
			formdata.append('area', state.area)
			formdata.append('hist_year', state.year)
			await LoadHistoryDeleteView(formdata)
		} else if (state.fileList.length != 0 && state.isUpload) {
			const formdata = new FormData()
			formdata.append('file', state.fileList[0].originFileObj)
			formdata.append('area', state.area)
			formdata.append('hist_year', state.year)
			await LoadHistoryUploadView(formdata)
		} else {
			message.success(t('保存成功'))
		}
		updateList(true)
	}).catch(() => {
		state.loading = false
	})
}
const removeFile = () => {
	state.fileList = []
}
const clear = () => {
	state.ipc1 = undefined
	state.ipc2 = undefined
	state.ipc3 = undefined
	state.ipc4 = undefined
}
const reset = () => {
	state.ipc1 = state.systemList[state.year] ? state.systemList[state.year][0] : undefined
	state.ipc2 = state.systemList[state.year] ? state.systemList[state.year][1] : undefined
	state.ipc3 = state.systemList[state.year] ? state.systemList[state.year][2] : undefined
	state.ipc4 = state.systemList[state.year] ? state.systemList[state.year][3] : undefined
}
const downloadCsv = () => {
	const link = document.createElement('a')
	link.style.display = 'none'
	link.setAttribute('href', encodeURI('data:text/csv;charset=utf-8,' + '\uFEFF' + state.csvStr))
	// link.setAttribute('download', '时序负荷曲线模板' + new Date().getTime() + '.csv')
	link.setAttribute('download', t('时序负荷曲线模板') + '.csv')
	document.body.appendChild(link)
	link.click()
	window.URL.revokeObjectURL(link.href)
	document.body.removeChild(link)
}
const downloadYearCsv = () => {
	const link = document.createElement('a')
	link.style.display = 'none'
	link.setAttribute('href', encodeURI('data:text/csv;charset=utf-8,' + '\uFEFF' + state.csvYearStr))
	link.setAttribute('download', t('时序负荷曲线') + '.csv')
	document.body.appendChild(link)
	link.click()
	window.URL.revokeObjectURL(link.href)
	document.body.removeChild(link)
}
const selectYear = (item) => {
	state.isUpload = false
	state.year = item.label
	if (item.value1) {
		state.ipc1 = state.userList[item.label][0]
		state.ipc2 = state.userList[item.label][1]
		state.ipc3 = state.userList[item.label][2]
		state.ipc4 = state.userList[item.label][3]
	} else {
		state.ipc1 = undefined
		state.ipc2 = undefined
		state.ipc3 = undefined
		state.ipc4 = undefined
	}
	if (item.value2) {
		state.fileList = [
			{
				name: t('时序负荷曲线') + '.csv'
			}
		]
		state.csvYearStr = state.userCsvList[state.year]
	} else {
		state.fileList = []
		state.csvYearStr = undefined
	}
}
const handleChange = () => {
	state.isUpload = true
}
const changeArea = () => {
	state.loading = true
	state.historyList = []
	state.year = undefined
	state.isUpload = false
	clear()
	state.fileList = []
	updateList()
}
onMounted(() => {

})
</script>
<style lang="scss">
  .modal_loadHistory{
    .ant-modal{
      width: auto!important;
      .ant-modal-body{
        >div{
          .modal_content{
            width: 780px;
            padding: 20px 30px 90px;
            >div:first-child{
                display: flex;
                align-items: center;
                p{
                    line-height: 38px;
                    font-size: 16px;
                }
                .ant-select-selector{
                    width: 210px;
                }
            }
            >p{
                line-height: 38px;
                font-size: 16px;
            }
            .left{
                width: 290px;
                >div:first-child,.scroll>div{
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    p{
                        text-align: center;
                        line-height: 32px;
                    }
                }
                >div:first-child{
                    border-radius: 8px 8px 0 0;
                    border: 1px solid #ccc;
                    background: rgb(248, 248, 248);
                    border-bottom: none;
                }
                .scroll{
                    border: 1px solid #ccc;
                    border-radius: 0 0 8px 8px;
                    height: 340px;
                    .success{
                        color:#27b148;
                    }
                    .fail{
                        color: #ccc;
                    }
                    >div:hover{
                        cursor: pointer;
                        background-color: skyblue;
                    }
                    .active{
                        background-color: skyblue;
                    }
                }
            }
            .right{
                width: 410px;
                .right_top{
                    border: 1px solid #ccc;
                    border-radius: 8px;
                    overflow: hidden;
                    background: rgb(248, 248, 248);
                    >div:first-child{
                        padding: 0 10px;
                        border-bottom: 1px solid #ccc;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        p{
                            line-height: 32px;
                        }
                        >div{
                            display: flex;
                            p{
                                line-height: 16px;
                                font-size: 14px;
                                padding: 3px 10px;
                                margin-left: 10px!important;
                                border-radius: 4px;
                                &:hover{
                                    cursor: pointer;
                                }
                            }
                            >p:first-child{
                                border: 1px solid #000;
                            }
                            >p:last-child{
                                border: 1px solid var(--base-color);
                                color: var(--base-color);
                            }
                        }
                    }
                    >div:last-child{
                        padding:20px 20px;
                        >div{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 15px;
                            .ant-input-number{
                                width: 135px;
                            }
                        }
                        >div:last-child{
                            margin-bottom: 0;
                        }
                    }
                }
                .right_bottom{
                    border: 1px solid #ccc;
                    border-radius: 8px;
                    margin-top: 10px;
                    overflow: hidden;
                    background: rgb(248, 248, 248);
                    >p{
                        padding: 0 10px;
                        line-height: 32px;
                        display: flex;
                        border-bottom: 1px solid #ccc;
                        span{
                            color: var(--base-color);
                            text-decoration: underline;
                            margin-left: 5px;
                            &:hover{
                                cursor: pointer;
                            }
                        }
                    }
                    >div{
                        height:80px;
                        display: flex;
                        align-items: center;
                        padding: 0 20px;
                         .modal_unit_upload_name{
                            display: flex;
                            justify-content: space-between;
                            >div:first-child{
                                display: flex;
                                width: 280px;
                                justify-content: space-between;
                                border: 1px solid #ccc;
                                border-radius: 5px;
                                padding: 5px;
                                span{
                                    font-size: 15px;
                                    background-color: #ccc;
                                    padding: 3px;
                                    border-radius: 5px;
                                    &:hover{
                                        cursor: pointer;
                                    }
                                }
                            }
                            p{
                                color: var(--base-color);
                                font-size: 15px;
                            }
                            >span{
                                background-color: #fff;
                                color: var(--base-color);
                                border: 1px solid #ccc;
                                font-size: 25px;
                                padding: 3px;
                                border-radius: 5px;
                                margin-left: 10px;
                                &:hover{
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                }
            }
          }
          .modal_btn{
            button{
                letter-spacing: 0;
            }
          }
        }
      }
    }
  }
</style>

