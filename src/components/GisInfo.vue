<!-- 可能存在问题 -->
<template>
    <div class="gis_info" v-if="state.isReady">
        <FormOutlined v-show="!state.isEdit&&!state.isAdd" class="edit_icon" @click="openEdit" />
        <p>{{ props.type=='lines'?  $t('通道信息') : $t('场站信息') }}</p>
        <div class="channel_info" v-if="props.type=='lines'&&state.mode==0">
            <div>
                <!-- <div>名称：<p v-if="!state.isEdit">{{ state.channelData.channel_name }}</p>
                <a-input v-else v-model:value="state.channelData.channel_name" /></div> -->
                <a-form
                    :model="formState"
                    ref="formRefs"
                >
                    <div class="form_div">
                        <a-form-item
                            :label="$t('名称')"
                            name="name"
                            :rules="[{ required: true, message: $t('请输入通道名称') }]"
                            >
                            <a-input :disabled="!state.isEdit" v-model:value="formState.name"/>
                        </a-form-item>
                    </div>
                </a-form>
                <p>{{ $t('首端') }}：<span>{{ state.channelData.from_station_name }}</span></p>
                <p>{{ $t('末端') }}：<span>{{ state.channelData.to_station_name }}</span></p>
            </div>
            <div>
                <div class="line_content">
                    <div class="line_list">
                        <p>{{ $t('线路列表') }}</p>
                        <a-radio-group v-model:value="state.lineIndex">
                            <a-radio :value="index" v-for="(item,index) in state.channelData.line_data" :key="item.index">{{ $t('线路')+(index+1)}}</a-radio>
                        </a-radio-group>
                    </div>
                    <div v-for="(item,index) in state.channelData.line_data" :key="index">
                        <div class="line_base" v-show="state.lineIndex==index">
                            <p>{{ $t('线路属性') }}</p>
                            <div v-if="item.type=='trafo'">
                                <p style="width: 100%;">{{ $t('名称') }}：{{ item.name }} </p>
                                <p>{{ $t('高压节点') }}：{{ item.hv_bus_name }} </p>
                                <p>{{ $t('低压节点') }}： {{ item.lv_bus_name }}</p>
                                <p style="width: 100%;">{{ $t('高压侧额定电压') }}：{{ item.vn_hv_kv }} kV</p>
                                <p style="width: 100%;">{{ $t('低压侧额定电压') }}：{{ item.vn_lv_kv }} kV</p>
                                <p>{{ $t('短路电压百分比') }}: {{ item.line_number }} %</p>
                                <p>{{ $t('额定容量') }}：{{ item.sn_mva }} MVA</p>
                                <p style="width: 100%;">{{ $t('投运时段') }}：{{ item.commissioning_datetime }} —— {{ item.decommissioning_datetime }}</p>
                                <p>{{ $t('所属断面') }}：{{ item.interface}}</p>
                            </div>
                            <div v-else-if="item.type=='dc_line'||item.type=='ac_line'">
                                <p style="width: 100%;">{{ $t('名称') }}：{{ item.name }} </p>
                                <p style="width: 100%;">{{ $t('首端节点') }}：{{ item.from_bus_name }}</p>
                                <p style="width: 100%;">{{ $t('末端节点') }}：{{ item.to_bus_name }} </p>
                                <p>{{ $t('电压等级') }}：{{ item.vn_kv}} kV</p>
                                <p>{{ $t('线路长度') }}：{{ item.length_km }} km</p>
                                <p style="width: 100%;">{{ $t('单位电阻') }}：{{ item.r_ohm_per_km }} Ω/km</p>
                                <p style="width: 100%;">{{ $t('最大载流上限') }}： {{ item.max_i_ka }} kA</p>
                                <p style="width: 100%;">{{ $t('单位电抗') }}：{{ item.x_ohm_per_km }} Ω/km</p>
                                <p style="width: 100%;">{{ $t('投运时段') }}：{{ item.commissioning_datetime }} —— {{ item.decommissioning_datetime }}</p>
                                <p>{{ $t('所属断面') }}：{{ item.interface||'暂无'}}</p>
                            </div>
                        </div>
                        <div class="line_graph" v-if="state.channelData.line_data.length<=6&&state.lineIndex==index">
                            <p>{{ $t('图形') }}</p>
                            <div class="line_config">
                                <p>{{ $t('颜色') }}：<span v-if="!state.isEdit" class="line_color" :style="{'background':item.color}"></span><input v-else v-model="item.color" type="color" /></p>
                                <p>{{ $t('类型') }}：<span v-if="!state.isEdit" class="line_type"><span :style="{borderBottom:`5px ${item.line_types} ${item.color}`}"></span></span>
                                    <a-select v-else
                                        ref="select"
                                        v-model:value="item.line_types"
                                        >
                                        <a-select-option value="solid">————</a-select-option>
                                        <a-select-option value="dashed">— — —</a-select-option>
                                        <a-select-option value="dotted">···············</a-select-option>
                                    </a-select>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
                <div v-if="state.channelData.line_data.length>6">
                    <p>{{ $t('图形') }}</p>
                    <div class="line_config">
                        <p>{{ $t('颜色') }}：<span v-if="!state.isEdit" class="line_color" :style="{'background':state.channelData.color}"></span><input v-else v-model="state.channelData.color" type="color" /></p>
                        <p>{{ $t('类型') }}：<span v-if="!state.isEdit" class="line_type"><span :style="{borderBottom:`5px ${state.channelData.line_types} ${state.channelData.color}`}"></span></span>
                            <a-select v-else
                                ref="select"
                                v-model:value="state.channelData.line_types"
                                >
                                <a-select-option value="solid">————</a-select-option>
                                <a-select-option value="dashed">— — —</a-select-option>
                                <a-select-option value="dotted">···············</a-select-option>
                            </a-select>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="station_info" v-else>
            <div>
                <a-form
                    :model="formState"
                    ref="formRef"
                >
                    <div class="form_div">
                        <a-form-item
                            :label="props.type=='lines' ? $t('通道名称') : $t('场站名称')"
                            name="name"
                            :rules="[{ required: true, message: props.type=='lines'?$t('请输入通道名称'):$t('请输入场站名称') }]"
                            >
                            <a-input :disabled="!state.isAdd&&!state.isEdit" v-model:value="formState.name"/>
                        </a-form-item>
                        <a-form-item
                            :label="$t('电压等级')"
                            name="vn_kv"
                            v-if="props.type!='lines'"
                            >
                            <a-input-number disabled :controls="false"  v-model:value="formState.vn_kv" >
                                <template #addonAfter>
                                    <span>kV</span>
                                </template>
                            </a-input-number>
                        </a-form-item>
                    </div>
                    <div class="form_div" v-if="props.type=='lines'">
                        <a-form-item
                            :label="$t('一端')"
                            name="from_station"
                            :rules="[{ required:true, message: $t('请选择一端') }]"
                            >
                            <a-select :filter-option="filterOption" show-search allowClear v-model:value="formState.from_station" :options="state.stationOption1" :disabled="!state.isAdd" @change="changeFromStation">
                            </a-select>
                        </a-form-item>
                        <a-form-item
                            :label="$t('另一端')"
                            name="to_station"
                            :rules="[{ required:true, message: $t('请选择另一端') }]"
                            >
                            <a-select :filter-option="filterOption" show-search allowClear v-model:value="formState.to_station"  :options="state.stationOption2" :disabled="!state.isAdd" @change="changeToStation">
                            </a-select>
                        </a-form-item>
                    </div>
                    <div class="form_div" v-if="props.type=='point'">
						<a-form-item
                            :label="$t('场站类型')"
                            name="type"
                            :rules="[{ required:true, message: $t('请选择场站类型') }]"
                            >
                            <a-select :disabled="!state.isAdd&&!state.isEdit" v-model:value="formState.type" :options="state.stationTypeOptions">
                            </a-select>
                        </a-form-item>
						<a-form-item
							v-if="state.mode==0"
                            :label="$t('图形颜色')"
                            name="color"
                        >
                            <input :disabled="!state.isAdd&&!state.isEdit" v-model="formState.color" type="color" />
                        </a-form-item>
						<!-- <a-form-item
							v-if="state.mode==0"
                            label="图形类型"
                            name="color"
                            >
							<a-select
                                ref="select"
                                v-model:value="formState.borderType"
								:disabled="!state.isAdd&&!state.isEdit"
                            >
                                <a-select-option value="solid">————</a-select-option>
                                <a-select-option value="dashed">— — —</a-select-option>
                                <a-select-option value="dotted">···············</a-select-option>
                            </a-select>
                        </a-form-item> -->
                        <!-- <a-form-item
                            label="装机容量"
                            name="max_p_mw"
                            >
                            <a-input-number disabled :controls="false"  v-model:value="formState.max_p_mw" >
                                <template #addonAfter>
                                    <span>MW</span>
                                </template>
                            </a-input-number>
                        </a-form-item> -->
                    </div>
                </a-form>
            </div>
            <div class="info_list" v-if="props.type=='lines'">
                <p>{{ $t('包含线路') }}</p>
                <div class="table_list">
                    <a-table
                        :row-selection="(state.isEdit||state.isAdd)&&state.mode==1?{ selectedRowKeys: state.selectedLineKeys, onChange: selectLinesChange }:undefined"
                       	:columns="handleColumns(state.addModalColumn1).columns"
						:scroll="{ x: handleColumns(state.addModalColumn1).length}"
                        rowKey="index"
                        :data-source="state.line_data"
                        :pagination="false"
                        :customRow="customRow(1)"
                    >
						<template #bodyCell="{ column,text}">
							<template v-if="column.type=='boolean'">
								{{ text==true ? $t('是'):$t('否') }}
							</template>
							<template v-if="column.col_source=='bus'">
								{{ state.allBusData.find(item=>item.index==text).name }}
							</template>
						</template>
					</a-table>
                </div>
                <div class="option_btn" v-if="state.isEdit||state.isAdd">
                    <div @click="addRow('ac_line')" :class="(formState.to_station==undefined||formState.from_station==undefined)?'disabled':''"><PlusCircleFilled />{{ $t('添加行') }}</div>
                    <div @click="deleteRow('ac_line')" :class="(state.line_data.length==0||state.selectedLineKeys.length==0)?'disabled':''"><MinusCircleFilled />{{ $t('删除行') }}</div>
                </div>
            </div>
            <div class="info_list" v-if="props.type=='point'">
                <p>{{ $t('包含节点') }}</p>
                <div class="table_list">
                    <a-table
                        :row-selection="(state.isEdit||state.isAdd)&&state.mode==1?{ selectedRowKeys: state.selectedBusKeys, onChange: selectBusChange }:undefined"
						:columns="handleColumns(state.addModalColumn2).columns"
						:scroll="{ x: handleColumns(state.addModalColumn2).length}"
                        rowKey="index"
                        :data-source="state.bus_data"
                        :pagination="false"
                        :customRow="customRow(1)"
                    >
                        <template #bodyCell="{ column,text}">
                            <template v-if="['in_service'].includes(column.dataIndex)">
                                {{ text==true ? $t('是'):$t('否') }}
                            </template>
							<template v-if="['zone'].includes(column.dataIndex)">
                                {{ state.zoneOption.find(item=>item.value==text)?state.zoneOption.find(item=>item.value==text).label:'' }}
                            </template>
                        </template>
                    </a-table>
                </div>
                <div class="option_btn" v-if="(state.isEdit||state.isAdd)&&state.mode!=0">
                    <div @click="addRow('bus')"><PlusCircleFilled />{{ $t('添加行') }}</div>
                    <div @click="deleteRow('bus')" :class="(state.bus_data.length==0||state.selectedBusKeys.length==0)?'disabled':''"><MinusCircleFilled />{{ $t('删除行') }}</div>
                </div>
				<p>{{ $t('包含变压器') }}</p>
                <div class="table_list">
                    <a-table
                        :row-selection="(state.isEdit||state.isAdd)&&state.mode==1?{ selectedRowKeys: state.selectedTrafoKeys, onChange: selectTrafoChange }:undefined"
                        :columns="handleColumns(state.addModalColumn3).columns"
						:scroll="{ x: handleColumns(state.addModalColumn3).length}"
                        rowKey="index"
                        :data-source="state.trafo_data"
                        :pagination="false"
                        :customRow="customRow(2)"
                    >
						<template #bodyCell="{ column,text}">
							<template v-if="column.type=='boolean'">
								{{ text==true ? $t('是'):$t('否') }}
							</template>
							<template v-if="column.col_source=='bus'">
								{{ state.allBusData.find(item=>item.index==text).name }}
							</template>
						</template>
                    </a-table>
                </div>
                <div class="option_btn" v-if="(state.isEdit||state.isAdd)&&state.mode!=0">
                    <div @click="addRow('trafo')" :class="(state.bus_data.length==0)?'disabled':''"><PlusCircleFilled />{{ $t('添加行') }}</div>
                    <div @click="deleteRow('trafo')" :class="(state.trafo_data.length==0||state.selectedTrafoKeys.length==0)?'disabled':''"><MinusCircleFilled />{{ $t('删除行') }}</div>
                </div>
                <div class="equipment_title">
                    <p>{{ $t('包含设备') }}</p>
                    <div class="option_btn" v-if="(state.isEdit||state.isAdd)&&state.mode==1">
                        <div @click="addEquipment" :class="state.bus_data.length==0||!formState.type?'disabled':''"><PlusCircleFilled />{{ $t('添加设备') }}</div>
                        <div @click="removeEquipment" :class="(state.equipmentList.every(item=>item.selectedEquipmentKeys.length==0))?'disabled':''"><MinusCircleFilled />{{ $t('删除设备') }}</div>
                    </div>
                </div>
                <div class="equipment_list">
                    <div v-for="(item,index) in state.equipmentList" :key="index">
                        <p>{{ item.label }}</p>
                        <div>
                            <a-table
                                :row-selection="(state.isEdit||state.isAdd)&&state.mode==1?{ selectedRowKeys: item.selectedEquipmentKeys, onChange: selectEquipmentChange(item) }:undefined"
                                :columns="handleColumns(item.columns).columns"
                                rowKey="index"
                                :scroll="{ x: handleColumns(item.columns).length}"
                                :data-source="item.data"
                                :pagination="false"
                                :customRow="customRow(3)"
                            >
                                <template #bodyCell="{ column,text}">
                                    <template v-if="column.type=='boolean'">
                                        {{ text==true ? $t('是'):$t('否') }}
                                    </template>
                                    <template v-if="column.dataIndex=='bus'">
                                        {{ state.bus_data.find(item=>item.index==text).name }}
                                    </template>
									<template v-else-if="column.col_source=='bus'">
										{{ state.allBusData.find(item=>item.index==text).name }}
									</template>
                                </template>
                            </a-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="edit_btn" v-if="state.isEdit||state.isAdd">
			<p v-if="state.mode==0"></p>
			<p v-else>（ {{ $t('注') }}:&nbsp;{{ props.type=='lines'? $t('通道内必须至少包含一条线路') : $t('场站内必须至少包含一个节点') }} ）</p>
			<div>
				<a-button :disabled="state.mode==1&&((props.type=='lines'&&state.line_data.length==0)||props.type=='point'&&state.bus_data.length==0)" shape="circle" type="primary" :icon="h(CheckOutlined)" @click="saveEdit"/>
				<a-button shape="circle" :icon="h(CloseOutlined)" @click="cancelEdit" />
			</div>
        </div>
    </div>
    <div v-else class="gis_info">
        <p>{{ props.type=='lines' ?  $t('通道信息') :  $t('场站信息') }}</p>
        <a-spin size="large">
            <div :class="props.type=='lines'&&state.mode==0?'channel_info loading':'station_info loading'"></div>
        </a-spin>
    </div>
    <div class="gis_info_mask" v-if="state.isReady&&(state.isEdit||state.isAdd)">

    </div>
    <add-complex-modal :options="state.bus_data.map(item=>{return{label:item.name,value:item.index}})"
		:isAdd="state.optionType=='add'"
		:data="state.addEquipmentData"
		v-if="state.addComplexShow"
		:type="formState.type"
		@close="state.addComplexShow=false;state.addEquipmentData=undefined"
		@confirm="confirmComplexAdd"></add-complex-modal>
    <add-modal :relationOption="state.relationOption"
		:relationColumn="state.relationColumn"
		:busOption="state.allBusData"
		:bus_data="state.bus_data"
		:columns="state.editType=='ac_line'? state.addModalColumn1:state.editType=='bus'?state.addModalColumn2:state.addModalColumn3"
		:isAdd="state.optionType=='add'"
		:data="state.addEquipmentData"
		v-if="state.addSimpleShow"
		:type="state.editType"
		@close="state.addSimpleShow=false;state.addEquipmentData=undefined"
		@confirm="confirmSimpleAdd"></add-modal>
</template>
<script setup>
import { t } from '@/utils/common'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { reactive, ref, onMounted, h, watch } from 'vue'
import { useRoute } from 'vue-router'
import { openModal, stationList, getGisStyle, deepClone, getTextWidth } from '@/utils/gis'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
import { loadingStore } from '@/store/loadingStore'
const storeModal = loadingStore()
const emit = defineEmits(['saveEdit', 'cancelEdit', 'refreshGis'])
const route = useRoute()
const props = defineProps({
	isAdd: {
		type: Boolean,
		default: false
	},
	busData: {
		type: Array,
		default: () => []
	},
	type: {
		type: String
	},
	selectedStationId: {
		type: String
	},
	equipmentType: {
		type: Number
	},
	zlevelId: {
		type: Number
	},
	mode: {
		type: Number
	},
	data: {
		type: Object,
		default: () => {}
	}
})
const formRef = ref()
const formRefs = ref()
const formState = reactive({
	name: undefined,
	vn_kv: undefined,
	from_station: undefined,
	to_station: undefined,
	type: undefined,
	max_p_mw: undefined,
	color: undefined
})
const state = reactive({
	mode: props.mode,
	isEdit: false,
	isAdd: props.isAdd,
	channelData: {
		line_data: []
	},
	addLineIndex: -1,
	addBusIndex: -1,
	addTrafoIndex: -1,
	addEuqipmentIndex: -1,
	line_data: [],
	baseChannelData: {},
	bus_data: [],
	trafo_data: [],
	baseData: {},
	equipmentList: [],
	lineIndex: undefined,
	selectedLineKeys: [],
	selectedBusKeys: [],
	selectedTrafoKeys: [],
	isReady: false,
	stationOption: [],
	stationOption1: [],
	stationOption2: [],
	addComplexShow: false,
	addSimpleShow: false,
	addEquipmentData: undefined,
	optionType: undefined,
	stationTypeOptions: [],
	relationColumn: {},
	relationOption: {},
	addModalColumn1: [],
	addModalColumn2: [],
	addModalColumn3: [],
	zoneOption: [],
	allBusData: props.busData
})
const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const handleColumns = (columns) => {
	const regex = /成本|费用|效率|对应出力率|类型/
	// const column = columns.filter(item => !item.hide && item.required && !regex.test(item.headerName) && item.field != 'timeseries').map(item1 => {
	const column = columns.filter(item => !item.hide && item.required && !regex.test(item.headerName)).map(item1 => {
		return {
			title: item1.headerName,
			dataIndex: item1.field,
			align: 'center',
			type: item1.cellDataType,
			col_source: item1.col_source,
			ellipsis: true,
			width: getTextWidth(item1.headerName)
		}
	})
	return {
		columns: column,
		length: column.reduce((acc, cur) => {
			return acc + getTextWidth(cur.title)
		}, 0) * 1.5
	}
}
const customRow = (type) => {
	return (record) => {
		return {
			ondblclick: (e) => {
				if (e.target.nodeName == 'INPUT') return
				if (state.isEdit && state.mode == 1 || state.isAdd) {
					state.optionType = 'edit'
					state.addEquipmentData = record
					if (type == 1) {
						if (props.type == 'lines') {
							state.editType = 'ac_line'
							state.addModalColumn1.find(item => item.field == 'from_bus').options = state.stationOption.find(item => item.value == formState.from_station).children
							state.addModalColumn1.find(item => item.field == 'to_bus').options = state.stationOption.find(item => item.value == formState.to_station).children
						} else if (props.type == 'point') {
							state.editType = 'bus'
						}
						state.addSimpleShow = true
					} else if (type == 2) {
						state.editType = 'trafo'
						state.addSimpleShow = true
					} else if (type == 3) {
						state.addComplexShow = true
					}
				}
			}
		}
	}
}
const confirmSimpleAdd = (formState) => {
	state.addSimpleShow = false
	if (state.optionType == 'add') {
		if (state.editType == 'ac_line') {
			state.line_data.push(formState)
		} else if (state.editType == 'bus') {
			state.bus_data.push(formState)
			state.allBusData.push(formState)
		} else if (state.editType == 'trafo') {
			state.trafo_data.push(formState)
		}
	} else {
		if (state.editType == 'ac_line') {
			const findIndex = state.line_data.findIndex(item => item.index == formState.index)
		    state.line_data.splice(findIndex, 1, formState)
		} else if (state.editType == 'bus') {
			const findIndex1 = state.bus_data.findIndex(item => item.index == formState.index)
		    state.bus_data.splice(findIndex1, 1, formState)
			const findIndex2 = state.allBusData.findIndex(item => item.index == formState.index)
		    state.allBusData.splice(findIndex2, 1, formState)
		} else if (state.editType == 'trafo') {
			const findIndex = state.trafo_data.findIndex(item => item.index == formState.index)
			state.trafo_data.splice(findIndex, 1, formState)
		}
	}
}
const confirmComplexAdd = (formState, { label, columns }) => {
	state.addComplexShow = false
	if (state.optionType == 'add') {
		const find = state.equipmentList.find(item => item.value == formState.table_type)
		if (find) {
			find.data.push(formState)
		} else {
			state.equipmentList.push({
				data: [formState],
				value: formState.table_type,
				label,
				selectedEquipmentKeys: [],
				columns: columns.filter(item => item.required)
			})
		}
	} else {
		state.equipmentList.forEach(item => {
			if (item.value == formState.table_type) {
				const findIndex = item.data.findIndex(item => item.index == formState.index)
		    	item.data.splice(findIndex, 1, formState)
			}
		})
	}
}
const changeFromStation = () => {
	if (formState.from_station) {
		state.stationOption2 = state.stationOption.filter(item => item.value != formState.from_station)
	} else {
		state.stationOption2 = state.stationOption
	}
}
const changeToStation = () => {
	if (formState.to_station) {
		state.stationOption1 = state.stationOption.filter(item => item.value != formState.to_station)
	} else {
		state.stationOption1 = state.stationOption
	}
}
const openEdit = () => {
	state.isEdit = true
}
const cancelEdit = () => {
	if (props.isAdd) {
		emit('cancelEdit')
	} else {
		if (state.mode == 0) {
			if (props.type == 'lines') {
				state.channelData = state.baseChannelData
				state.channelData.line_data.forEach(item => {
					item.color = item.color || getGisStyle(item).color
					item.line_types = item.line_types || 'solid'
				})
				formState.name = state.baseChannelData.channel_name
				formRefs.value.validate()
			} else if (props.type == 'point') {
				formState.type = state.baseData.station_type
				formState.name = state.baseData.station_name
				formRef.value.validate()
			}
		} else if (state.mode == 1) {
			if (props.type == 'lines') {
				state.selectedLineKeys = []
				formState.name = state.baseData.channel_name
				state.line_data = state.baseData.line_data
				formRef.value.validate()
			} else if (props.type == 'point') {
				state.selectedBusKeys = []
				state.bus_data = state.baseData.bus_data
				state.trafo_data = state.baseData.trafo_data
				state.equipmentList = state.baseData.relation_data.map(item => {
					return {
						...item,
						selectedEquipmentKeys: [],
						data: item.data.map(item1 => {
							return {
								...item1,
								table_type: item.value
							}
						})
					}
				})
				formState.type = state.baseData.station_type
				formState.name = state.baseData.station_name
				formRef.value.validate()
			}
		}
	}
	state.isEdit = false
}
const saveEdit = () => {
	if (props.isAdd) {
		formRef.value.validate()
			.then(() => {
				storeModal.showModal()
				if (props.type == 'lines') {
					basicApi({
						'import_string_func': 'teapgis:add_channel',
						'func_arg_dict': {
							'tc_file_path': route.query.filePath,
							'channel_name': formState.name,
							'from_station': formState.from_station,
							'to_station': formState.to_station,
							'line_data': state.line_data.map(item => {
								return Object.assign({ ...item }, {
									'in_service': true,
									'max_loading_rate': 1
								})
							})
						}
					}).then(res => {
						if (res.code == 1 && res.func_result.code == 1) {
							message.success(res.func_result.message)
							emit('refreshGis', res.func_result.channel_data)
						}
					}).catch(() => {
						storeModal.hiddenModal()
					})
				} else {
					const relation_data = {}
					state.equipmentList.forEach(item => {
						relation_data[item.value] = item.data
					})
					relation_data['trafo'] = state.trafo_data
					basicApi({
						'import_string_func': 'teapgis:add_station',
						'func_arg_dict': {
							'tc_file_path': route.query.filePath,
							'station_name': formState.name,
							'station_type': formState.type,
							'vn_kv': formState.vn_kv,
							'bus_data_list': state.bus_data,
							'lat': props.data.lat,
							'lon': props.data.lon,
							'relation_data': relation_data
						}
					}).then(res => {
						if (res.code == 1 && res.func_result.code == 1) {
							message.success(res.func_result.message)
							emit('refreshGis', res.func_result.station_data)
						}
					}).catch(() => {
						storeModal.hiddenModal()
					})
				}
			})
			.catch(error => {
				console.log('error', error)
			})
	} else {
		if (state.mode == 0) {
			if (props.type == 'lines') {
				formRefs.value.validate()
					.then(() => {
						storeModal.showModal()
						state.isEdit = false
						basicApi({
							'import_string_func': 'teapgis:update_channel',
							'func_arg_dict': {
								'tg_file_path': route.query.filePath,
								'channel_data': [
									{
										'channel_name': formState.name,
										'from_station': props.data.from_station,
										'to_station': props.data.to_station,
										'color': state.channelData.color || undefined,
										'line_types': state.channelData.line_types || undefined,
										'line_data': state.channelData.line_data.map(item => {
											return {
												'index': item.index,
												'type': item.type,
												'color': item.color || undefined,
												'vn_kv': item.vn_kv,
												'line_types': item.line_types || undefined,
												'isSequence': item.isSequence !== false
											}
										})
									}
								]
							}
						}).then(res => {
							if (res.code == 1 && res.func_result.code == 1) {
								message.success(res.func_result.message)
								emit('saveEdit', state.channelData)
							}
						})
					})
					.catch(error => {
						console.log('error', error)
					})
			} else {
				formRef.value.validate()
					.then(() => {
						storeModal.showModal()
						state.isEdit = false
						basicApi({
							'import_string_func': 'teapgis:update_station_simple',
							'func_arg_dict': {
								'tg_file_path': route.query.filePath,
								'station_id': props.data.station_id,
								'station_name': formState.name,
								'station_type': formState.type,
								'color': formState.color
							}
						}).then(res => {
							if (res.code == 1 && res.func_result.code == 1) {
								message.success(res.func_result.message)
								emit('saveEdit', formState)
							}
						}).catch(() => {
							storeModal.hiddenModal()
						})
					})
					.catch(error => {
						console.log('error', error)
					})
			}
		} else {
			formRef.value.validate()
				.then(() => {
					storeModal.showModal()
					state.isEdit = false
					if (props.type == 'lines') {
						basicApi({
							'import_string_func': 'teapgis:update_one_channel',
							'func_arg_dict': {
								'tc_file_path': route.query.filePath,
								'from_station': props.data.from_station,
								'to_station': props.data.to_station,
								'channel_name': formState.name,
								'relation_data': {
									'ac_line': state.line_data.map(item => {
										return {
											'index': item.index,
											'name': item.name,
											'from_bus': item.from_bus,
											'to_bus': item.to_bus,
											'vn_kv': item.vn_kv,
											'max_i_ka': item.max_i_ka,
											'length_km': item.length_km,
											'in_service': item.in_service == undefined ? true : item.in_service,
											'max_loading_rate': item.max_loading_rate == undefined ? 1 : item.max_loading_rate,
											'line_data_detail': item.line_data_detail || []
										}
									})
								}
							}
						}).then(res => {
							if (res.code == 1 && res.func_result.code == 1) {
								message.success(res.func_result.message)
								emit('refreshGis', res.func_result.channel_data, 'edit')
							}
						}).catch(() => {
							storeModal.hiddenModal()
						})
					} else {
						const relation_data = {}
						state.equipmentList.forEach(item => {
							relation_data[item.value] = item.data
						})
						relation_data['trafo'] = state.trafo_data
						basicApi({
							'import_string_func': 'teapgis:update_station',
							'func_arg_dict': {
								'tc_file_path': route.query.filePath,
								'station_id': props.data.station_id,
								'station_name': formState.name,
								'station_type': formState.type,
								'vn_kv': formState.vn_kv,
								'bus_data_list': state.bus_data,
								'relation_data': relation_data,
								'lat': props.data.lat,
								'lon': props.data.lon
							}
						}).then(res => {
							if (res.code == 1 && res.func_result.code == 1) {
								message.success(res.func_result.message)
								emit('refreshGis', res.func_result.station_data, 'edit')
							}
						}).catch(() => {
							storeModal.hiddenModal()
						})
					}
				})
				.catch(error => {
					console.log('error', error)
				})
		}
	}
}
const addRow = (type) => {
	state.editType = type
	if (type == 'ac_line') {
		state.addModalColumn1.find(item => item.field == 'from_bus').options = state.stationOption.find(item => item.value == formState.from_station).children
		state.addModalColumn1.find(item => item.field == 'to_bus').options = state.stationOption.find(item => item.value == formState.to_station).children
		state.addLineIndex = state.addLineIndex - 1
		state.addEquipmentData = { index: state.addLineIndex }
	} else if (type == 'bus') {
		state.addBusIndex = state.addBusIndex - 1
		state.addEquipmentData = { index: state.addBusIndex }
	} else if (type == 'trafo') {
		state.addTrafoIndex = state.addTrafoIndex - 1
		state.addEquipmentData = { index: state.addTrafoIndex }
	}
	state.optionType = 'add'
	state.addSimpleShow = true
}
const deleteRow = (type) => {
	if (type == 'ac_line') {
		openModal(t('确定删除线路吗？'), t('注：此操作将删除算例文件中的对应线路')).then(res => {
			state.line_data = state.line_data.filter(item => !state.selectedLineKeys.includes(item.index))
	    	state.selectedLineKeys = []
		}).catch(() => {

		})
	} else if (type == 'bus') {
		openModal(t('确定删除节点吗？'), t('注：此操作将删除算例文件中的对应节点和其包含设备')).then(res => {
			state.bus_data = state.bus_data.filter(item => !state.selectedBusKeys.includes(item.index))
			// 删除关联设备
			state.equipmentList.forEach(item => {
				item.data = item.data.filter(item1 => !state.selectedBusKeys.includes(item1.bus))
			})
			state.equipmentList = state.equipmentList.filter(item => item.data.length > 0)
			state.trafo_data = state.trafo_data.filter(item => !state.selectedBusKeys.includes(item.lv_bus) && !state.selectedBusKeys.includes(item.hv_bus))
			state.selectedBusKeys = []
		}).catch(() => {

		})
	} else if (type == 'trafo') {
		openModal(t('确定删除变压器吗？'), t('注：此操作将删除算例文件中的对应变压器')).then(res => {
			state.trafo_data = state.trafo_data.filter(item => !state.selectedTrafoKeys.includes(item.index))
			state.selectedTrafoKeys = []
		}).catch(() => {

		})
	}
}
const addEquipment = () => {
	state.addEuqipmentIndex = state.addEuqipmentIndex - 1
	state.optionType = 'add'
	state.addEquipmentData = { index: state.addEuqipmentIndex, table_type: undefined }
	state.addComplexShow = true
}
const removeEquipment = () => {
	openModal(t('确定删除设备吗？'), t('注：此操作将删除算例文件中的对应设备')).then(res => {
		state.equipmentList.forEach(item => {
			item.data = item.data.filter(item1 => !item.selectedEquipmentKeys.includes(item1.index))
			item.selectedEquipmentKeys = []
		})
		state.equipmentList = state.equipmentList.filter(item => item.data.length > 0)
	}).catch(() => {

	})
}
const refreshStation = () => {
	state.isReady = false
	state.isEdit = false
	initData()
}
const selectEquipmentChange = (item) => {
	return (selectedRowKeys, selectedRows) => {
		item.selectedEquipmentKeys = selectedRowKeys
	}
}
const selectLinesChange = (selectedRowKeys) => {
	state.selectedLineKeys = selectedRowKeys
}
const selectBusChange = (selectedRowKeys) => {
	state.selectedBusKeys = selectedRowKeys
}
const selectTrafoChange = (selectedRowKeys) => {
	state.selectedTrafoKeys = selectedRowKeys
}
const initData = async() => {
	storeModal.showModal()
	if (state.mode == 1) {
		state.allBusData = (await basicApi({
			'import_string_func': 'teapgis:read_from_tc',
			'func_arg_dict': {
				'file_path': route.query.filePath,
				'sheet_name': 'bus'
			}
		})).func_result.bus.data
	}
	if (props.type == 'lines') {
		if (state.mode == 1) {
			const [res1, res2] = await Promise.all([
				basicApi({
					'import_string_func': 'teapgis:list_all_station',
					'func_arg_dict': {
						'tc_file_path': route.query.filePath,
						'parent_station_id': props.zlevelId
					}
				}),
				basicApi({
					'import_string_func': 'teapgis:get_relation_sheet_index',
					'func_arg_dict': {
						'file_path': route.query.filePath,
						'ele_name': 'ac_line'
					}
				})
			])
			state.stationOption = res1.func_result.data
			state.stationOption1 = res1.func_result.data
			if (props.selectedStationId) {
				formState.from_station = props.selectedStationId
				state.stationOption2 = state.stationOption.filter(item => item.value != formState.from_station)
			} else {
				state.stationOption2 = res1.func_result.data
			}
			state.addModalColumn1 = res2.func_result.columns.filter(item => item.required)
		}
		if (!props.isAdd) {
			basicApi({
				'import_string_func': 'teapgis:get_channel',
				'func_arg_dict': Object.assign({
					'from_station': props.data.from_station,
					'to_station': props.data.to_station
				}, state.mode == 1 ? {
					'tc_file_path': route.query.filePath
				} : {
					'tg_file_path': route.query.filePath
				})
			}).then(res => {
				if (state.mode == 0) {
					formState.name = res.func_result.data.channel_name
					state.channelData = res.func_result.data
					state.channelData.line_data.forEach(item => {
						item.color = item.color || getGisStyle(item).color
						item.line_types = item.line_types || 'solid'
					})
					state.baseChannelData = deepClone(res.func_result.data)
					state.lineIndex = 0
				} else if (state.mode == 1) {
					state.baseData = deepClone(res.func_result.data)
					state.line_data = res.func_result.data.line_data
					formState.name = res.func_result.data.channel_name
					formState.from_station = res.func_result.data.from_station
					formState.to_station = res.func_result.data.to_station
				}
				state.isReady = true
			})
		} else {
			state.isReady = true
		}
	} else if (props.type == 'point') {
		const [res1, res2] = await Promise.all([
			basicApi({
				'import_string_func': 'teapgis:get_relation_sheet_index',
				'func_arg_dict': {
					'file_path': route.query.filePath,
					'ele_name': 'bus'
				}
			}),
			basicApi({
				'import_string_func': 'teapgis:get_relation_sheet_index',
				'func_arg_dict': {
					'file_path': route.query.filePath,
					'ele_name': 'trafo'
				}
			})
		])
		state.addModalColumn2 = res1.func_result.columns.filter(item => item.required)
		state.relationColumn = res1.func_result.relation_sheet_column
		state.relationOption = res1.func_result.relation_sheet_index
		state.zoneOption = res1.func_result.relation_sheet_index['zone']
		state.addModalColumn3 = res2.func_result.columns.filter(item => item.required)
		if (props.isAdd) {
			state.isReady = true
			if (props.equipmentType == 0) {
				state.stationTypeOptions = [{
					label: t('变电站'),
					value: 'station'
				}, {
					label: t('换流站'),
					value: 'converter_station'
				}, {
					label: t('开关站'),
					value: 'switching_station'
				}]
			} else if (props.equipmentType == 1) {
				state.stationTypeOptions = [{
					label: t('煤电'),
					value: 'coal'
				}, {
					label: t('气电'),
					value: 'gas'
				}]
			} else {
				if (props.equipmentType == 2) {
					formState.type = 'hydropower'
				} else if (props.equipmentType == 4) {
					formState.type = 'wind'
				} else if (props.equipmentType == 5) {
					formState.type = 'solar'
				} else if (props.equipmentType == 6) {
					formState.type = 'storage'
				} else if (props.equipmentType == 3) {
					formState.type = 'nuclear'
				} else if (props.equipmentType == 7) {
					formState.type = 'feedin'
				}
				state.stationTypeOptions = Object.keys(stationList).map(item => {
					return {
						label: t(stationList[item]),
						value: item
					}
				}).filter(item => item.value == formState.type)
			}
		} else {
			state.stationTypeOptions = Object.keys(stationList).map(item => {
				return {
					label: t(stationList[item]),
					value: item
				}
			})
			basicApi({
				'import_string_func': 'teapgis:get_station',
				'func_arg_dict': Object.assign({}, state.mode == 1 ? {
					'tc_file_path': route.query.filePath,
					'station_id': props.data.station_id
				} : {
					'tg_file_path': route.query.filePath,
					'station_id': props.data.station_id
				})
			}).then(res => {
				state.baseData = deepClone(res.func_result.data)
				state.bus_data = res.func_result.data.bus_data
				state.trafo_data = res.func_result.data.trafo_data
				if (state.mode == 0) {
					state.equipmentList = res.func_result.data.relation_data
				} else {
					state.equipmentList = res.func_result.data.relation_data.map(item => {
						return {
							...item,
							selectedEquipmentKeys: [],
							data: item.data.map(item1 => {
								return {
									...item1,
									table_type: item.value
								}
							})
						}
					})
				}
				formState.type = res.func_result.data.station_type
				formState.vn_kv = res.func_result.data.vn_kv
				formState.name = res.func_result.data.station_name
				formState.color = res.func_result.data.color
				state.isReady = true
			})
		}
	}
}
onMounted(() => {
	initData()
})
watch(() => state.bus_data, v => {
	if (v.length == 0) {
		formState.vn_kv = 0
	} else {
		formState.vn_kv = Math.max(...state.bus_data.map(item => item.vn_kv))
	}
}, { deep: true })
watch(() => state.isReady, v => {
	if (v) {
		storeModal.hiddenModal()
	}
})
defineExpose({
	refreshStation
})
</script>
<style lang="scss" scoped>
    .gis_info{
        position: absolute;
        right: 10px;
        z-index: 3;
        border: 1px solid rgb(211, 211, 211);
        border-radius: 4px;
        background: rgb(255,255,255);
		max-height: 100%;
		overflow-y: auto;
        .edit_icon{
            position: absolute;
            right: 10px;
            top: 5px;
            font-size: 22px;
            &:hover{
                color: blue;
            }
        }
        >p{
            color: rgb(71, 71, 71);
            font-family: 'SiYuan Medium',Serif;
            font-size: 16px;
            line-height: 34px;
            font-weight: 400;
            padding-left: 15px;
            border-bottom: 1px solid rgb(211, 211, 211);
            border-radius: 3px 3px 0px 0px;
            background: rgb(233, 234, 235);
        }
        .loading{
            // width: 400px;
            height: 400px;
        }
        .station_info{
            width: 500px;
            >div:first-child{
                border-bottom: 1px solid rgb(211, 211, 211);
                padding: 10px 20px;
            }
            .info_list{
                padding: 10px 20px 20px;
                >p{
                    color: rgb(157, 157, 157);
                    font-family: 'SiYuan Medium',Serif;
                    font-size: 14px;
                    font-weight: 400;
                    letter-spacing: 0px;
                    line-height: 24px;
                    margin-bottom: 10px!important;
                }
                .table_list{
                    margin-bottom: 10px;
                }
                .equipment_list{
                    padding: 5px;
                    max-height: 400px;
                    overflow-x: auto;
                    >div{
                        >p{
                            color: rgb(35, 137, 230);
                            font-family: 'SiYuan Medium',Serif;
                            font-size: 16px;
                            font-weight: 400;
                            line-height: 28px;
                            letter-spacing: 0px;
                        }
                    }
                }
                .equipment_title{
                    position: relative;
                    >p{
                        color: rgb(157, 157, 157);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        line-height: 24px;
                        margin-bottom: 10px!important;
                    }
                    >div{
                        position: absolute;
                        right: 0;
                        top: -8px;
                    }
                }
                .option_btn{
                    display: flex;
                    padding: 5px;
                    >div{
                        display: flex;
                        align-items: center;
                        margin-right: 20px;
                        color: rgb(71, 71, 71);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        height: 28px;
                        line-height: 28px;
                        text-align: center;
                        &:hover{
                            cursor: pointer;
                        }
                        >span{
                            font-size: 16px;
                            margin-right: 5px;
                        }
                    }
                    >div:last-child{
                        >span{
                            color: rgb(228, 51, 51);
                        }
                    }
                    >div:first-child{
                        >span{
                            color: rgb(35, 137, 230);
                        }
                    }
                    .disabled{
                        opacity: 0.5;
						pointer-events: none;
                    }
                }
                .option_sp{
                    >div{
                        color: rgb(35, 137, 230);
                    }
                }
            }
            &:deep(.ant-table){
                .ant-table-thead>tr{
                    th{
                        border: 1px solid rgb(233, 233, 236);
                        border-right: none;
                        color: rgb(71, 71, 71);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        letter-spacing: 0px;
                        padding: 5px 5px;
                        background: rgb(247, 247, 249);
                    }
                    >th:last-child{
                        border-right: 1px solid rgb(233, 233, 236);
                    }
                }
                .ant-table-cell{
                    padding: 5px 5px;
                }
                .ant-table-row{
                    td{
                        border: 1px solid rgb(233, 233, 236);
                        border-right: none;
                    }
                    >td:last-child{
                        border-right: 1px solid rgb(233, 233, 236);
                    }
                }
                td{
                    background-color: rgb(247, 247, 249);
                }
                .ant-table-placeholder{
                    display: none;
                }
            }
            .form_div{
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-column-gap: 20px;
            }
            &:deep(.ant-form-item){
				input[type="color"]{
					width: 100%;
				}
                .ant-form-item-label{
                    text-align: left;
                }
                .ant-form-item-control{
                    flex: auto;
                }
                .ant-form-row{
                    flex-direction: column!important;
                }
                .ant-input-number-group-wrapper{
                    width: 100%;
                }
                .ant-input-number-group-addon{
                    width: 50px;
                }
            }
        }
        .channel_info{
            width: 360px;
            >div:first-child{
                padding: 5px;
                display: flex;
                flex-direction: column;
                >div{
                    display: flex;
                }
                &:deep(){
                    .ant-form-item-label label{
                        font-size: 16px;
                        line-height: 32px;
                    }
                    .ant-input{
                        border-radius: 0;
                        // border: none;
                        // border-color: rgb(247, 247, 249);
                        padding: 0;
                        width: 80%;
                        padding-left: 10px;
                        font-size: 16px;
                    }
                }
                >p>span{
                    margin-left: 10px;
                }
                p{
                    color: rgb(71, 71, 71);
                    font-family: 'SiYuan Medium',Serif;
                    font-size: 16px;
                    line-height: 32px;
                    font-weight: 400;
                    letter-spacing: 0px;
                    padding-left: 10px;
                }
            }
            >div:last-child{
                >div{
                    >p{
                        color: rgb(157, 157, 157);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        line-height: 24px;
                    }
                    padding: 10px 15px;
                    border-top: 1px solid rgb(226, 226, 226);
                }
                >div:last-child{
                    border-bottom: 1px solid rgb(226, 226, 226);
                }
                .line_list{
                    >div{
                        padding: 15px 10px;
                        display: grid;
                        grid-template-columns: 1fr 1fr 1fr;
                    }
                }
                .line_content{
                    .line_list,.line_base,.line_graph>p{
                        color: rgb(157, 157, 157);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        line-height: 24px;
                    }
                }
                .line_base{
                    >div{
                        display: flex;
                        flex-wrap: wrap;
                        padding: 10px;
                        p{
                            color: rgb(134, 134, 134);
                            font-family: 'SiYuan Medium',Serif;
                            font-size: 14px;
                            line-height: 30px;
                            font-weight: 400;
                            letter-spacing: 0px;
                            min-width: 50%;
                        }
                    }
                }
                .line_config{
                    padding: 10px;
                    >p{
                        display: flex;
                        align-items: center;
                        line-height: 32px;
                        margin-bottom: 5px!important;
                    }
                    input{
                        width: 100px;
                        border-radius: 4px;
                        margin: 0 10px;
                    }
                    .line_color{
                        width: 100px;
                        height: 5px;
                        border-radius: 4px;
                        margin: 0 10px;
                    }
                    &:deep(){
                        .ant-select{
                            width: 100px;
                            margin: 0 10px;
                        }
                    }
                    .line_type{
                        width: 100px;
                        margin: 0 10px;
                        padding: 0 10px;
                        height: 32px;
                        background: rgb(238, 237, 237);
                        position: relative;
                        >span{
                            width: 80px;
                            display: block;
                            position: absolute;
                            top: calc(50% - 2.5px);
                        }
                    }
                }
            }
        }
        .edit_btn{
            border-top: 1px solid rgb(226, 226, 226);
            display: flex;
			justify-content: space-between;
			align-items: center;
			>p{
				color: red;
				font-weight: bolder;
				font-size: 14px;
			}
            padding: 7px 18px;
			>div{
				display: flex;
			}
            button{
                margin-left: 20px;
                font-size: 12px;
                width: 24px;
                height: 24px;
                padding: 0px;
                min-width: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
    .gis_info_mask{
        height: 100%;
        width: 100%;
        position: absolute;
        z-index: 2;
    }
</style>
