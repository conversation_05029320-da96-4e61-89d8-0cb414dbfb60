<template>
	<a-spin size="large" :spinning="state.loading">
		<div class="gis_system">
			<div v-if="state.type=='short_circuit'">
				<!-- <p>bpa文件：<span>{{ state.data.bpa_file_path }}</span></p> -->
				<!-- <p>swi文件：<span>{{ state.data.swi_file_path }}</span></p> -->
				<p>{{ $t('电压上限') }}：<span>{{ state.data.max_vn_kv }}kV</span></p>
				<p>{{ $t('电压下限') }}：<span>{{ state.data.min_vn_kv }}kV</span></p>
				<p>{{ $t('是否单相短路') }}：<span>{{ state.data.short_circuit_1?$t('是'):$t('否')  }}</span></p>
				<p>{{ $t('是否三相短路') }}：<span>{{ state.data.short_circuit_3?$t('是'):$t('否')  }}</span></p>
				<p>{{ $t('扫描范围') }}：<span>{{ state.data.select_type=='zone'? $t('分区'):$t('所有者') }}</span></p>
				<p>{{ $t('已选分区') }}：<span>{{ state.data.select_list.join('、') }}</span></p>
			</div>
			<div v-else-if="state.type=='ac_power_flow'">
				<!-- <p>bpa文件：<span>{{ state.data.bpa_file_path }}</span></p> -->
			</div>
			<div v-else>
				<!-- <p>bpa文件：<span>{{ state.data.bpa_file_path }}</span></p> -->
			</div>
		</div>
	</a-spin>
</template>
<script setup>
import { reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { GetLineJobTaskResult } from '@/api/gis'
const emit = defineEmits(['cancel', 'loading'])
const route = useRoute()
const state = reactive({
	loading: false,
	type: route.query.type,
	data: {
		'bpa_file_path': '',
		'swi_file_path': '',
		'ctr_file_path': '',
		'select_type': 'zone',
		'select_list': [],
		'min_vn_kv': 0,
		'max_vn_kv': 0,
		'short_circuit_1': undefined,
		'short_circuit_3': undefined
	}
})
onMounted(() => {
	state.loading = true
	initData()
})
const initData = () => {
	GetLineJobTaskResult({
		// task_record_id: +route.query.id,
		result_file_path: route.query.filePath,
		group: 'input'
	}).then(res => {
		state.data = res.data
		state.loading = false
		emit('cancel')
	}).catch(() => {
		state.loading = false
	})
}
</script>
<style lang="scss" scoped>
    .gis_system{
		>div{
			p{
				font-size: 18px;
				line-height: 38px;
			}
		}
    }
</style>
