<template>
    <a-modal v-model:open="state.ifShow" wrapClassName="gis_message_modal" :afterClose="closeModal" :centered="true" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="gis_message_content">
                <WarningOutlined />
                <p>{{ $t('检测到gis相关数据缺失') }}</p>
                <div class="message_list">
                    <p v-for="(item) in state.warningMessage" :key="item.element_cn_name">
                        <span>（{{ item.element_cn_name+item.element }}）</span><span>{{ item.name }}</span>{{ $t('缺失') }}!
                    </p>
                </div>
                <a-button type="primary" @click="closeModal">{{ $t('关闭') }}</a-button>
            </div>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { reactive } from 'vue'
const emit = defineEmits(['close'])
const props = defineProps({
	data: {
		type: Array,
		default: () => []
	}
})
const state = reactive({
	ifShow: true,
	warningMessage: props.data
})
const closeModal = () => {
	emit('close')
}
</script>
<style lang="scss">
.gis_message_modal {
    .ant-modal {
        width: auto !important;
        .gis_message_content {
            width: 600px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            >span {
                font-size: 100px;
                color: rgb(253, 115, 132);
            }
            >p {
                font-size: 24px;
                font-weight: bolder;
                letter-spacing: 1px;
                line-height: 50px;
            }
            .message_list {
                border: 2px solid red;
                width: 100%;
                height: 300px;
                padding: 5px;
                overflow-y: auto;
                >p {
                    font-size: 16px;
                    color: rgb(253, 115, 132);
                }
            }
            >button {
                background-color: rgb(227, 46, 46);
                width: 120px;
                font-size: 20px;
                height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 10px 0 0;
            }
        }
    }
}
</style>
