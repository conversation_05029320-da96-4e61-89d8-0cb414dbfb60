<template>
    <div class="user-select" :style="{zoom: state.zoom}">
        <slot></slot>
    </div>
</template>
<script setup>
import { onMounted, reactive, onUnmounted } from 'vue'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const state = reactive({
	zoom: 1
})
const screenScale = () => {
	state.zoom = document.getElementsByClassName('home-body')[0].style.zoom || 1
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	if (isChromeHigh.value) window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(() => {
	if (isChromeHigh.value) {
		screenScale()
		window.addEventListener('resize', debouncedScreenScale)
	}
})
</script>
