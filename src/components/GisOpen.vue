<template>
    <a-modal wrapClassName="GisOpen" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
                <div>
                    <div class="modal_top">
                        <p>{{ $t('打开') }}</p>
                        <close-outlined class="pointer" @click="closeModal" />
                    </div>
                    <div class="modal-content">
                        <div>
                            <div>
                                <p>{{ $t('tg文件') }}</p>
                                <div class="upload_input">
                                    <a-upload
                                        v-model:file-list="state.fileList"
                                        :beforeUpload="()=>false"
                                        :showUploadList="false"
                                        accept=".tg"
                                        name="file"
                                        :maxCount="1"
                                        @change="changeFile"
                                    >
                                        <div class="upload relative">
                                            <div>
                                                <p>{{ state.gisName?state.gisName.split('.')[0]: state.fileList[0]?state.fileList[0].name:' ' }}</p>
                                            </div>
                                            <a-button class="upload_btn" type="primary">
                                                <template #icon><folder-open-filled /></template>
                                                {{ $t('打开') }}</a-button>
                                        </div>
                                    </a-upload>
                                </div>
                                <div class="upload_div">
                                    <p>{{ $t('数据文件') }}</p>
                                    <a-upload
                                        v-if="state.tcFileList.length == 0"
                                        v-model:file-list="state.tcFileList"
                                        name="file"
                                        @change="changeTc"
                                        accept=".tc,.tr"
                                        :multiple="false"
                                        :beforeUpload="()=>false"
                                        :maxCount="1"
                                    >
                                        <p class="upload_p">
                                            <upload-outlined></upload-outlined>
                                            {{ $t('上传文件') }}
                                        </p>
                                    </a-upload>
                                    <p class="file_p" v-else>
                                        {{ state.tcFileList[0].name }}
                                        <DeleteOutlined @click="deleteTcFile" />
                                    </p>
                                </div>
                                <div :class="state.tcDisabled?'list list_disabled':'list'">
                                    <div>
                                        <p :class="state.type==0?'active':''" @click="changeType(0)">{{ $t('编辑器') }}</p>
                                        <p :class="state.type==1?'active':''" @click="changeType(1)">{{ $t('结果列表') }}</p>
                                    </div>
                                    <div class="list_content" v-show="state.type==1">
                                        <p :class="state.resultTcId==item.id?'active':''" @click="selectResult(item)" v-for="(item) in state.resultList" :key="item.id">{{ item.case_file_name }}</p>
                                    </div>
                                    <div class="list_content" v-show="state.type==0">
                                        <p :class="state.editTcFile==item.filePath?'active':''" @click="selectEdit(item)" v-for="(item) in editList" :key="item.filePath">{{ item.title }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal_btn">
                            <a-button :disabled="btnDisabled" @click="confirmAdd" type="primary">{{ $t('确认') }}</a-button>
                            <a-button @click="closeModal">{{ $t('取消') }}</a-button>
                        </div>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { basicApi } from '@/api/exampleApi'
import { getTaskTableApi, UploadTempFile } from '@/api/index'
import { UploadTgFile } from '@/api/gis'
import { routeStore } from '@/store/routeStore'
const storeRoute = routeStore()
const { routeTabs } = storeToRefs(storeRoute)
const props = defineProps({
	gisPath: { type: String, default: undefined },
	gisName: { type: String, default: undefined }
})
const state = reactive({
	ifShow: true,
	type: 0,
	loading: false,
	gisName: props.gisName,
	tcFileList: [],
	fileList: [],
	resultList: [],
	tcDisabled: false,
	editTcFile: undefined,
	resultTcFile: undefined,
	tcFilePath: undefined,
	tgFilePath: props.gisPath || undefined
})
const emit = defineEmits(['close', 'confirm'])
const btnDisabled = computed(() => {
	if (!state.tgFilePath) return true
	if (state.tcFilePath) return false
	if (state.type == 1 && state.resultTcFile == undefined) return true
	if (state.type == 0 && state.editTcFile == undefined) return true
	return false
})
const closeModal = () => {
	emit('close')
}
const selectEdit = (item) => {
	state.editTcFile = item.filePath
}
const selectResult = (item) => {
	state.resultTcFile = item.result_file_path
	state.resultTcId = item.id
}
const deleteTcFile = () => {
	state.tcFileList = []
	state.tcFilePath = undefined
	state.tcDisabled = false
}
const changeType = (val) => {
	if (state.type == val) return
	state.type = val
	if (state.type == 1) {
		initResult()
	}
}
const editList = computed(() => {
	return routeTabs.value.filter(item => {
		return item.type == 'isEditor' || item.type == 'firstSave'
	})
})
const changeTc = async({ file, fileList }) => {
	if (fileList.length == 0) return
	const formdata = new FormData()
	formdata.append('file', file)
	state.loading = true
	UploadTempFile({}, formdata).then(res => {
		state.loading = false
		if (res.code == 1) {
			state.tcFilePath = res.file_path
		}
	}).catch(() => {
		state.loading = false
	})
	state.tcDisabled = true
}
const changeFile = async({ file, fileList }) => {
	if (fileList.length == 0) return
	state.gisName = undefined
	if (navigator.userAgent.includes('Electron')) {
		state.tgFilePath = state.fileList[0].originFileObj.path
	} else {
		const formdata = new FormData()
		formdata.append('file', file)
		state.loading = true
		UploadTgFile({}, formdata).then(res => {
			state.loading = false
			if (res.code == 1) {
				state.tgFilePath = res.file_path
			}
		}).catch(() => {
			state.loading = false
		})
	}
}
const getFilePath = () => {
	if (state.tcFileList.length == 0) {
		if (state.type == 1) {
			return state.resultTcFile
		} else {
			return state.editTcFile
		}
	} else {
		return state.tcFilePath
	}
}
const confirmAdd = () => {
	basicApi({
		'import_string_func': 'teapgis:open_gis_file',
		'func_arg_dict': {
			'tg_file_path': state.tgFilePath,
			'tc_file_path': getFilePath()
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			emit('confirm', res.func_result, 'open')
		}
	})
}
const initResult = () => {
	getTaskTableApi({}).then(res => {
		if (res.code == 1) {
			state.resultList = res.finished_data_list.filter(item => ![101, 102, 103, 104].includes(item.job_type_id))
		}
	})
}
onMounted(async() => {
	// initResult()
})
</script>
<style lang="scss">
    .GisOpen{
        .ant-modal{
            width: auto!important;
        }
        .modal-content{
            padding: 20px 30px 70px;
            >div:first-child{
                margin-bottom: 20px;
                width: 600px;
                .upload_input{
                    border: 1px solid #d9d9d9;
                    border-radius: 8px;
                    margin-bottom: 10px;
                    .ant-upload-wrapper{
                        display: block;
                        height: 32px;
                    }
                    .upload{
                        display: flex;
                        align-items: center;
                        padding-left: 15px;
                        height: 32px;
                        width: 600px;
                        .upload_btn{
                            position: absolute;
                            right: 0px;
                        }
                    }
                }
                >div>p,.upload_div>p:first-child{
                    color: rgb(80, 79, 74);
                    font-size: 18px;
                    font-weight: 500;
                    line-height: 35px;
                    letter-spacing: 1px;
                }
                .upload_div{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .upload_p{
                        font-size: 16px;
                        color: rgb(54, 120, 191);
                        &:hover{
                            cursor: pointer;
                        }
                    }
                }
                .list{
                    >div:first-child{
                        display: flex;
                        border-radius: 12px 12px 0px 0px;
                        background: rgb(217, 217, 217);
                        height: 35px;
                        >p{
                            font-size: 16px;
                            line-height: 32px;
                            width: 50%;
                            text-align: center;
                            margin: 4px 4px 0!important;
                            border-radius: 12px 12px 0px 0px;
                            &:hover{
                                cursor: pointer;
                                color: rgb(54, 120, 191);
                                background: #fff;
                            }
                        }
                        .active{
                            color: rgb(54, 120, 191);
                            background: #fff;
                        }
                    }
                    .list_content{
                        height: calc(300px - 35px);
                        padding: 10px;
                        overflow-y: auto;
                        border: 1px solid rgb(196, 196, 196);
                        border-top: none;
                        border-radius: 0px 0px 12px 12px;
                        >p{
                                &:hover{
                                    cursor: pointer;
                                    color: rgb(54, 120, 191);
                                }
                        }
                        .active{
                                color: rgb(54, 120, 191);
                        }
                    }
                }
                .list_disabled{
                    pointer-events: none;
                    opacity: 0.5;
                }
                .file_p{
                    font-size: 16px;
                    span{
                        &:hover{
                            cursor: pointer;
                            color: red;
                        }
                    }
                }
            }
        }
    }
</style>
