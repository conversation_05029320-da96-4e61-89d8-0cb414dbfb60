<template>
  <a-modal wrapClassName="wind_solar" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
      <div class="modal_top">
        <p>{{props.type=='solar' ? `${ $t('光伏生成') }` : `${ $t('风电生成') }`}}</p>
        <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large">
        <div class="modal_content relative">
              <div>
                <p>{{ $t('预测条件设置') }}</p>
                <div class="modal_checked">
                    <a-radio-group v-model:value="state.type">
                        <a-radio :value="1">{{ $t('区域') }}</a-radio>
                        <a-radio :value="2">{{ $t('经纬度') }}</a-radio>
                    </a-radio-group>
                </div>
                <div class="modal_setting">
                    <div>
                        <p>{{ $t('预测年份') }}:</p>
                        <a-select v-model:value="state.year" :options="state.yearList">
                        </a-select>
                    </div>
                    <div v-if="state.type==1">
                        <p>{{ $t('省/直辖市') }}:</p>
                        <a-select @change="changeProvince" allowClear v-model:value="state.province" :options="state.provinceList">
                        </a-select>
                    </div>
                    <div v-if="state.type==1">
                        <p>{{ $t('市') }}</p>
                        <a-select v-model:value="state.city" allowClear :options="state.cityList">
                        </a-select>
                    </div>
                    <div v-if="state.type==2">
                        <p>{{ $t('经度') }}:</p>
                        <a-input-number min="0" max="180" v-model:value="state.lat" :controls="false">
                        </a-input-number>
                    </div>
                    <div v-if="state.type==2">
                        <p>{{ $t('纬度') }}:</p>
                        <a-input-number min="0" max="90" v-model:value="state.lon" :controls="false">
                        </a-input-number>
                    </div>

                </div>
                <p>{{ $t('参数设置') }}（{{ $t('可选') }}）</p>
                <div class="modal_setting modal_setting_sp">
                    <div>
                      <p>{{ $t('年目标利用小时数') }}:</p>
                      <a-input-number min="0" max="8760" v-model:value="state.trg_hour" :controls="false">
                      </a-input-number>
                    </div>
                </div>
              </div>
              <div>
                <p>{{ $t('关联场站') }}（{{ $t('可选') }}）</p>
                <div class="modal_transfer">
                    <Transfer  v-if="state.transferShow" :data="state.stationList" @confirm="confirmTransfer"></Transfer>
                </div>
              </div>
        </div>
        <div class="modal_btn">
          <a-button @click="confirm" type="primary">{{ $t('确认') }}</a-button>
          <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
        </div>
      </a-spin>
    </screen-scale>
    <curve-modal v-if="state.modalShow" :type="props.type" :state="state" @close="close"></curve-modal>
  </a-modal>
</template>
<script setup>
/*eslint-disable no-unused-vars*/
import { onMounted, reactive, h, onUnmounted } from 'vue'
import { getBaseDataApi } from '@/api/exampleApi'
import { useRoute } from 'vue-router'
import { TmyDownload } from '@/api/index'
import { CloseOutlined } from '@ant-design/icons-vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import message from '@/utils/message'
import { t } from '@/utils/common'

const storeSetting = settingStore()
const { provinceList, cityObj } = storeToRefs(storeSetting)
const route = useRoute()
const props = defineProps({
	type: {
		type: String
	}
})
const state = reactive({
	ifShow: true,
	isEdit: false,
	zoom: 1,
	type: 1,
	year: undefined,
	caseYear: undefined,
	province: undefined,
	city: undefined,
	lat: undefined,
	lon: undefined,
	loading: false,
	tc_filename: undefined,
	fileList: [],
	provinceList,
	trg_hour: undefined,
	cityObj,
	cityList: [],
	stationList: [],
	yearList: [],
	index_list: [],
	modalShow: false,
	transferShow: false,
	confirm: false,
	lineStr: undefined,
	lineData: [],
	download_file_name: undefined
})
const leftColumns = [{
	dataIndex: 'label',
	title: ''
}]
const rightColumns = [{
	dataIndex: 'label',
	title: ''
}]
const emit = defineEmits(['close', 'refresh'])
const renderFunc = (item) => {
	return h('p', { onDblclick: () => handleDoubleClick(item) }, item.label)
}
const handleDoubleClick = (item) => {
	if (state.index_list.includes(item.key)) {
		state.index_list = state.index_list.filter(items => items != item.key)
	} else {
		state.index_list.push(item.key)
	}
}
const getRowSelection = ({
	disabled,
	selectedKeys,
	onItemSelectAll,
	onItemSelect
}) => {
	return {
		getCheckboxProps: item => ({
			disabled: disabled || item.disabled
		}),
		onSelectAll(selected, selectedRows) {
			const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({
				key
			}) => key)
			onItemSelectAll(treeSelectedKeys, selected)
		},
		onSelect({
			key
		}, selected) {
			onItemSelect(key, selected)
		},
		selectedRowKeys: selectedKeys
	}
}
const dblAdd = (e, a) => {
	if (a == 'left') {
		state.index_list.push(e)
	} else {
		state.index_list = state.index_list.filter(item => item != e)
	}
}
const closeModal = () => {
	emit('close')
}
const filterOption = (inputValue, option) => {
	return option.label.indexOf(inputValue) > -1
}
const changeProvince = () => {
	state.city = undefined
	state.cityList = state.cityObj[state.province]
}

const confirmTransfer = (val) => {
	state.index_list = val.map(item => item.key)
}

const close = (val) => {
	state.modalShow = false
	if (val) {
		emit('refresh')
	}
}
const confirm = async() => {
	if (state.type == 1 && !state.province) {
		message.warning(t('请选择省/直辖市'))
		return
	}
	if (state.type == 2 && (!state.lat || !state.lon)) {
		message.warning(t('请完整填写经纬度坐标'))
		return
	}
	if (state.year == undefined) {
		message.warning(t('请选择预测年份'))
		return
	}
	state.loading = true
	TmyDownload(Object.assign({
		job_type: props.type,
		year: state.year,
		trg_hour: state.trg_hour
	},
	state.year == 0 ? {
		tc_filename: state.tc_filename
	} : {},
	state.type == 1 ? {
		province: state.province,
		city: state.city
	} : {
		lat: state.lat,
		lon: state.lon
	}
	)).then(res => {
		state.loading = false
		state.lineData = res.data_list
		state.lineStr = res.data_str
		state.caseYear = res.year
		state.download_file_name = res.download_file_name
		state.modalShow = true
	}).catch(() => {
		state.loading = false
	})
}
onMounted(() => {
	state.isEdit = (route.query.type == 'isEditor' || route.query.type == 'isNewBuilt' || route.query.type == 'firstSave' || route.query.type == 'isResultBuilt')
	state.tc_filename = route.query.filePath
	state.yearList = (state.isEdit ? [{
		value: 0,
		label: t('请选择预测年份')
	}] : []).concat([...Array(27)].map((item, index) => {
		return {
			// value: index + new Date().getFullYear() + 1,
			// label: index + new Date().getFullYear() + 1
			value: index + 2019,
			label: index + 2019
		}
	}))
	if (provinceList.value.length == 0) {
		getBaseDataApi({
			'import_string_func': 'teapcase:list_all_city',
			'func_arg_dict': {
			}
		}).then(res => {
			if (res.code == 1) {
				storeSetting.changeAreaList(res.func_result)
			}
		})
	}

	if (state.isEdit) {
	  state.loading = true
		getBaseDataApi({
			'import_string_func': 'teapcase:read_name_col_from_tc',
			'func_arg_dict': {
				file_name: state.tc_filename,
				sheet_names: props.type == 'wind' ? ['wind', 'wind_plan'] : ['solar', 'solar_plan']
			}
		}).then(res => {
			state.stationList = Object.keys(res.func_result.data).reduce((a, b) => {
				return a.concat(res.func_result.data[b].map((item, index) => {
					return {
						label: item.name,
						key: b + '-' + (item.index).toString()
					}
				}))
			}, [])
			// state.stationList = res.func_result.data[props.type].map((item, index) => {
			// 	return {
			// 		label: item.name,
			// 		key: (item.index).toString()
			// 	}
			// })
			state.transferShow = true
			state.loading = false
		}).catch(() => {
			state.loading = false
		})
	}
})

</script>
<style lang="scss">
  .wind_solar{
    .ant-modal{
      width: auto!important;
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 10px 30px 80px;
            display: flex;
            justify-content: space-between;
            >div{
              width: 400px;
              >p{
                  font-size: 18px;
                  font-weight: bolder;
                  line-height: 40px;
              }
            }
            >div:first-child{
              margin-right: 20px;
            }
            >div:last-child{
              width: 500px;
            }
            .modal_setting{
                background: rgb(248, 248, 248);
                margin-bottom: 50px;
                padding: 15px 40px 15px;
                >div{
                    margin: 10px 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    p{
                      line-height: 30px;
                      text-align: right;
                      width: 70px;
                    }
                    .ant-select-selector{
                        width: 220px;
                    }
                    .ant-input-number{
                        width: 220px;
                    }
                }
                >div:last-child{
                  margin-bottom: 5px;
                }
            }
            .modal_setting_sp{
              margin-bottom: 0px;
              >div{
                p{
                  width: auto;
                }
                .ant-input-number{
                  width: 180px;
                }
              }
            }
            .modal_transfer{
              background: rgb(248, 248, 248);
              padding: 15px 20px 20px;
              .ant-transfer-list{
                height: 365px;
                width: 220px;
                background-color: #fff;
              }
              // .ant-table-cell{
              //   padding:4px 10px!important;
              // }
              // .ant-table-body{
              //   min-height: 223px;
              // }
              // .ant-empty-normal{
              //   margin-block: 72px;
              // }
            }
            .modal_checked{
                padding:10px 20px;
                margin-bottom: 10px;
            }
            .modal_upload{
                padding: 10px 0;
                height: 55px;
                display: flex;
                align-items: center;
                .modal_upload_name{
                    width: 280px;
                    display: flex;
                    justify-content: space-between;
                    border: 1px solid rgb(244, 244, 244);
                    border-radius: 5px;
                    padding: 5px;
                    p{
                        color: var(--base-color);
                        font-size: 15px;
                    }
                    span{
                        font-size: 15px;
                        background-color: rgb(244, 244, 244);
                        padding: 3px;
                        border-radius: 5px;
                        &:hover{
                            cursor: pointer;
                        }
                    }
                }
            }
          }
          .modal_btn{
            button{
                letter-spacing: 0;
            }
          }
        }
      }
    }
  }
</style>

