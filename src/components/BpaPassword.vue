<template>
  <a-modal
    wrapClassName="modal_password"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="475px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('修改密码') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content">
        <div>
          <a-input-password v-model:value="state.origin_pwd" :placeholder="$t('输入原始密码')" />
        </div>
        <div>
          <a-input-password v-model:value="state.new_pwd" :placeholder="$t('设置新密码')" />
        </div>
        <div>
          <a-input-password v-model:value="state.confirm_pwd" :placeholder="$t('再次输入密码')" />
        </div>
        <div class="modal_btns_box">
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('保存') }}</a-button>
          <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
        </div>
      </div>

    </div>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'
// import { useRoute } from 'vue-router'
import { Modal } from 'ant-design-vue'
import message from '@/utils/message'
import { modifyPwd } from '@/api/index'
import { t } from '@/utils/common'

// const route = useRoute()

const emits = defineEmits(['close', 'confirm'])

const state = reactive({
	visible: true,
	origin_pwd: '',
	new_pwd: '',
	confirm_pwd: ''
})
// const sourceValue = ref([])

const handleOk = () => {
	if (state.origin_pwd == '' || state.new_pwd == '' || state.confirm_pwd == '') return message.warning(t('请填写完整信息') + '！')
	if (state.new_pwd !== state.confirm_pwd) return message.warning(t('请保持再次输入密码与新密码一致') + '！')
	modifyPwd({
		'origin_pwd': state.origin_pwd,
		'new_pwd': state.new_pwd,
		'confirm_pwd': state.confirm_pwd
	}).then(res => {
		if (res.code == 1) {
			message.success(res.msg || t('密码修改成功') + '！')
			emits('confirm')
		} else {
			Modal.confirm({
				title: t('提示'),
				content: `${res.data.msg}`,
				okText: t('再次输入'),
				cancelText: t('取消'),
				onOk() {
					// message.success('修改成功！')
				},
				onCancel() {
					// message.success('修改失败！')
				}
			})
		}
	})
}

const closeModal = () => {
	emits('close')
}

onMounted(() => {

})

</script>
<style lang="scss" scoped>
  .modal_password{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: left;
            >div {
              margin: 20px;
            }

          }

          .modal_btns_box{
            // margin-top: 30px;
            text-align: center;
            button{
              width: 120px;
              height: 35px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
