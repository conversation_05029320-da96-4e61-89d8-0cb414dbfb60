<!-- 容量平衡 -->
<template>
    <div class="global-main-capacity">
        <div class="main-left">
            <p>{{ $t('参数设置') }}</p>
            <div :class="['parameterSet']">
                <div>
                    <p>{{ $t('备用率') }}</p>
                    <a-input-number v-model:value="stateMore.general.reserve_cof" :min="0" addon-after="%"></a-input-number>
                </div>
                <div>
                    <p>{{ $t('考虑规划机组') }}</p>
                    <a-radio-group v-model:value="stateMore.general.cons_plan_gen" button-style="solid">
                        <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                        <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                    </a-radio-group>
                </div>
                <div>
                    <p>{{ $t('需求侧响应率') }}</p>
                    <a-input-number v-model:value="stateMore.general.load_demand_response_cof" :min="0" addon-after="%"></a-input-number>
                </div>
                <div>
                    <p>{{ $t('计及下半年投产机组') }}</p>
                    <a-radio-group v-model:value="stateMore.general.cons_sechalf_gen" button-style="solid">
                        <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                        <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                    </a-radio-group>
                </div>
                <div>
                    <p>{{ $t('负荷平衡系数') }}</p>
                    <a-input-number
                        v-model:value="stateMore.general.load_balance_cof"
                        :min="0"
                        addon-after="%"
                    ></a-input-number>
                </div>
            </div>
            <p>{{ $t('仿真设置') }}</p>
            <div :class="['solutionMethod']">
                <div class="solutionMethod-top">
                    <div>
                        <p>{{ $t('仿真时间') }}</p>
                        <a-range-picker v-model:value="state.simulation_capacity_time" valueFormat="YYYY-MM-DD" @change="capacityTimechange"/>
                    </div>
                    <div></div>
                </div>
            </div>
            <p>{{ $t('案例备注') }}</p>
            <div :class="['caseRemark']">
                <a-textarea v-model:value="stateCase.description" :placeholder="$t('请输入')+'...'" :rows="2" />
            </div>
        </div>
        <div class="main-right">
            <p>{{ $t('电源出力系数表') }}</p>
            <AgGrid ref="agGridBalanceRef" :isEdit="'capacityBalance'" :isInputSearch="false"></AgGrid>
        </div>
        <div class="main-btn">
            <a-button style="width: 96px" @click="handleReset">{{ $t('重置') }}</a-button>
            <a-button :style="{'width': '96px', 'backgroundColor': '#dbe9f4'}" @click="confirm(false)">{{ $t('仅保存') }}</a-button>
            <a-button type="primary" style="width: 96px" @click="confirm(true)">{{ $t('计算') }}</a-button>
        </div>
      </div>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import Mitt from '@/utils/mitt.js'
import { useRoute } from 'vue-router'
import { onMounted, reactive, ref, watch, nextTick } from 'vue'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
// AG-Grid表格
const route = useRoute()
const agGridBalanceRef = ref(null)
const props = defineProps({
	stateSetting: {
		type: Object
	},
	state: {
		type: Object
	},
	globalActiveKey: {
		type: String
	},
	isReady: {
		type: Boolean
	}
})
const state = reactive({
	simulation_capacity_time: props.state.simulation_capacity_time
})
const stateCase = ref({

})
const stateMore = ref({
	general: {

	}
})
const emit = defineEmits(['reset', 'saveSetting'])
onMounted(() => {

})
// 容量平衡 仿真时间
const capacityTimechange = (val) => {
	stateMore.value.general.start_datetime = `${val[0]} 00:00:00`
	stateMore.value.general.end_datetime = `${val[1]} 23:00:00`
	emit('saveSetting', false, 'capacity_balance', {
		stateCase: stateCase.value,
		stateMore: stateMore.value
	})
}
const handleReset = () => {
	getCapacityBalanceData()
	emit('reset')
}
const confirm = (val) => {
	emit('saveSetting', val ? 6 : false, 'capacity_balance', {
		stateCase: stateCase.value,
		stateMore: stateMore.value
	})
}
const updataAgTable = () => {
	nextTick(() => {
		agGridBalanceRef.value.saveCapacityBalance()
	})
}
Mitt.on('updataAgTable', updataAgTable)
// 获取容平 电源出力系数表
const getCapacityBalanceData = () => {
	basicApi({
		'import_string_func': 'teapcase_cb:get_capacity_balance_power_cof_table',
		'func_arg_dict': {
			'file_name': route.query.filePath
		}
	}).then(res => {
		if (res.code == 1) {
			const { capacity_balance_power_cof } = res.func_result
			nextTick(() => {
				agGridBalanceRef.value.setTimeDeatilData(capacity_balance_power_cof)
			})
		}
	})
}
Mitt.on('getCapacityBalanceData', getCapacityBalanceData)
watch(() => props.isReady, (val) => {
	stateCase.value = props.stateSetting.case_info
	stateMore.value = props.stateSetting.capacity_balance
	state.simulation_capacity_time = props.state.simulation_capacity_time
}, { immediate: true })
watch(() => props.globalActiveKey, (val) => {
	if (val === 'capacity_balance') {
		getCapacityBalanceData()
	}
}, { immediate: true })
</script>
<style lang="scss">

</style>

