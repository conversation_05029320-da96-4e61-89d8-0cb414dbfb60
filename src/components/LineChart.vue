<template>
  <a-modal wrapClassName="modal_line_chart" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <div class="user-select" :style="{zoom: state.zoom}">
      <div class="modal_top">
        <p>{{$t(props.name)}}{{ $t('预览') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
    </div>
    <a-spin :spinning="state.loading" size="large" :tip="$t('接口请求中')">
      <div class="modal_content relative">
        <a-button type="primary" ghost @click="state.confidenceShow=true">{{ $t('置信度分析') }}</a-button>
        <div class="line" ref="line" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;height:${state.height}px;`"></div>
      </div>
    </a-spin>
    </div>
    <!-- 置信度分析 -->
    <confidence-analyse
      v-if="state.confidenceShow"
      :index="props.index"
      @close="state.confidenceShow=false"
    >
    </confidence-analyse>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive, ref, inject, markRaw, onUnmounted } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getLineOptionSole } from '@/utils/teap'
import { t } from '@/utils/common'
import { getBaseDataApi } from '@/api/exampleApi'
import { useRoute } from 'vue-router'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const route = useRoute()
const props = defineProps({
	index: {
		type: Number
	},
	name: {
		type: String
	}
})
const echarts = inject('ec')
const lineChart = ref()
const state = reactive({
	ifShow: true,
	confidenceShow: false,
	loading: false,
	zoom: 1,
	zooms: 1,
	scales: 1,
	height: 600,
	lineData: []
})
const line = ref()
const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}
const initLine = (data) => {
	const option = getLineOptionSole(data, t(props.name))
	lineChart.value.setOption(option)
}
const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		state.zoom = root
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	state.height = 600 * root
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(() => {
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	lineChart.value = markRaw(echarts.init(line.value))
	state.loading = true
	getBaseDataApi({
		'import_string_func': 'teapcase:read_one_ts_value_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'row_id': props.index
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			initLine(res.func_result)
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
})
</script>
<style lang="scss">
.modal_line_chart{
  .ant-modal{
    width: auto!important;
    // height: 80vh;
    .ant-modal-body{
      >div{
        .modal_content{
          height: 680px;
		  width: 1000px;
          padding: 30px 30px 30px;
          .line{
                // width: 100%;
              	transform-origin: 0 0;
          }
        }
      }
    }
  }
}
</style>

