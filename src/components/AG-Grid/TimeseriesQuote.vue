<template>
    <a-modal
        wrapClassName="modal_quote"
        :afterClose="closeModal"
        :centered="true"
        v-model:open="state.visible"
        :footer="null"
        :closable="false"
        :maskClosable="false"
    >
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('设备引用列表') }}</p>
                <close-outlined class="pointer" @click="closeModal" />
            </div>
            <div class="modal_content relative">
                <a-table
                class="ant-table-striped"
                size="middle"
                :columns="state.columns"
                :data-source="state.tableData"
                :customRow="customRow"
                :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
                :scroll="{ y: 500 }"
                :pagination="false"
                bordered
                />
            </div>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { reactive, defineEmits, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import { t } from '@/utils/common'

// const props = defineProps({
// 	targetValue: {
// 		type: Array,
// 		default: () => []
// 	}
// })

const emits = defineEmits(['cancel', 'confirm'])
const route = useRoute()

const state = reactive({
	visible: true,
	isMultiple: 'multiple',
	columns: [
		{
			title: t('设备名称'),
			dataIndex: 'relation_name',
			key: 'relation_name',
			align: 'center',
			ellipsis: true
		},
		{
			title: t('设备类型'),
			dataIndex: 'relation_sheet_name',
			key: 'relation_sheet_name',
			align: 'center',
			ellipsis: true
		}
	],
	tableData: []
})

// sourceValue.value = props.targetValue
// const options = ref([])

// const filterOption = (input, option) => {
// 	return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
// }

// const handleCancel = () => {
// 	emits('cancel')
// }
const closeModal = () => {
	emits('cancel')
}

const customRow = (record, index) => {
	return {
		onClick: () => {
			emits('cancel')
			Mitt.emit('handleItemizedDelQuote', record)
		}
	}
}

const getQuoteData = (id) => {
	basicApi({
		'import_string_func': 'teapcase:get_ts_relation_info',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'row_id': id
		}

	}).then(res => {
		if (res.code == 1) {
			const { data } = res.func_result
			// options.value = data['timeseries'].map(item => {
			// 	return {
			// 		key: item.index,
			// 		title: item.name
			// 	}
			// })
			state.tableData = data
		}
	})
}

defineExpose({ getQuoteData })

onMounted(() => {
	// getReadNameColData()
})

</script>
<style lang="scss">
  .modal_quote{
        .ant-modal{
            width: auto!important;
            .ant-modal-body{
                >div{
                    .modal_content{
                        width: 475px;
                        padding: 17px 35px;
                        text-align: center;
                        [data-doc-theme='light'] .ant-table-striped :deep(.table-striped) td {
                        background-color: #fafafa;
                        }
                        [data-doc-theme='dark'] .ant-table-striped :deep(.table-striped) td {
                        background-color: rgb(29, 29, 29);
                        }
                    }
                }
            }
        }
  }
</style>
