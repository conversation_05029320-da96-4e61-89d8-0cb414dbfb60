<template>
  <a-modal
    wrapClassName="modal_timeAdd"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="visible"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('时序新增') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <div class="btn_box">
          <span>{{ $t('名称') }}：{{ state.timeseriesObj.name }}</span>
          <span class="tableBar_btn"> {{ $t('曲线类型') }}：{{ state.ts_type[state.timeseriesObj.type] }}</span>
          <span class="tableBar_btn"> {{ $t('数据类型') }}：{{ state.timeseriesDataType }}</span>
          <span class="tableBar_btn"> {{ $t('计算场景') }}：{{ state.timeseriesObj.scenario }}</span>
          <!-- <span class="tableBar_btn"> 显示形式：</span> -->
          <!-- <a-radio-group
            v-model:value="state.timeseriesDisplay"
            size="small"
            button-style="solid"
            @change="changeTimeseriesDisplay"
          >
            <a-radio-button value="day">24列</a-radio-button>
            <a-radio-button value="hour">单列</a-radio-button>
          </a-radio-group> -->
          <a-button @click="handleCount" type="primary" :style="{color:'#fff',marginLeft: '15px'}" size="small">{{ $t('计算') }}</a-button>
          <a-button @click="handleReplace" type="primary" :style="{color:'#fff',marginLeft: '15px'}" size="small">{{ $t('替换') }}</a-button>
        </div>
        <div class="agClass">
          <!-- <AgGrid ref="agGridRef" :isTimeseriesDetail="true"></AgGrid> -->
          <AgGrid ref="agGridRef"></AgGrid>
        </div>
        <div class="modal_btns">
          <a-button @click="handleOk" type="primary" :style="{color:'#fff',marginLeft: '15px'}">{{ $t('保存修改') }}</a-button>
        </div>
      </div>

    </div>
  </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { ref, reactive, toRef, defineProps, defineEmits, onMounted, nextTick } from 'vue'
import message from '@/utils/message'
import { basicApi, saveBaseDataApi } from '@/api/exampleApi'
import { t } from '@/utils/common'

const props = defineProps(['visible', 'sourceType'])
const visible = toRef(props, 'visible')
// const sourceType = toRef(props, 'sourceType')
const emits = defineEmits(['cancel', 'confirm'])

// AG-Grid表格
const agGridRef = ref(null)

const state = reactive({
	timeseriesObj: {},
	ts_type: {},
	rowId: null,
	timeseriesDataType: '',
	file_name: '',
	timeseriesDisplay: 'hour', // 时序显示形式
	totalHours: 8760,
	mod_24_col_data: [],
	mod_single_col_data: [],
	mod_other_data: {}
})

const closeModal = () => {
	emits('cancel')
}

const handleCount = () => {
	nextTick(() => {
		agGridRef.value.onCount()
	})
}

const handleReplace = () => {
	nextTick(() => {
		agGridRef.value.openReplace()
	})
}

// const changeTimeseriesDisplay = () => {
// 	nextTick(() => {
// 		agGridRef.value.setTimeDeatilData(state.timeseriesDisplay == 'day' ? state.mod_24_col_data : state.mod_single_col_data)
// 	})
// }

const getTimeseriesInit = (formData, routeId, rowId) => {
	state.file_name = routeId
	state.rowId = rowId
	state.timeseriesObj = formData
	state.timeseriesDataType = formData.value_type == 'multiply' ? t('数据倍乘') : t('数据替换')
	console.log(1234, formData)

	basicApi({
		'import_string_func': 'teapcase:get_one_ts_value_template',
		'func_arg_dict': {
			'file_name': routeId,
			// "template_name": "逐时数据"
			// "template_name": "日×24时数据"
			// "template_name": "月×24时数据"
			// "template_name": "年×12月数据"
			'template_name': formData.period,
			't_range': formData.periodNum ? formData.periodNum : 3
		}
	}).then(res => {
		if (res.code == 1) {
			if (formData.period == '逐时数据') {
				const { data: { mod_24_col_data, mod_single_col_data }, ts_type } = res.func_result

				state.mod_24_col_data = mod_24_col_data
				state.mod_single_col_data = mod_single_col_data
				state.mod_24_col_data.columns.forEach((item, index) => {
					if (item.field == 'day') {
						item.width = 150
					} else {
						item.width = 80
					}
				})
				state.mod_single_col_data.columns.forEach((item, index) => {
					if (item.field == 'time') {
						item.width = 200
					} else {
						item.width = 80
					}
				})

				state.mod_24_col_data.data.forEach((item, index) => {
					item.index = index
				})
				state.mod_single_col_data.data.forEach((item, index) => {
					item.index = index
				})

				state.ts_type = ts_type.ts_type_name_map

				nextTick(() => {
					agGridRef.value.setTimeDeatilData(state.timeseriesDisplay == 'day' ? state.mod_24_col_data : state.mod_single_col_data)
				})
			} else {
				const { func_result } = res
				state.ts_type = func_result.ts_type.ts_type_name_map
				state.mod_other_data = func_result
				nextTick(() => {
					agGridRef.value.setTimeDeatilData(func_result)
				})
			}
		}
	})
}

const handleOk = () => {
	let tempData = agGridRef.value.saveTimeAddData()
	if (state.timeseriesObj.period == '逐时数据') {
		tempData = JSON.parse(JSON.stringify(tempData)).map(item => {
			delete item.index
			delete item.day
			delete item.time
			item = Object.values(item)
			return item
		}).flat(Infinity)
		if (tempData.length > 8760) {
			tempData = tempData.splice(0, 8760)
		}
	}
	handleSaveAdd(tempData)
}

const handleSaveAdd = (data) => {
	const treeNode = sessionStorage.getItem('treeType')
	let tempParams = {}
	if (treeNode.includes('-')) {
		tempParams = {
			'import_string_func': 'teapcase:save_one_ts_value_to_tc',
			'func_arg_dict': {
				'sheet_name': treeNode.split('-')[0],
				'row_id': Number(treeNode.split('-')[1]),
				'file_name': state.file_name,
				'template_name': state.timeseriesObj.period,
				'ts_name': state.timeseriesObj.name,
				'ts_type': state.timeseriesObj.type,
				'ts_value_type': state.timeseriesObj.value_type,
				'ts_scenario': state.timeseriesObj.scenario,
				'data_list': data,
				'column_list': state.timeseriesObj.period !== '逐时数据' ? state.mod_other_data.columns : state.timeseriesDisplay == 'day' ? state.mod_24_col_data.columns : state.mod_single_col_data.columns
			}
		}
	} else {
		tempParams = {
			'import_string_func': 'teapcase:save_one_ts_value_to_tc',
			'func_arg_dict': {
				'file_name': state.file_name,
				'template_name': state.timeseriesObj.period,
				'ts_name': state.timeseriesObj.name,
				'ts_type': state.timeseriesObj.type,
				'ts_value_type': state.timeseriesObj.value_type,
				'ts_scenario': state.timeseriesObj.scenario,
				'data_list': data,
				'column_list': state.timeseriesObj.period !== '逐时数据' ? state.mod_other_data.columns : state.timeseriesDisplay == 'day' ? state.mod_24_col_data.columns : state.mod_single_col_data.columns
			}
		}
	}
	saveBaseDataApi(tempParams).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message || t('时序新增成功') + '！')
			emits('cancel')
			Mitt.emit('handleRefresh')
		} else {
			message.error(res.func_result.message || t('时序新增失败') + '！')
		}
	}).catch(() => {
		// storeLoading.hiddenModal()
	})
}

defineExpose({ getTimeseriesInit })

onMounted(() => {

})

</script>
<style lang="scss">
.modal_timeAdd{
  .ant-modal{
    width: 72%!important;
    .ant-modal-body{
      >div{
        .modal_content{
          height: 540px;
          padding: 0px 30px 20px 30px;
          text-align: center;
          position: relative;
          .btn_box {
            position: absolute;
            top: 0;
            left: 30px;
            z-index: 33;
            margin-top: 8px;
            text-align: left;
            .tableBar_btn {
              margin-left: 10px;
            }
          }
        }
        .agClass {
          width: 100%;
          height: 480px;
        }
        .modal_btns {
          text-align: right;
          margin-top: 15px;
        }

      }
    }
  }
}
</style>
