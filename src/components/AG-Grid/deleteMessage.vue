<template>
  <a-modal
    wrapClassName="modal_quote"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="475px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('注意') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content relative">
        <p>{{ $t('删除“节点”将一并删除如下关联设备，请问是否仍要删除') }}？</p>
        <a-table
          class="ant-table-striped"
          size="middle"
          :columns="state.columns"
          :data-source="state.tableData"
          :customRow="customRow"
          :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
          :scroll="{ y: 400 }"
          :pagination="false"
          bordered
        />
        <div class="modal_btns">
          <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>

    </div>
  </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { reactive, defineEmits, onMounted } from 'vue'
import { t } from '@/utils/common'
// import { useRoute } from 'vue-router'
// import { basicApi } from '@/api/exampleApi'

// const props = defineProps({
// 	targetValue: {
// 		type: Array,
// 		default: () => []
// 	}
// })

const emits = defineEmits(['cancel', 'confirm'])
// const route = useRoute()

const state = reactive({
	visible: true,
	isMultiple: 'multiple',
	columns: [
		{
			title: t('设备类型'),
			dataIndex: 'sheet_name',
			key: 'sheet_name',
			align: 'center',
			ellipsis: true
		},
		{
			title: t('名称'),
			dataIndex: 'name',
			key: 'name',
			align: 'center',
			ellipsis: true
		}
	],
	tableData: []
})

// sourceValue.value = props.targetValue
// const options = ref([])

// const filterOption = (input, option) => {
// 	return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
// }

const handleOk = () => {
	emits('confirm')
}
const closeModal = () => {
	emits('cancel')
}

const customRow = (record, index) => {
	return {
		onClick: () => {
			console.log(record)
			emits('cancel')
			Mitt.emit('handleItemizedDelQuote', record)
		}
	}
}

const getDeleteInfo = (data) => {
	state.columns = data.columns.filter(item => item.field !== 'index').map(item => {
		return {
			title: item.headerName,
			dataIndex: item.field,
			key: item.field,
			align: 'center',
			ellipsis: true
		}
	})
	state.tableData = data.data
}

defineExpose({ getDeleteInfo })

onMounted(() => {
	// getReadNameColData()
})

</script>
<style lang="scss" scoped>
  .modal_quote{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            [data-doc-theme='light'] .ant-table-striped :deep(.table-striped) td {
              background-color: #fafafa;
            }
            [data-doc-theme='dark'] .ant-table-striped :deep(.table-striped) td {
              background-color: rgb(29, 29, 29);
            }
            p {
              font-size: 14px;
              color: #f74242;
              line-height: 24px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
