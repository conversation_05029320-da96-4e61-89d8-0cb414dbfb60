<template>
  <!-- <div class="ag-theme-alpine" style="height: 500px;"></div> -->
  <div class="ag-grid-body">
    <div class="ag-searchinput" v-if="props.isInputSearch">
      <span>{{ $t('搜索') }}：</span>
      <a-input v-model:value="searchValue" :placeholder="$t('请输入')" @change="onFilterTextBoxChanged()" :style="{'width': '200px'}" />
    </div>
    <ag-grid-vue
      :treeData="props.istree"
      :getDataPath="getDataPath"
      :style="tableStyle"
      class="ag-theme-quartz"
      :headerHeight="35"
      :rowHeight="30"
      :scrollbarWidth="scrollBarWidth"
      :columnDefs="columnDefs"
      @grid-ready="onGridReady"
      :defaultColDef="defaultColDef"
      :columnTypes="columnTypes"
      :rowData="rowData"
      :rowSelection="rowSelection"
      :rowMultiSelectWithClick="false"
      :suppressRowClickSelection="true"
      :enableRangeSelection="true"
      :undoRedoCellEditing="true"
      :enableCellChangeFlash="false"
      :rowDragManaged="false"
      :getRowStyle="getRowStyle"
      :autoGroupColumnDef="autoGroupColumnDef"
      :tooltipShowDelay="tooltipShowDelay"
      :localeText="localeText"
      :getRowId="getRowId"
      :statusBar="statusBar"
      :processCellForClipboard="processCellForClipboard"
      :processDataFromClipboard="processDataFromClipboard"
      @selection-changed="onSelectionChanged"
      @cellValueChanged="onCellValueChanged"
      @cellEditingStarted="onCellEditingStarted"
      @rowEditingStarted="onRowEditingStarted"
      @pasteEnd="onPasteEnd"
      @filterChanged="onFilterChanged"
      :showContextMenu="showContextMenu"
    ></ag-grid-vue>
  </div>
  <!-- 时序数据源 -->
  <SourceModal v-if="state.sourceTimeVisible"
    :headerName="state.headerName"
    :col_source="state.col_source"
    :targetValue="state.sourceValue"
    @confirm="handleConfirmTime"
    @cancel="handleCancelTime"
    @add="timeseriesAdd(false)"
  />
  <!-- 母线数据源 -->
  <SourceBusModal v-if="state.sourceVisible"
    @confirm="handleSaveSource"
    @cancel="state.sourceVisible = false"
  />
  <!-- 所属城市 -->
  <SourceCityModal v-if="state.sourceCityVisible"
    @confirm="handleCityConfirm"
    @cancel="state.sourceCityVisible = false"
  />
  <!-- 储能数据源 -->
  <SourceStorageModal v-if="state.storageVisible"
    :sourceTitle="state.sourceTitle"
    :sourceVal="state.sourceVal"
    @confirm="handleSaveStorage"
    @cancel="state.storageVisible = false"
  ></SourceStorageModal>
  <!--类型详情 -->
  <SourceTypeModal v-if="state.typeVisible"
    ref="sourceTypeRef"
    @confirm="handleSaveType"
    @cancel="state.typeVisible = false"
  />
  <!-- 替换 -->
  <ReplaceModal v-if="state.replaceVisible"
    @confirm="handleSaveReplace"
    @cancel="state.replaceVisible = false"
  ></ReplaceModal>
   <!-- 计算 -->
   <CountWay v-if="state.countWayVisible"
    @confirm="handleSaveCount"
    @cancel="state.countWayVisible = false"
  ></CountWay>
  <!-- 时序详情 -->
  <TimeseriesDetail v-if="state.timeseriesVisible"
    ref="timeseriesRef"
    v-model:open="state.timeseriesVisible"
    @cancel="timeseriesDetailClose"
  />
  <!-- 时序引用 -->
  <TimeseriesQuote v-if="state.timeseriesQuoteVisible"
    ref="timeseriesQuoteRef"
    v-model:open="state.timeseriesQuoteVisible"
    @cancel="state.timeseriesQuoteVisible = false"
  />
  <!-- 时序曲线新增 -->
  <TimeseriesForm v-if="state.timeseriesFormVisible"
    ref="timeseriesFormRef"
    @confirm="handleSaveTimeseriesAdd"
    @cancel="state.timeseriesFormVisible = false"
  />
  <TimeseriesAdd v-if="state.timeseriesAddVisible"
    ref="timeseriesAddRef"
    v-model:open="state.timeseriesAddVisible"
    @cancel="state.timeseriesAddVisible = false"
  />
  <!-- 类型转换 -->
   <type-change
  	v-if="state.typeChangeVisible"
	:data="state.typeChangeData"
	@confirm="confirmTypeChange"
    @close="state.typeChangeVisible = false"
   ></type-change>
  <!-- 节点删除二次确认 -->
  <delete-message v-if="state.deteleMessageVisible"
    ref="deleteMessageRef"
    v-model:open="state.deteleMessageVisible"
    @confirm="handleDeleteConfirm"
    @cancel="handleDeleteCancle"
  />
  <!-- 时序匹配 -->
  <time-match v-if="state.timeMatchShow"
    @refresh="handleTimeMatchRefresh"
    :data="state.selectedData"
    :treeValue="state.treeValue"
    @close="state.timeMatchShow=false">
  </time-match>
  <!-- 时序曲线图 -->
  <line-chart
    v-if="state.lineShow"
    :index="state.timeRowIndex"
    :name="state.timeRowName"
    @close="state.lineShow=false"
  >
  </line-chart>
</template>
<script setup>
// 引入样式文件
import 'ag-grid-community/styles/ag-grid.css' // Core CSS
import 'ag-grid-community/styles/ag-theme-quartz.css' // Theme
import 'ag-grid-enterprise'
import { LicenseManager } from 'ag-grid-enterprise'

import Mitt from '@/utils/mitt.js'
import { AgGridVue } from 'ag-grid-vue3'
import { ref, reactive, onMounted, onBeforeMount, nextTick, defineProps, defineEmits } from 'vue'
import { useRoute } from 'vue-router'
import { Modal } from 'ant-design-vue'
import message from '@/utils/message'
import { loadingStore } from '@/store/loadingStore'
import { settingStore } from '@/store/settingStore'
import { routeStore } from '@/store/routeStore'
// import { AG_GRID_LOCALE_ZH } from '@/utils/locale.cn.js'
import { getModifyDataApi, saveBaseDataApi, paramsReplenApi, deviceInitParam, updateDeviceInitParam, basicApi, DownloadCaseApi, resetDeviceInitParam } from '@/api/exampleApi'
import { downloadApiFile } from '@/utils/common.js'
// enter your license key here to suppress console message and watermark
LicenseManager.setLicenseKey('DownloadDevTools_COM_NDEwMjM0NTgwMDAwMA==59158b5225400879a12a96634544f5b6')

import SourceBusModal from './sourceBusModal.vue'
import SourceModal from './sourceModal.vue'
import SourceStorageModal from './sourceStorageModal.vue'
import ReplaceModal from './replaceModal.vue'
import CountWay from './countWay.vue'

import SourceTypeModal from './sourceTypeModal.vue'

import TimeseriesDetail from './TimeseriesDetail.vue'
import TimeseriesQuote from './TimeseriesQuote.vue'
import TimeseriesForm from './TimeseriesForm.vue'
import TimeseriesAdd from './TimeseriesAdd.vue'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const route = useRoute()
const store = settingStore()
const storeLoading = loadingStore()
const storeRoute = routeStore()

const emits = defineEmits(['handleCorrelate'])

const props = defineProps({
	istree: {
		type: Boolean,
		default: false
	},
	isEdit: {
		type: String,
		default: ''
	},
	treeNode: {
		type: String,
		default: ''
	},
	isInputSearch: {
		type: Boolean,
		default: false
	}
})

const sourceTypeRef = ref()

const tableMarginTop = ref(props.isEdit != 'capacityBalance' ? '40px' : '0px')
const tableStyle = ref(props.isEdit != 'capacityBalance' ? 'width: 100%; height: 1000%;' : 'width: 100%; height: 85%;')

// 序号列
class RowIndexRenderer {
	init(params) {
		this.eGui = document.createElement('div')
		// this.eGui.innerHTML = '' + (params.node.rowIndex + 1) + `<span style="color: red; font-size: 12px;"> 🔴</span>`
		this.eGui.innerHTML = '' + (params.node.rowIndex + 1)
	}
	refresh(params) {
		return false
	}
	getGui() {
		return this.eGui
	}
}

// 操作列
class CustomElements {
	eGui
	eButton
	cButton
	eventListener
	eventListenerForCurve
	init(params) {
		if (state.routePath !== route.fullPath) return
		this.eGui = document.createElement('div')
		this.eGui.classList.add('custom-element')
		this.eGui.innerHTML = `
      <button class="btn-detail">${t('详情')}</button>
      <button class="btn-curve">${t('曲线')}</button>
      <button class="btn-quote">${t('引用')}</button>
    `
		// get references to the elements we want
		this.eButton = this.eGui.querySelector('.btn-detail')
		this.cButton = this.eGui.querySelector('.btn-curve')
		this.qButton = this.eGui.querySelector('.btn-quote')
		// add event listener to button
		this.eventListener = () => {
			timeseriesDetail(params.node)
		}
		this.eventListenerForCurve = () => {
			timeseriesCurve(params.node)
		}
		this.eventListenerForQuote = () => {
			timeseriesQuote(params.node)
		}
		this.eButton.addEventListener('click', this.eventListener)
		this.cButton.addEventListener('click', this.eventListenerForCurve)
		this.qButton.addEventListener('click', this.eventListenerForQuote)
	}

	getGui() {
		return this.eGui
	}

	refresh(params) {
		return false
	}
}

const localeText = ref(null) // 本地汉化文件

const searchValue = ref('')
const fistColumn = ref({ headerName: t('序号'), maxWidth: 70, pinned: 'left', headerCheckboxSelection: true, headerCheckboxSelectionFilteredOnly: true, checkboxSelection: true, cellRenderer: RowIndexRenderer })
const actionBarColumn = ref({ headerName: t('操作'), minWidth: 180, editable: false, cellRenderer: CustomElements })
// const autoSizeStrategy = ref(null) // 自适应列宽
const tooltipShowDelay = ref(null) // 页眉提示
// const editRowIcon = ref('') // 已修改的行的图标
const statusBar = ref(null)

const rowData = ref(null)
const getRowId = ref(null)
const timeseriesRef = ref(null)
const timeseriesQuoteRef = ref(null)
const timeseriesFormRef = ref(null)
const timeseriesAddRef = ref(null)
const deleteMessageRef = ref(null)

const gridApi = ref()
const columnTypes = ref(null)
const autoGroupColumnDef = ref(null)
const getDataPath = ref(null)
const rowSelection = ref(null)

const defaultColDef = ref({
	// flex: 1,
	width: 150,
	// minWidth: 94,
	// maxWidth: 120,
	editable: props.isEdit !== 'balanceSheet' && props.isEdit !== 'costSheet' && props.isEdit !== 'curveTimeseries',
	filter: true,
	menuTabs: ['generalMenuTab', 'filterMenuTab'] // 旧版选项卡式列菜单 "filterMenuTab", "generalMenuTab", "columnsMenuTab"
	// suppressHorizontalScroll: true // 不显示水平滚动条
})

// 'text', 'number', 'boolean', 'date', 'dateString', 'object'
const columnDefs = ref([
	// { headerName: '序号', maxWidth: 110, pinned: 'left', checkboxSelection: true,	rowDrag: true, cellRenderer: RowIndexRenderer },
	// { headerName: 'Athlete11', field: 'mission', cellDataType: 'text', pinned: 'left', cellClassRules: monthCellClassRules },
	// { field: 'company', headerTooltip: '页眉提示工具' }
])

const scrollBarWidth = 16

const state = reactive({
	routePath: route.fullPath,
	confirm_flag: true, // 保存二次确认
	confirm_time_flag: null, // 保存二次确认
	sourceVisible: false, // 母线数据源
	sourceCityVisible: false, // 所在城市数据源
	sourceTimeVisible: false, // 时序数据源弹框
	storageVisible: false, // 储能数据源弹框
	typeVisible: false, // 类型数据源弹框
	replaceVisible: false, // 替换弹框
	countWayVisible: false, // 计算弹框
	typeChangeVisible: false, // 类型转换弹框
	typeChangeData: [], // 类型转换下拉列表
	sourceValue: [], // 选中单元格的时序数据
	col_source: '',
	headerName: '',
	sourceTimeOpenWay: 'cell',

	sourceTitle: '',
	sourceVal: '',

	isedit: false, // 是否编辑

	tableData: [],
	beforeDelData: [],
	timeRowIndex: null,
	timeRowName: '',

	treeValue: '',
	filterData: [],
	isItemized: false,
	treeNode: null,
	rowId: null,
	timeserieFormData: {},

	org_hierarchy_list: [], // 平衡目标参数
	new_value_list: [], // 平衡目标参数
	ratio_value_list: [], // 平衡目标求和比例

	startRow: null, // 范围选择 起始行
	endRow: null, // 范围选择 结束行
	rangeColumns: [], // 范围选择 列

	editRowIndex: null, // 编辑行的序号
	editField: '', // 编辑单元格的key索引
	isEdit: false, // 是否编辑
	timeseriesVisible: false, // 时序详情弹框
	timeseriesQuoteVisible: false, // 时序引用弹框
	timeseriesFormVisible: false, // 时序曲线新增表单弹框
	timeseriesAddVisible: false, // 时序曲线新增表格弹框
	timeMatchShow: false, // 时序匹配弹框
	lineShow: false,
	deteleMessageVisible: false,
	selectedData: [],
	insertObj: {},
	timeTypeOptions: {},
	sourceOptions: {},
	typeOptions: {},
	paramsTypeOptions: {},
	// isMultiple: '',
	max_index: -9999999
})

// const props = defineProps(['tableOptions'])
// const tableOptions = toRef(props, 'tableOptions')

// 设置行样式 （斑马纹）
const getRowStyle = params => {
	if (params.node.rowIndex % 2 === 0) {
		// return { background: '#eff8ff' }
	}
}

// 快速搜索
const onFilterTextBoxChanged = () => {
	gridApi.value.setGridOption(
		'quickFilterText',
		searchValue.value
	)
}

// 启动过滤器
// const openFilter = () => {
// 	gridApi.value.showColumnFilter('country')
// }

// 插入行
const insertRow = (count) => {
	if (state.routePath !== route.fullPath) return
	const treeValue = sessionStorage.getItem('treeType')

	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	if (props.isEdit !== 'itemizedTimeseries' && props.isEdit !== 'paramsManage' && treeValue !== 'timeseries') {
		if (treeValue !== 'dc_line' && treeValue !== 'bus') {
			state.insertObj.type = treeValue.split('.')[1] // 插入行默认填充type栏
		}

		if (Object.keys(state.insertObj).includes('in_service')) {
			state.insertObj.in_service = true // 插入行默认填充是否有效栏
		}
	}
	const newItems = Array(count).fill({ ...state.insertObj })
	const tempArr = JSON.parse(JSON.stringify(newItems))
	tempArr.forEach((item, index) => {
		state.max_index = item.index = state.max_index + 1
	})
	const addIndex = undefined
	gridApi.value.applyTransaction({
		add: tempArr,
		addIndex: addIndex
	})
	// const maxRowFound = gridApi.value.isLastRowIndexKnown()
	// if (addIndex) {
	// 	const rowCount = gridApi.value.getInfiniteRowCount() || 0
	// 	gridApi.value.setRowCount(rowCount + addIndex)
	// }
	// get grid to refresh the data
	// gridApi.value.refreshInfiniteCache()
}

// 删除选中的行
const onRemoveSelected = (treeVal) => {
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	state.treeValue = treeVal
	state.beforeDelData = allRowData
	const selectedData = gridApi.value.getSelectedRows()
	const selectedArr = gridApi.value.getSelectedRows().map(item => item.index)
	gridApi.value.applyTransaction({ remove: selectedData })
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)

	if ((treeVal == 'bus') && selectedArr.length > 0) {
		basicApi({
			'import_string_func': 'teapcase:check_element_relation',
			'func_arg_dict': {
				'sheet_name': treeVal,
				'file_name': route.query.filePath,
				'row_id_list': selectedArr
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 0) {
				state.deteleMessageVisible = true
				nextTick(() => {
					deleteMessageRef.value.getDeleteInfo(res.func_result)
				})
				// Modal.confirm({
				// 	title: '注意',
				// 	content: res.func_result.message,
				// 	onOk() {
				// 		saveAgtable(treeVal)
				// 	},
				// 	onCancel() {
				// 		gridApi.value.setRowData(allRowData)
				// 	}
				// })
			}
		})
	}
}

const handleDeleteConfirm = () => {
	state.deteleMessageVisible = false
	saveAgtable()
}

const handleDeleteCancle = () => {
	state.deteleMessageVisible = false
	gridApi.value.setRowData(state.beforeDelData)
}

// 获取选中的行
const getSelected = () => {
	const selectedData = gridApi.value.getSelectedRows()
	// Mitt.emit('handleItemizedDel', selectedData)
	return selectedData
}

// 删除行（废弃）
const removeRow = () => {
	// if (state.routePath !== route.fullPath) return
	// const selectedRows = gridApi.value.getSelectedRows()
	// rowData.value = rowData.value.filter(item => {
	// 	return !selectedRows.some(val => item.index == val.index)
	// })
	// gridApi.value.refreshInfiniteCache()
}

// 开始编辑单元格事件
const onCellEditingStarted = (e) => {
	if (state.routePath !== route.fullPath) return
	const treeValue = sessionStorage.getItem('treeType')
	if (e.colDef.col_source || e.colDef.field == 'type' || e.colDef.field == 'data_source' || e.colDef.field == 'location_city') {
		// state.isMultiple = e.colDef.col_source == 'timeseries' ? 'multiple' : ''
		// sourceType.value = e.colDef.col_source
		state.editField = e.colDef.field
		if (e.colDef.field == 'location_city' || e.data.col_source == 'location_city') {
			state.sourceCityVisible = true
			gridApi.value.stopEditing()
		} else if (e.colDef.col_source == 'timeseries' || e.colDef.col_type == 'index_list') {
			state.sourceValue = e.value || []
			state.col_source = e.colDef.col_source
			state.headerName = e.colDef.headerName
			state.sourceTimeVisible = true
			state.sourceTimeOpenWay = 'cell'
			state.isItemized = false // 默认非逐项视图
		} else if (e.data.col_type == 'index_list') {
			state.sourceValue = e.value || []
			state.col_source = e.data.col_source
			state.headerName = e.data.name
			state.sourceTimeVisible = true
			state.sourceTimeOpenWay = 'itemizedButton'
			state.isItemized = true // 默认非逐项视图
		} else if ((treeValue == 'timeseries' || props.isEdit == 'itemizedTimeseries') && route.query.type !== 'params') {
			if (e.colDef.field == 'type') {
				state.typeVisible = true
				gridApi.value.stopEditing()
				nextTick(() => {
					sourceTypeRef.value.getReadNameColData(props.isEdit, props.treeNode)
				})
			}
		} else {
			return
		}
		state.editRowIndex = e.data.index
		// gridApi.value.stopEditing()
	}
	// state.isedit = true
	// storeRoute.setTabs(route.fullPath, true)
}

// 下载表格
const onBtExport = (fileName) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.exportDataAsExcel({ fileName, sheetName: 'Sheet1' })
}

// 单元格 值改变 事件
const onCellValueChanged = (data) => {
	if (state.routePath !== route.fullPath) return
	state.org_hierarchy_list.push(data.data.orgHierarchy)
	state.new_value_list.push(data.value)
	state.ratio_value_list.push(data.value / data.oldValue)
	state.isedit = true
	if (props.isEdit != 'capacityBalance') {
		storeRoute.setTabs(route.fullPath, true)
		storeRoute.setSaveTabs(route.fullPath, false)
	}
}

// 开始编辑行事件
const onRowEditingStarted = (e) => {
	if (state.routePath !== route.fullPath) return
}

// 选择触发
const onSelectionChanged = (e) => {
	if (state.routePath !== route.fullPath) return
}

// 处理复制到粘贴板的值
const processCellForClipboard = (params) => {
	// if (props.isTimeseriesDetail) return

	return Array.isArray(params.value) ? JSON.stringify([...params.value]) : params.value
}
// 在网格底部粘贴新行
const processDataFromClipboard = (params) => {
	if (state.routePath !== route.fullPath) return
	if (props.isEdit == 'balanceSheet' || props.isEdit == 'costSheet' || props.isEdit == 'costLineSheet') return
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
	const treeValue = sessionStorage.getItem('treeType')
	const data = [...params.data]
	const emptyLastRow =
        data[data.length - 1][0] === '' && data[data.length - 1].length === 1
	if (emptyLastRow) {
		data.splice(data.length - 1, 1)
	}

	const lastIndex = params.api.getModel().getRowCount() - 1
	const focusedCell = params.api.getFocusedCell()
	const focusedIndex = focusedCell.rowIndex
	if (focusedIndex + data.length - 1 > lastIndex) {
		const resultLastIndex = focusedIndex + (data.length - 1)
		const numRowsToAdd = resultLastIndex - lastIndex
		const rowsToAdd = []
		for (let i = 0; i < numRowsToAdd; i++) {
			const index = data.length - 1
			const row = data.slice(index, index + 1)[0]
			// Create row object
			const rowObject = {}
			let currentColumn = focusedCell.column
			row.forEach((item) => {
				if (!currentColumn) {
					return
				}
				rowObject[currentColumn.colDef.field] = item
				currentColumn = params.api.getDisplayedColAfter(currentColumn)
			})

			for (const key in state.insertObj) {
				if (rowObject[key] == undefined) {
					rowObject[key] = null
				}
			}

			if (treeValue.includes('gen') || treeValue.includes('stogen') || treeValue.includes('storage') || treeValue.includes('gen_plan') || treeValue.includes('stogen_plan') || treeValue.includes('storage_plan')) {
				rowObject.type = treeValue.split('.')[1] // 粘贴时默认填充type栏
			}

			if (Object.keys(rowObject).includes('in_service')) {
				rowObject.in_service = true // 插入行默认填充是否有效栏
			}
			state.max_index = rowObject.index = state.max_index + 1
			rowsToAdd.push(rowObject)
		}
		params.api.applyTransaction({ add: rowsToAdd })
	}
	return data
}

const showContextMenu = (params) => {
	return {
		x: 0,
		y: 0
	}
}

// 粘贴完触发
const onPasteEnd = (e) => {
	// const allRowData = []
	// gridApi.value.forEachNode(function(node) {
	// 	allRowData.push(node.data)
	// })
	gridApi.value.redrawRows()

	gridApi.value.refreshClientSideRowModel()
}

// 筛选触发
const onFilterChanged = (e) => {
	state['filterModel-' + state.treeValue] = gridApi.value.getFilterModel()
}

// 清除筛选条件
const clearFilter = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.setFilterModel(null)
}
Mitt.on('clearFilter', clearFilter)

// 按钮触发复制
const onCopyClick = () => {
	// gridApi.value.copyToClipboard()
	gridApi.value.copySelectedRangeToClipboard()
}

const onCutClick = () => {
	gridApi.value.cutToClipboard()
}

const onPasteClick = () => {
	gridApi.value.pasteFromClipboard()
}

// 增加删除列
const onBtExcludeMedalColumns = () => {
	if (state.routePath !== route.fullPath) return
	const colDefsMedalsExcluded = [
		{ field: 'athlete' },
		{ field: 'age' }
	]
	gridApi.value.setGridOption('columnDefs', colDefsMedalsExcluded)
}
// 修改headerName
const setHeaderNames = () => {
	if (state.routePath !== route.fullPath) return
	columnDefs.value.forEach(function(colDef, index) {
		colDef.headerName = 'C' + index
	})
	gridApi.value.setGridOption('columnDefs', columnDefs.value)
}
// 设置隐藏咧
const onBtHide = () => {
	if (state.routePath !== route.fullPath) return
	columnDefs.value.forEach((colDef) => {
		if (colDef.field === 'age' || colDef.field === 'athlete') {
			colDef.hide = true
		}
	})
	gridApi.value.setGridOption('columnDefs', columnDefs.value)
}

// 设置列固定
const onBtPinnedOn = () => {
	if (state.routePath !== route.fullPath) return
	columnDefs.value.forEach((colDef) => {
		if (colDef.field === 'athlete') {
			colDef.pinned = 'left'
		}
		if (colDef.field === 'age') {
			colDef.pinned = 'right'
		}
	})
	gridApi.value.setGridOption('columnDefs', columnDefs.value)
}

// 添加范围选择
const onAddRange = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.addCellRange({
		rowStartIndex: 4,
		rowEndIndex: 8,
		columnStart: 'age',
		columnEnd: 'date'
	})
}
// 取消范围选择
// const onClearRange = () => {
// 	gridApi.value.clearRangeSelection()
// }
// 上一步 撤销
const undo = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.undoCellEditing()
}
// 下一步 恢复
const redo = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.redoCellEditing()
}

// 时序详情
const timeseriesDetail = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeseriesVisible = true
	nextTick(() => {
		timeseriesRef.value.getTimeseriesInit(val.data, route.query.filePath)
	})
}
const timeseriesDetailClose = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeseriesVisible = false
	if (props.isEdit == 'curveTimeseries') return
	nextTick(() => {
		getAgTableData(state.treeValue, state.filterData)
	})
}

// 时序曲线
const timeseriesCurve = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeRowIndex = val.data.index
	state.timeRowName = '时序曲线'
	state.lineShow = true
}

// 时序引用
const timeseriesQuote = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeseriesQuoteVisible = true

	nextTick(() => {
		timeseriesQuoteRef.value.getQuoteData(val.data.index)
	})
}

// 时序曲线新增
const timeseriesAdd = (val, treeNode, rowId) => {
	if (state.routePath !== route.fullPath) return
	state.isItemized = val
	state.treeNode = treeNode
	state.rowId = rowId
	state.timeseriesFormVisible = true
	nextTick(() => {
		timeseriesFormRef.value.getReadNameColData(val, treeNode)
	})
}

// 母线数据源
const handleSaveSource = (val) => {
	if (state.routePath !== route.fullPath) return
	// gridApi.value.stopEditing()
	state.sourceVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

// 所属城市
const handleCityConfirm = (val) => {
	if (state.routePath !== route.fullPath) return
	// gridApi.value.stopEditing()
	state.sourceCityVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val.areaName)
	rowNode.setDataValue('longitude', val.center[0])
	rowNode.setDataValue('latitude', val.center[1])
}
// 时序数据源
const handleConfirmTime = (val) => {
	if (state.routePath !== route.fullPath) return
	if (!state.isItemized || state.col_source !== 'timeseries') {
		gridApi.value.stopEditing()
		state.sourceTimeVisible = false
		const rowNode = gridApi.value.getRowNode(state.editRowIndex)
		rowNode.setDataValue(state.editField, val)
	} else {
		state.sourceTimeVisible = false
		// Mitt.emit('handleCorrelate', val)
		emits('handleCorrelate', val)
	}
}
const handleCancelTime = (val) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	state.sourceTimeVisible = false
	if (state.sourceTimeOpenWay == 'itemizedButton') return
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

// 逐项视图关联时序
const handleCorrelateData = (val) => {
	if (state.routePath !== route.fullPath) return
	state.sourceValue = val || []
	state.col_source = 'timeseries'
	state.headerName = '时序曲线'
	state.sourceTimeVisible = true
	state.sourceTimeOpenWay = 'itemizedButton'
	state.isItemized = true
}

// 储能数据源
const handleSaveStorage = (val) => {
	if (state.routePath !== route.fullPath) return
	// gridApi.value.stopEditing()
	state.storageVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

// 类型详情
const handleSaveType = (val) => {
	if (state.routePath !== route.fullPath) return
	// gridApi.value.stopEditing()
	state.typeVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

// 参数补充
const handleParamsReplen = (treeVal) => {
	if (state.routePath !== route.fullPath) return
	const selectedRows = gridApi.value.getSelectedRows()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	const table_list = selectedRows.length <= 0 ? allRowData : selectedRows
	paramsReplenApi({
		'import_string_func': 'teapcase:get_init_col_data',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': treeVal,
			'replace': false,
			'row_id_list': table_list.map(item => item.index),
			'col_list': []
			// 'data': table_list.map(item => item.index)
		}
	}).then(res => {
		const { input_equals_output } = res.func_result[treeVal]
		// if (data.length <= 0) return
		if (!input_equals_output) {
			getAgTableData(treeVal)
			Mitt.emit('getTreeMenuList', 'saveRefresh')
			// const updateData = allRowData.map(item => {
			// 	data.forEach(val => {
			// 		if (item.index == val.index) {
			// 			item = val
			// 		}
			// 	})
			// 	return item
			// })

			// updateData.forEach(element => {
			// 	columns.forEach(item => {
			// 		for (const key in element) {
			// 			if (item.field == key && item.col_ratio && element[key]) {
			// 				element[key] = element[key] * item.col_ratio
			// 			}
			// 		}
			// 	})
			// })

			// gridApi.value.applyTransaction({
			// 	remove: allRowData
			// })
			// gridApi.value.setGridOption('rowData', updateData)
			// Mitt.emit('getDeviceRowClick')
			storeRoute.setTabs(route.fullPath, true)
			storeRoute.setSaveTabs(route.fullPath, false)
			// gridApi.value.applyTransaction({ update: data })
			// saveAgtable(treeVal)
		}
		message.success(t('参数补充成功！'))
	})
}

// 时序匹配
const handleTimeMatch = () => {
	if (state.routePath !== route.fullPath) return

	const isHaveTimeseries = columnDefs.value.some(item => item.field == 'timeseries')
	if (!isHaveTimeseries) return message.warning(t('不允许关联时序曲线'))

	state.selectedData = gridApi.value.getSelectedRows().map(item => item.index)
	if (state.selectedData.length <= 0) return message.warning(t('请选择数据'))
	saveAgtable(state.treeValue, 'timeMatch')
}
const confirmTypeChange = (val) => {
	state.typeChangeVisible = false
	storeLoading.showModal()
	basicApi({
		'import_string_func': 'teapcase:power_ele_type_convert',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'source_sheet_name': state.treeValue,
			'target_sheet_name': val,
			'selected_row_id_list': gridApi.value.getSelectedRows().map(item => item.index)
		}
	}).then(res => {
		if (res.code == 1) {
			message.success(t('类型转换成功！'))
			getAgTableData(state.treeValue)
			Mitt.emit('getTreeMenuList')
		}
		storeLoading.hiddenModal()
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

// 类型转换
const handleTypeChange = () => {
	if (state.routePath !== route.fullPath) return
	state.selectedData = gridApi.value.getSelectedRows().map(item => item.index)
	if (state.selectedData.length <= 0) return message.warning(t('请选择数据'))
	basicApi({
		'import_string_func': 'teapcase:get_valid_type_convert_sheet_info',
		'func_arg_dict': {
			'source_sheet_name': state.treeValue
		}
	}).then(res => {
		if (res.code == 1) {
			state.typeChangeData = res.func_result
			state.typeChangeVisible = true
		}
	})
}

const handleTimeMatchRefresh = () => {
	state.timeMatchShow = false
	getAgTableData(state.treeValue)
}

// 曲线预览的时序表
const setCurveTimeData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	const { relation_sheet_index, type, ts_type } = tableData
	for (const key in relation_sheet_index) {
		state.sourceOptions[key] = relation_sheet_index[key].reduce((acc, item) => Object.assign(acc, { [item.index]: item.name }), {})
	}
	for (const key in type) {
		state.typeOptions[key] = type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
	}
	state.timeTypeOptions = ts_type.ts_type_name_map

	if (tableData.total_row > 0) {
		tableData.columns.push(actionBarColumn.value)
	}
	updateData(tableData)
}
// 时序详情
const setTimeDeatilData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	updateData(tableData)
}

// 平衡表
const setBlanceData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	updateData(tableData)
}

// 平衡目标表
const setBlanceGoalsData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	updateData(tableData)
}

// 打开替换弹框
const openReplace = () => {
	const rangeSelections = gridApi.value.getCellRanges()
	if (rangeSelections.length <= 0) {
		return message.warning(t('请选择要替换的数据'))
	} else {
		state.replaceVisible = true
		state.startRow = rangeSelections[0].startRow.rowIndex
		state.endRow = rangeSelections[0].endRow.rowIndex
		state.rangeColumns = rangeSelections[0].columns
	}
}

// 替换
const handleSaveReplace = (searchValue, replaceValue) => {
	const itemsToUpdate = []
	gridApi.value.forEachNodeAfterFilterAndSort(function(rowNode, index) {
		if (index >= state.startRow && index <= state.endRow) {
			const data = rowNode.data
			state.rangeColumns.forEach(item => {
				if (item.colDef.col_source) { // 判断是否是引用类型
					const tempSearch = Number(Object.keys(state.sourceOptions[item.colDef.col_source]).find(key => state.sourceOptions[item.colDef.col_source][key] === searchValue))
					const tempeRplace = Number(Object.keys(state.sourceOptions[item.colDef.col_source]).find(key => state.sourceOptions[item.colDef.col_source][key] === replaceValue))
					if (item.colDef.col_source == 'timeseries') {
						if (data[item.colId].includes(tempSearch)) {
							data[item.colId] = data[item.colId].map(item => (item === tempSearch ? tempeRplace : item))
						}
					} else {
						if (data[item.colId] == tempSearch) {
							data[item.colId] = tempeRplace
						}
					}
				} else {
					if (item.colDef.field == 'type') {
						if (item.colDef.editable) {
							const typeSearch = Object.keys(state.typeOptions[state.treeValue]).find(key => state.typeOptions[state.treeValue][key] === searchValue)
							const typeRplace = Object.keys(state.typeOptions[state.treeValue]).find(key => state.typeOptions[state.treeValue][key] === replaceValue)
							if (data[item.colId] == typeSearch) {
								data[item.colId] = typeRplace
							}
						}
					} else {
						data[item.colId] = JSON.parse(JSON.stringify(data[item.colId]).replace(searchValue, replaceValue))
					}
				}

				itemsToUpdate.push(data)
			})
			itemsToUpdate.push(data)
		}
	})
	gridApi.value.applyTransaction({ update: itemsToUpdate })

	state.replaceVisible = false
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
}

// 计算
const onCount = () => {
	const rangeSelections = gridApi.value.getCellRanges()
	if (rangeSelections.length <= 0) {
		return message.warning(t('请选择要计算的数据'))
	} else {
		state.countWayVisible = true
		state.startRow = rangeSelections[0].startRow.rowIndex
		state.endRow = rangeSelections[0].endRow.rowIndex
		state.rangeColumns = rangeSelections[0].columns
	}
}

// 计算
const handleSaveCount = (value, way) => {
	if (value !== null) {
		const itemsToUpdate = []
		gridApi.value.forEachNodeAfterFilterAndSort(function(rowNode, index) {
			if (index >= state.startRow && index <= state.endRow) {
				const data = rowNode.data
				state.rangeColumns.forEach(item => {
					if (way == 'sub') {
						data[item.colId] = data[item.colId] - value
					} else if (way == 'mul') {
						data[item.colId] = data[item.colId] * value
					} else if (way == 'div') {
						data[item.colId] = data[item.colId] / value
					} else {
						data[item.colId] = data[item.colId] + value
					}
					itemsToUpdate.push(data)
				})
				itemsToUpdate.push(data)
			}
		})
		gridApi.value.applyTransaction({ update: itemsToUpdate })
	}
	state.countWayVisible = false
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
}

// ag-grid创建完成后执行的事件
const onGridReady = (params) => {
	gridApi.value = params.api
}

function onBtExpandTopLevel() {
	// gridApi.value.expandAll() // 展开全部节点
	gridApi.value.forEachNode(function(node) { // 展开第一级节点
		if (node.level == 0) {
			node.setExpanded(true)
		}
	})
}

const getReadNameData = (data) => {
	const { relation_sheet_index, ts_type, type } = data
	for (const key in relation_sheet_index) {
		state.sourceOptions[key] = relation_sheet_index[key].reduce((acc, item) => Object.assign(acc, { [item.index]: item.name }), {})
	}

	for (const key in type) {
		state.typeOptions[key] = type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
	}

	state.timeTypeOptions = ts_type.ts_type_name_map
}

// 获取表格数据
const getAgTableData = (val, filterArr) => {
	if (state.routePath !== route.fullPath) return
	state.treeValue = val || sessionStorage.getItem('treeType')
	state.filterData = filterArr
	getModifyDataApi({
		'import_string_func': 'teapcase:read_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath, // # 要带扩展名.h5
			'filtered_index_list': filterArr,
			'sheet_name': val || sessionStorage.getItem('treeType') // # 此参数可以不传，也可以为空列表，两种情况都会返回所有表
		}
	}).then(res => {
		if (res.code == 1) {
			const { relation_sheet_index, type, ts_type } = res.func_result[val || sessionStorage.getItem('treeType')]
			for (const key in relation_sheet_index) {
				state.sourceOptions[key] = relation_sheet_index[key].reduce((acc, item) => Object.assign(acc, { [item.index]: item.name }), {})
			}

			for (const key in type) {
				state.typeOptions[key] = type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
			}
			state.timeTypeOptions = ts_type.ts_type_name_map
			// ts_type.ts_type_name_map.forEach(item => {
			// 	state.timeTypeOptions[item.value] = item.label
			// })
			state.tableData = res.func_result[val || sessionStorage.getItem('treeType')]
			state.tableData.columns.unshift(fistColumn.value)
			if (val == 'timeseries' || sessionStorage.getItem('treeType') == 'timeseries') {
				if (res.func_result.timeseries.total_row > 0) { // 时序空白行不添加最后的操作列
					state.tableData.columns.push(actionBarColumn.value)
				}
				Mitt.emit('getGlobalParameters', {
					start_datetime: res.func_result.start_datetime,
					end_datetime: res.func_result.end_datetime,
					data_freq: res.func_result.data_freq
				})
			}

			state.max_index = state.tableData.max_index
			storeRoute.setSaveTabs(route.fullPath, res.func_result.is_saved)

			updateData(state.tableData, null, 'getAgTableData')
		}
	})
}

// 获取参数管理
const getParamsAgTable = (val) => {
	if (state.routePath !== route.fullPath) return
	state.treeValue = val || sessionStorage.getItem('treeType')
	deviceInitParam({
		device_name: val
	}).then(res => {
		if (res.code == 1) {
			// state.tableData = res.func_result[val]
			const { columns, data } = res
			for (const key in res.type) {
				state.typeOptions[key] = res.type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
			}
			columns.unshift(fistColumn.value)
			const tableData = {
				columns: columns,
				data: data
			}
			storeRoute.setTabs(route.fullPath, false) // 重置 关闭未保存状态
			storeRoute.setSaveTabs(route.fullPath, true)
			updateData(tableData, val)
		}
	})
}

// 参数管理 恢复默认
const resetParamsAgTable = (val, paramsTreeData, isCurrent) => {
	if (state.routePath !== route.fullPath) return
	resetDeviceInitParam({
		device_names: isCurrent ? [val] : paramsTreeData
	}).then(res => {
		if (res.code == 1) {
			message.success(res.message || t('恢复默认成功'))
			getParamsAgTable(val)
		}
	})
}

// 更新表格data
const updateData = (data, deviceId, type) => {
	if (state.routePath !== route.fullPath) return
	const treeValue = sessionStorage.getItem('treeType')
	state.insertObj = {}
	let tableData
	// getRowId.value = (params) => params.data.index
	columnDefs.value = data.columns.map(item => {
		// 时序、节点、直流线路 的type 列可编辑，其他表的type禁止编辑
		// if (props.isEdit !== 'itemizedTimeseries') {
		// 	item.editable = 'false'
		// }

		// 格式化 （保留三位小数）
		if (item.cellDataType == 'number' && !item.col_source) {
			item.valueFormatter = currencyNumFormatter
		}

		// 时序的 数据类型列 （替换/倍乘）
		if (item.field == 'value_type') {
			item.cellEditor = 'agSelectCellEditor'
			item.cellEditorParams = {
				values: ['replace', 'multiply']
			}
			item.refData = {
				replace: t('数据替换'),
				multiply: t('数据倍乘')
			}
			item.filter = 'agSetColumnFilter'
		}

		// 引用类型的列 select以及汉化处理
		if (item.col_source && item.col_type !== 'index_list' && !item.type) {
			item.cellEditor = 'agRichSelectCellEditor'
			item.cellEditorParams = {
				values: Object.keys(state.sourceOptions[item.col_source]).map(Number),
				formatValue: value => state.sourceOptions[item.col_source][value] == '' ? ' ' : state.sourceOptions[item.col_source][value],
				allowTyping: true,
				filterList: true,
				highlightMatch: true,
				valueListMaxHeight: 220
			}

			item.valueFormatter = currencyValFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyValFormatter }
		}

		if (item.col_source == 'timeseries') {
			item.cellEditor = 'agTextCellEditor'
			item.valueFormatter = currencyValTimeFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyValFormatter }
		}

		// type列 汉化处理(除了时序表的类型)
		if (item.field == 'type' && item.headerName !== '时序类型') {
			if (state.typeOptions[treeValue]) {
				item.cellEditor = 'agRichSelectCellEditor'
				item.cellEditorParams = {
					values: Object.keys(state.typeOptions[treeValue]),
					formatValue: value => state.typeOptions[treeValue][value],
					allowTyping: true,
					filterList: true,
					highlightMatch: true,
					valueListMaxHeight: 220
				}
			}

			item.valueFormatter = currencyTypeFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyTypeFormatter }
		}
		// 储能类型 列 汉化处理 属性type为true的就使用下拉字典
		if (item.type) {
			item.cellEditor = 'agRichSelectCellEditor'
			item.cellEditorParams = {
				values: Object.keys(state.typeOptions[`${item.col_source}.${item.field}`]),
				formatValue: value => state.typeOptions[`${item.col_source}.${item.field}`][value],
				allowTyping: true,
				filterList: true,
				highlightMatch: true,
				valueListMaxHeight: 220
			}
			item.valueFormatter = (params) => {
				if (params.value && state.typeOptions[`${item.col_source}.${item.field}`]) {
					return state.typeOptions[`${item.col_source}.${item.field}`][params.value]
				} else {
					return params.value
				}
			}
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: (params) => {
				if (params.value && state.typeOptions[`${item.col_source}.${item.field}`]) {
					return state.typeOptions[`${item.col_source}.${item.field}`][params.value]
				} else {
					return params.value
				}
			} }
		}

		// type列 汉化处理 参数管理的类型
		if (deviceId && item.field == 'type' && deviceId !== 'ac_line' && deviceId !== 'dc_line') {
			item.cellEditor = 'agRichSelectCellEditor'
			item.cellEditorParams = {
				values: Object.keys(state.typeOptions[deviceId]),
				formatValue: value => state.typeOptions[deviceId][value],
				allowTyping: true,
				filterList: true,
				highlightMatch: true,
				valueListMaxHeight: 220
			}
			item.valueFormatter = (params) => {
				if (params.value && state.typeOptions[deviceId]) {
					return state.typeOptions[deviceId][params.value]
				} else {
					return params.value
				}
			}
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: (params) => {
				if (params.value && state.typeOptions[deviceId]) {
					return state.typeOptions[deviceId][params.value]
				} else {
					return params.value
				}
			} }
		}
		if ((sessionStorage.getItem('treeType') && sessionStorage.getItem('treeType').includes('timeseries')) || props.isEdit == 'itemizedTimeseries' || props.isEdit == 'curveTimeseries') {
			if (item.field == 'type') {
				item.valueFormatter = currencyTimeTypeFormatter
				item.filter = 'agSetColumnFilter'
				item.filterParams = { valueFormatter: currencyTimeTypeFormatter }
			}
		}

		if (item.col_type == 'index_list') {
			item.valueFormatter = currencyValTimeFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyValTimeFormatter }
		}

		// 逐项试图里的关联列 节点 select以及汉化处理
		if (item.field == 'data_source') {
			if (item.cellDataType !== 'object') {
				item.cellEditor = 'agRichSelectCellEditor'
				item.cellEditorParams = currencyCellEditorParams
			}
			item.valueFormatter = currencySourceFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencySourceFormatter }
		}
		// 逐项视图里的数据源处理
		if (item.field == 'value') { // 机组类型
			item.cellDataType = null
			item.valueParser = itemValueParser
			item.valueFormatter = currencyValueFormatter
		}

		// 时间格式处理
		if (item.datetime || item.field == 'datetime') {
			item.cellEditor = 'agDateStringCellEditor'
		}

		// 直流线路 可用功率档位
		if (item.col_type == 'list') {
			if (!item.hide) {
				item.cellDataType = 'array'
				item.valueParser = currencyValueParser
				item.valueFormatter = powerLevelsValueFormatter
			}
		}

		if (item.required) {
			item.headerComponentParams = {
				template: `<div class="ag-cell-label-container" role="presentation">
	                  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>
	                  <div ref="eLabel" class="ag-header-cell-label" role="presentation">
                      <span ref="eSortOrder" class="ag-header-icon ag-sort-order ag-hidden"></span>
                      <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon ag-hidden"></span>
                      <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon ag-hidden"></span>
                      <span ref="eSortMixed" class="ag-header-icon ag-sort-mixed-icon ag-hidden"></span>
                      <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon ag-hidden"></span>
                      <span ref="eText" class="ag-header-cell-text" role="columnheader"></span> ※
                      <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>
	                  </div>
	              </div>`
			}
		}
		return item
	})
	columnDefs.value.forEach(item => {
		item.cellStyle = { 'font-family': 'SiYuan Normal' }
		if (item.cellDataType == 'text') {
			state.insertObj[item.field] = ''
		}
		if (item.cellDataType == 'boolean') {
			state.insertObj[item.field] = null
		} else if (item.cellDataType == 'number') {
			state.insertObj[item.field] = null
			if (!item.col_source) {
				item.cellStyle = { 'font-family': 'SiYuan Normal', 'text-align': 'right' }
			}
		} else if (item.cellDataType == 'object') {
			state.insertObj[item.field] = []
		} else if (item.field && !item.cellDataType) {
			state.insertObj[item.field] = null
		}
		// if (props.isEdit === 'itemized') {
		// item.editable = cellEditable
		// }
		if (props.isEdit == 'balanceSheet' || props.isEdit == 'costSheet') {
			item.cellStyle = { 'font-family': 'SiYuan Normal', 'text-align': 'right' }
		}
	})

	if (data.data.length <= 0) {
		const tempInsertObj = JSON.parse(JSON.stringify(state.insertObj))
		state.max_index = tempInsertObj.index = state.max_index + 1
		if (props.isEdit !== 'itemizedTimeseries' && props.isEdit !== 'balanceSheet' && props.isEdit !== 'costSheet' && props.isEdit !== 'curveTimeseries' && treeValue !== 'timeseries') {
			if (treeValue !== 'dc_line' && treeValue !== 'bus') {
				tempInsertObj.type = treeValue.split('.')[1] == undefined ? null : treeValue.split('.')[1] // 插入行默认填充type栏
			}
			if (Object.keys(tempInsertObj).includes('in_service')) {
				tempInsertObj.in_service = true // 插入行默认填充是否有效栏
			}
		}
		tableData = [...data.data, tempInsertObj]
	} else {
		tableData = data.data
	}
	// 单位切换 处理
	if (type == 'getAgTableData') {
		tableData.forEach(element => {
			columnDefs.value.forEach(item => {
				for (const key in element) {
					if (item.field == key && item.col_ratio && element[key]) {
						element[key] = element[key] * item.col_ratio
					}
				}
			})
		})
	}

	rowData.value = tableData

	getRowId.value = (params) => params.data.index
	if (props.isEdit == 'curveTimeseries') return
	gridApi.value.clearRangeSelection() // 取消范围选择
	gridApi.value.deselectAllFiltered() // 取消选择所有过滤
	gridApi.value.deselectAllOnCurrentPage() // 取消选择
	storeLoading.hiddenModal()

	if (props.isEdit == 'costSheet') { // 成本表 展开第一级节点
		nextTick(() => {
			onBtExpandTopLevel()
		})
	}
	const filterModel = state['filterModel-' + state.treeValue] // 筛选状态保持
	if (props.isEdit == 'editor' && filterModel && Object.keys(filterModel).length !== 0) {
		nextTick(() => {
			gridApi.value.setFilterModel(filterModel)
		})
	}
}

// 过滤器 值 格式化
// const filterFormatter = (params) => {
// 	console.log(1111, params.value)
// 	return {
// 		// filterOptions: state.sourceOptions[params.colDef.col_source]
// 		filterOptions: ['contains']
// 	}
// }

// 逐项视图 关联列
const currencyCellEditorParams = (params) => {
	return {
		values: Object.keys(state.sourceOptions[params.data.col_source]).map(Number),
		// formatValue: state.sourceOptions[params.data.col_source][params.data.data_source],
		allowTyping: true,
		filterList: true,
		highlightMatch: true,
		valueListMaxHeight: 220
	}
}

// 格式化 （保留三位小数）
const currencyNumFormatter = (params) => {
	if (params.value && !Number.isInteger(params.value)) {
		// 转换为字符串
		const strNumber = params.value.toString()
		// 判断小数部分位数
		const decimalIndex = strNumber.indexOf('.')
		if (decimalIndex !== -1 && strNumber.length - decimalIndex > 4) {
			return parseFloat(params.value.toFixed(3))
		} else {
			return params.value
		}
	} else {
		return params.value
	}
}
// 格式化 （通过value现实name）
const currencyValFormatter = (params) => {
	if (state.sourceOptions[params.colDef.col_source][params.value]) {
		return state.sourceOptions[params.colDef.col_source][params.value]
	} else if (state.sourceOptions[params.colDef.col_source][params.value] == '') {
		return ''
	} else {
		return params.value
	}
}

// 多选 引用列 格式化（时序曲线、正反向交流）
const currencyValTimeFormatter = (params) => {
	if (Array.isArray(params.value) && params.value.length > 0) {
		const tempArr = params.value.map(item => item = state.sourceOptions[params.colDef.col_source][item])
		return tempArr
	} else {
		return params.value
	}
}

// type类型格式化 （通过value现实name）
const currencyTypeFormatter = (params) => {
	let treeNode = sessionStorage.getItem('treeType') || 'bus'
	treeNode = treeNode.includes('.') ? treeNode.split('.')[0] : treeNode
	if (params.value && state.typeOptions[treeNode]) {
		return state.typeOptions[treeNode][params.value]
	} else {
		return params.value
	}
}

// 时序类型格式化 （通过value现实name）
const currencyTimeTypeFormatter = (params) => {
	if (state.timeTypeOptions[params.value]) {
		return state.timeTypeOptions[params.value]
	} else {
		return params.value
	}
}

// 逐项视图数据源处理
const itemValueParser = (params) => {
	if (params.data.key == 'power_levels') {
		if (!params.newValue || params.newValue.length <= 0) return []
		const paramsCellValue = Array.isArray(params.newValue) ? params.newValue : params.newValue.split(',')
		const tempArr = paramsCellValue.map(item => {
			const num = Number(item)
			return isNaN(num) ? null : num // 如果转换结果不是数字，返回null（或者根据需要处理）
		})
		if (tempArr.includes(null)) {
			alert(t('格式错误,请输入英文逗号分隔的数值！示例：1,2,3'))
		}
		return tempArr.includes(null) ? [] : tempArr
	} else {
		return params.newValue
	}
}
const currencyValueFormatter = (params) => {
	if (params.data.key == 'type') {
		return state.typeOptions[params.data.col_source] ? state.typeOptions[params.data.col_source][params.value] : params.value
	} else if (params.data.col_type == 'float') { // 逐项视图下的vlaue列 显示三位小数
		if (params.value && !Number.isInteger(params.value)) {
		// 转换为字符串
			const strNumber = JSON.stringify(params.value)
			// 判断小数部分位数
			const decimalIndex = strNumber.indexOf('.')
			if (decimalIndex !== -1 && strNumber.length - decimalIndex > 4) {
				return parseFloat(Number(params.value).toFixed(3))
			} else {
				return params.value
			}
		} else {
			return params.value
		}
	} else if (params.data.key == 'power_levels') {
		return !params.value || params.value.length <= 0 ? params.value : params.value.join(',')
	} else {
		return params.value
	}
}
const currencySourceFormatter = (params) => {
	if (params.value !== '—') {
		if (params.data.col_type == 'index_list') {
			if (Array.isArray(params.value) && params.value.length > 0) {
				const tempArr = params.value.map(item => item = state.sourceOptions[params.data.col_source][item])
				return tempArr
			} else {
				return params.value
			}
		} else {
			return state.sourceOptions[params.data.col_source][params.value]
		}
	} else {
		return params.value
	}
}

// 直流线路 可用功率档位
const currencyValueParser = (params) => {
	if (!params.newValue || params.newValue.length <= 0) return []
	const paramsCellValue = Array.isArray(params.newValue) ? params.newValue : params.newValue.split(',')
	const tempArr = paramsCellValue.map(item => {
		const num = Number(item)
		return isNaN(num) ? null : num // 如果转换结果不是数字，返回null（或者根据需要处理）
	})
	if (tempArr.includes(null)) {
		alert(t('格式错误,请输入英文逗号分隔的数值！示例：1,2,3'))
	}
	return tempArr.includes(null) ? [] : tempArr
}

const powerLevelsValueFormatter = (params) => {
	return !params.value || params.value.length <= 0 ? params.value : params.value.join(',')
}

// 单元格样式
// const cellStyle = (params) => {
// 	if (params.colDef.field == 'value' && (params.data.col_type !== 'string' && params.data.col_type !== 'float' && params.data.col_type !== 'int')) {
// 		return { backgroundColor: '#dddddd' }
// 	} else if (params.colDef.field == 'data_source' && params.data.col_type !== 'index') {
// 		return { backgroundColor: '#dddddd' }
// 	} else if (params.colDef.field == 'boolean' && params.data.col_type !== 'bool') {
// 		return { backgroundColor: '#dddddd' }
// 	} else if (params.colDef.field == 'datetime' && params.data.col_type !== 'datetime') {
// 		return { backgroundColor: '#dddddd' }
// 	} else if (params.colDef.field == 'unit') {
// 		return { backgroundColor: '#dddddd' }
// 	}
// }

// 单元格禁用
// const cellEditable = (params) => {
// 	if (params.colDef.field == 'value' && (params.data.col_type !== 'string' && params.data.col_type !== 'float' && params.data.col_type !== 'int')) {
// 		return false
// 	} else if (params.colDef.field == 'data_source' && params.data.col_type !== 'index') {
// 		return params.data.col_type == 'index_list'
// 	} else if (params.colDef.field == 'value' && params.data.key == 'type') {
// 		return false
// 	} else if (params.colDef.field == 'boolean' && params.data.col_type !== 'bool') {
// 		return false
// 	} else if (params.colDef.field == 'datetime' && params.data.col_type !== 'datetime') {
// 		return false
// 	} else if (params.colDef.field == 'unit') {
// 		return false
// 	} else {
// 		return true
// 	}
// }
// 保存
const saveAgtable = (treeVal, val, fileType) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()

	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	// allRowData.forEach((item, index) => {
	// 	if (!item.index && item.index != 0) {
	// 		item.index = Number(`-${index + 1}`)
	// 	}
	// 	return item
	// })
	// 单位切换 处理
	const columnTempDefs = JSON.parse(JSON.stringify(columnDefs.value))
	allRowData.forEach(element => {
		columnTempDefs.forEach(item => {
			for (const key in element) {
				if (item.field == key && item.col_ratio && element[key]) {
					element[key] = element[key] / item.col_ratio
				}
			}
		})
	})

	saveBaseDataApi({
		'import_string_func': 'teapcase:write_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': treeVal || state.treeValue,
			'data': allRowData,
			'confirm_flag': state.confirm_flag,
			'ignore_temp_save': val == 'ignoreTempSave' || val == 'newBuilt' || val == 'globalToSave' || val == 'newBuiltToCalculate' || val == 'saveAndClose' //  True:直接写入tc文件; False:写入临时文件;
		}
	}, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			state.isedit = false
			if (val == 'saveAndClose') return Mitt.emit('onAfterSaveClose', fileType) // 关闭程序前保存
			storeRoute.setTabs(route.fullPath, false)

			if (val == 'ignoreTempSave' || val == 'newBuilt' || val == 'globalToSave' || val == 'newBuiltToCalculate') {
				storeRoute.setSaveTabs(route.fullPath, true)
				message.success(res.func_result.message || t('保存成功') + '！')
				if (val == 'newBuilt' || val == 'newBuiltToCalculate') return Mitt.emit('saveNewFile', val) // 新建、计算 保存逻辑
			}

			if (val == 'globalToSaveTemp' || val == 'globalToSave') {
				Mitt.emit('handleUploadCase')
			}

			if (val == 'timeMatch') {
				state.timeMatchShow = true
			}
			if (val == 'paramsReplen') return handleParamsReplen(treeVal) // 参数补充
			if (val == 'copyData') return Mitt.emit('handleCopyData') // 复制行
			if (val == 'pasteData') return Mitt.emit('handlePasteData') // 粘贴行
			if (val == 'tableUpload') return Mitt.emit('handleImportFile') // 当前表导入
			if (val == 'tableDownload') return Mitt.emit('exportXls') // 当前表导出
			if (val == 'curvePreview') return Mitt.emit('handleCurve') // 曲线预览
			if (val == 'statistic') return Mitt.emit('handleStatistic') // 装机统计
			if (val == 'saveAs') return getSaveAs(fileType) // 另存为
			if (val == 'unitChange') return getAgTableData() // 修改单位
			if (val == 'showGis') return Mitt.emit('gisReadyShow')

			// if (val && val.type !== 'treeChange') {
			// 	message.success(res.func_result.message || '保存成功！')
			// }
			Mitt.emit('getTreeMenuList', 'saveRefresh')
			if (val.type && val.type == 'treeChange') {
				return Mitt.emit('onSelectTreeChange', val.treeNode_new)
				// return getSaveAs(fileType) // 另存为
			}

			// Mitt.emit('getTreeMenuList')
			getAgTableData()
		} else if (res.code == -2) {
			Modal.confirm({
				title: '注意',
				content: res.message,
				okText: t('另存为'),
				cancelText: '取消',
				onOk() {
					getSaveAs('tc')
				},
				onCancel() {
					state.confirm_flag = false
				}
			})
		} else {
			message.error(res.func_result.message || t('保存失败') + '！')
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const getSaveAs = (fileType) => {
	if (navigator.userAgent.includes('Electron') && fileType == 'tc') {
		window.electronApi.sendToMain('will_download')
		window.electronApi.receiveFromMain('download_finished', (args) => {
			Mitt.emit('SaveAs', args)
		})
	}
	DownloadCaseApi({
		'tc_filename': route.query.filePath,
		'file_type': fileType == 'v2Xlsx' ? 'xlsx' : fileType,
		'v2_flag': fileType == 'v2Xlsx'
	}, true).then(res => {
		downloadApiFile(res)
		storeLoading.hiddenModal()
		if (navigator.userAgent.includes('Electron') && fileType == 'tc') {
			basicApi({ // 另存为的时候，先生成新文件，原文件释放后台内存，新文件走临时文件保存逻辑
				'import_string_func': 'teapcase:delete_tc_instance',
				'func_arg_dict': {
					'file_name': route.query.filePath
				}
			})
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

// electron保存
const saveInElectron = (val) => {
	if (state.routePath !== route.fullPath) return
	if (state.isedit) {
		gridApi.value.stopEditing()
		const allRowData = []
		gridApi.value.forEachNode(function(node) {
			allRowData.push(node.data)
		})
		allRowData.forEach((item, index) => {
			if (!item.index && item.index != 0) {
				item.index = Number(`-${index + 1}`)
			}
			return item
		})
		saveBaseDataApi({
			'import_string_func': 'teapcase:write_to_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': state.treeValue,
				'data': allRowData,
				'confirm_flag': state.confirm_flag,
				'ignore_temp_save': true //  True:直接写入tc文件; False:写入临时文件;
			}
		}, true).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				// 是否调用数据校核
				Mitt.emit('canSave', true)
			} else {
				Mitt.emit('canSave', false)
			}
			storeLoading.hiddenModal()
		}).catch(() => {
			Mitt.emit('canSave', false)
			storeLoading.hiddenModal()
		})
	} else {
		Mitt.emit('canSave', true)
	}
}
Mitt.on('saveInElectron', saveInElectron)

// 逐项试图里的时序表保存
const saveTsimeseriestable = (treeVal, id, args) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	// const tempIdArr = []

	const tempArr = allRowData.map(item => {
		delete item.max_value
		delete item.min_value
		delete item.sum_value
		delete item.relation_count
		// tempIdArr.push(item.index)
		return item
	})

	saveBaseDataApi({
		'import_string_func': 'teapcase:update_timeseries_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': treeVal,
			'row_id': Number(id),
			'confirm': state.confirm_time_flag,
			'data': tempArr,
			'ignore_temp_save': args == 'ignoreTempSave' //  True:直接写入tc文件; False:写入临时文件;
		}
	}, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			state.confirm_time_flag = null
			state.isedit = false
			storeRoute.setTabs(route.fullPath, false)
			if (args == 'ignoreTempSave') {
				storeRoute.setSaveTabs(route.fullPath, true)
			}
			if (args == 'globalToSaveTemp' || args == 'globalToSave') {
				Mitt.emit('handleUploadCase')
			}

			if (args && args.type == 'saveAs') return getSaveAs(args.fileType)

			Mitt.emit('getTreeMenuList')

			if (args.type && args.type == 'treeChange') {
				Mitt.emit('onSelectTreeChange', args.treeNode_new)
				return
			}

			Mitt.emit('getDeviceRowClick')

			// message.success('更新成功！')
		} else if (res.code == 1 && res.func_result.code == 2) {
			const messageTemp = res.func_result.relation_ts_name_list.join('、')
			Modal.confirm({
				title: t('注意'),
				content: `${messageTemp} ${t('已关联其他设备，请确认操作')}`,
				okText: t('全部修改'),
				cancelText: t('创建副本'),
				onOk() {
					state.confirm_time_flag = true
					saveTsimeseriestable(treeVal, id)
				},
				onCancel() {
					state.confirm_time_flag = false
					saveTsimeseriestable(treeVal, id)
					storeLoading.hiddenModal()
				}
			})
		} else if (res.func_result.code !== 1) {
			message.error(res.func_result.message || t('保存失败') + '！')
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

// 时序详情保存
const saveTimeDeatilData = (id, totalHours) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	let timeArr = JSON.parse(JSON.stringify(allRowData)).map(item => {
		delete item.index
		delete item.day
		delete item.time
		item = Object.values(item)
		return item
	}).flat(Infinity)
	if (timeArr.length > totalHours) {
		timeArr = timeArr.splice(0, totalHours)
	}
	saveBaseDataApi({
		'import_string_func': 'teapcase:write_one_ts_value_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath, // 完整路径
			'row_id': id,
			'data': timeArr
		}
	}, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message || t('更新成功') + '！')
		} else {
			message.error(res.func_result.message || t('更新失败') + '！')
		}
		// storeRoute.setTabs(route.fullPath, false)
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const getAgData = () => {
	// if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	return allRowData
}

// 时序新增保存
const saveTimeAddData = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	return allRowData
}

// 时序新增
const handleSaveTimeseriesAdd = (formData) => {
	state.timeseriesFormVisible = false
	state.timeserieFormData = formData

	Mitt.emit('handleActionBar', 'saveTemp')

	state.timeseriesAddVisible = true
	nextTick(() => {
		timeseriesAddRef.value.getTimeseriesInit(state.timeserieFormData, route.query.filePath, state.isItemized ? Number(state.rowId) : null)
	})
}

// 平衡目标保存
const saveBalanceGoals = (sheet_name, group_columns, group_type, target_column) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	setTimeout(() => {
		saveBaseDataApi({
			'import_string_func': 'teapcase:update_relation_bus_data',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': sheet_name,
				'group_columns': group_columns,
				'group_type': group_type,
				'target_column': target_column,
				'org_hierarchy_list': state.org_hierarchy_list,
				'new_value_list': state.new_value_list,
				'ratio_value_list': state.ratio_value_list
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				message.success(res.func_result.message || t('更新成功') + '！')
				state.org_hierarchy_list = []
				state.new_value_list = []
				state.ratio_value_list = []
				Mitt.emit('refreshBalanceGoals')
			} else {
				message.error(res.func_result.message || t('更新失败') + '！')
				state.org_hierarchy_list = []
				state.new_value_list = []
				state.ratio_value_list = []
			}
		// storeRoute.setTabs(route.fullPath, false)
		}).catch(() => {
		// storeLoading.hiddenModal()
		})
	}, 200)
}

// 参数管理 保存
const saveParamsAgtable = (val, targetNode) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()

	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	allRowData.forEach((item, index) => {
		if (!item.index && item.index != 0) {
			item.index = Number(`-${index + 1}`)
		}
		return item
	})

	updateDeviceInitParam({
		'device_name': val,
		'data': allRowData
	}).then(res => {
		if (res.code == 1) {
			message.success(res.message || t('保存成功') + '！')
			state.isedit = false
			storeRoute.setTabs(route.fullPath, false)
			storeRoute.setSaveTabs(route.fullPath, true)
			if (targetNode) {
				Mitt.emit('onSelectParamsTreeChange', targetNode)
			}
		}
	}).catch(() => {
		// storeLoading.hiddenModal()
	})
}

// 容量平衡计算保存
const saveCapacityBalance = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	saveBaseDataApi({
		'import_string_func': 'teapcase_cb:update_capacity_balance_power_cof_table',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'data': allRowData
		}
	}).then(res => {
		if (res.code == 1) {
			setTimeDeatilData(res.func_result.capacity_balance_power_cof)
		// 	// Mitt.emit('handleGlobalSave')
		// } else {
		// 	// message.error(res.func_result.message || '更新失败！')
		}
	}).catch(() => {
		// storeLoading.hiddenModal()
	})
}

const handleSaveAgtable = (saveType) => {
	if (state.routePath !== route.fullPath) return
	// gridApi.value.stopEditing()
	if (!state.treeValue || state.treeValue == '') return
	saveAgtable(state.treeValue, saveType)
}
Mitt.on('handleSaveAgtable', handleSaveAgtable)

const stopEditing = (saveType) => {
	gridApi.value.stopEditing()
}
Mitt.on('stopEditing', stopEditing)

defineExpose({ onBtExcludeMedalColumns, setHeaderNames, onBtHide, onBtPinnedOn, setTimeDeatilData, setBlanceData, onCopyClick, onCutClick, onPasteClick,
	onAddRange, undo, redo, clearFilter, getAgTableData, insertRow, removeRow, saveAgtable, saveTimeDeatilData, getParamsAgTable, saveTimeAddData,
	handleParamsReplen, onRemoveSelected, handleTimeMatch, saveInElectron, openReplace, setBlanceGoalsData, onCount, saveParamsAgtable, handleSaveAgtable,
	onBtExport, timeseriesAdd, saveTsimeseriestable, handleCorrelateData, getSelected, saveBalanceGoals, updateData, getAgData, getReadNameData, setCurveTimeData,
	resetParamsAgTable, saveCapacityBalance, handleTypeChange
})

onMounted(async() => {

})

onBeforeMount(() => {
	// localeText.value = AG_GRID_LOCALE_ZH // 汉化文件
	rowSelection.value = 'multiple'
	// autoSizeStrategy.value = {
	// 	type: 'fitCellContents'
	// }
	tooltipShowDelay.value = 500
	statusBar.value = {
		statusPanels: [
			// { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'left' },
			{ statusPanel: 'agTotalRowCountComponent', align: 'left' },
			{ statusPanel: 'agFilteredRowCountComponent' },
			{ statusPanel: 'agSelectedRowCountComponent' },
			{ statusPanel: 'agAggregationComponent' }
		]
	}

	autoGroupColumnDef.value = {
		minWidth: 220,
		pinned: 'left'
	}

	getDataPath.value = (data) => {
		return data.orgHierarchy
	}

	// columnTypes.value = {
	// 	editableColumn: {
	// 		editable: (params) => {
	// 			return isCellEditable(params)
	// 		},
	// 		cellStyle: (params) => { 修改背景色
	// 			if (isCellEditable(params)) {
	// 				return { backgroundColor: '#2244CC44' }
	// 			}
	// 		}
	// 	}
	// }
})

</script>
<style lang="scss" scoped>
.ag-grid-body {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  ::-webkit-scrollbar {
    width:16px;
    height: 16px;
  }
  ::-webkit-scrollbar-thumb { /* 滚动条上的滚动滑块 */
    background: #92c1e4;
      border-radius: 2px;
  }
  ::-webkit-scrollbar-track { /* 滚动条的轨道 */
      background: #d1dee8;
      border-radius: 2px;
  }
  ::-webkit-scrollbar-button {
    // background: #92c1e4!important;///////////////////
    width: 16px!important;
    height: 16px!important;
    border: 0;
    // background-image: url('../../assets/bofang.png'); /* 向上箭头图标路径 */
  }
  /* Buttons */
  // ::-webkit-scrollbar-button:single-button {
  //   background-color: #bbbbbb;
  //   display: block;
  //   border-style: solid;
  //   height: 8px;
  //   width: 16px;
  // }
  /* Up */
  ::-webkit-scrollbar-button:single-button:vertical:decrement {
    // border-width: 0 8px 8px 8px;
    // border-color: transparent transparent #000 transparent;
    background-image: url('../../assets/scrollbar/up.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }

  // ::-webkit-scrollbar-button:single-button:vertical:decrement:hover {
  //   border-color: transparent transparent #777777 transparent;
  // }
  /* Down */
  ::-webkit-scrollbar-button:single-button:vertical:increment {
    background-image: url('../../assets/scrollbar/down.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }
  // ::-webkit-scrollbar-button:vertical:single-button:increment:hover {
  //   border-color: #777777 transparent transparent transparent;
  // }

  /* 自定义滚动条左箭头样式 */
  ::-webkit-scrollbar-button:horizontal:start {
    background-image: url('../../assets/scrollbar/left.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }

  /* 自定义滚动条右箭头样式 */
  ::-webkit-scrollbar-button:horizontal:end {
    background-image: url('../../assets/scrollbar/right.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }

  .ag-searchinput {
    position: absolute;
    top: 0;
    right: 0;
    /* width: 300px; */
    /* font-family: Verdana, Geneva, Tahoma, sans-serif; */
    // font-size: 13px;
    @include add-size(13px, $size);
    margin: 5px;
  }
  .cell-act {
    background: rgba(255, 0, 0, 0.1);
  }

  .cell-bud {
    background: rgba(0, 255, 0, 0.1);
  }
  .ag-theme-quartz {
    margin-top: v-bind(tableMarginTop);
    /* disable all borders */
    /* --ag-borders: none; */
    /* --ag-border-color: #333; 边框的颜色 */
    --ag-grid-size: 4px; // 文字或图标  与单元格之间的间隙

    /* --ag-header-height: 35px; 表头高度 */
    --ag-header-foreground-color: #474747; /* 表头文本和图标的颜色 */
    --ag-header-background-color: #F4F4F4; /* 表头背景色 */
    --ag-header-column-resize-handle-color: #D7D7D7; /* 表头列分割线颜色 */

    /* --ag-row-height: 30px; 行高 */
    //--ag-font-size: .875rem; /* 文字大小 */
    --ag-font-size: v-bind(store.agFontSize); /* 文字大小 */

    // @include add-ag-size(14px, $size);
    // --ag-font-family: 'SiYuan Normal';
    /* --ag-foreground-color: rgb(126, 46, 132); 主要 UI 元素（如菜单）中的文本和图标的颜色 */
    --ag-data-color: #474747; /* 单元格中文本的颜色 */
    --ag-background-color: #eef3f5; /* 表格背景色 */
    --ag-odd-row-background-color: #eff8ff;  /* 奇数行背景色 */

    --ag-active-color: #3481B9;   /* 选中的复选框、范围选择和 Quartz 主题中的输入焦点轮廓的主题色 */
    /* --ag-disabled-foreground-color 处于禁用状态而无法与之交互的元素的颜色 */
    /* --ag-row-hover-color 将鼠标悬停在网格和下拉菜单中的行上时的背景颜色。设置为 transparent 以禁用悬停效果 */
    --ag-selected-row-background-color: #74B1FA;   // 网格和下拉菜单中所选行的背景色
    /* --ag-row-border-color 网格行之间边框的颜色，或 transparent 不显示边框 */
    --ag-range-selection-border-color: #3481B9;  /* 选定单元格区域周围绘制的颜色 */
    --ag-range-selection-background-color: #88B7E2; /* 所选单元格区域的背景颜色 */

    --ag-value-change-value-highlight-background-color: transparent;

    // --ag-range-selection-highlight-color   在从单元格区域复制或粘贴到单元格区域时将其短暂应用于背景色

    // --ag-value-change-delta-up-color  当单元格数据的值在 agAnimateShowChangeCellRenderer 单元格中增加时临时应用于单元格数据的颜色
  }
  .ag-theme-quartz .ag-header-cell {
    font-size: 14px;
  }

}

.modal_source{
  .ant-modal{
    .ant-modal-body{
      >div{
        .modal_content{
          padding: 17px 35px;
          text-align: center;
          .ant-input-number .ant-input-number-input {
            width: 100%;
            height: 35px;
          }
        }

        .modal_btns{
          margin-top: 17px;
          text-align: center;
          button{
            width: 90px;
            height: 30px;
            letter-spacing: 0;
          }
        }

      }
    }
  }
}
</style>
