<!-- 可能存在问题 -->
<template>
<div class="modal_result_content">
  <div class="flex-direction">
    <div class="select_first_box">
      <div v-show="state.sim_mode !== 'long_term' && state.countMode == 'security'">
        <p>{{ $t('选择模式') }}</p>
        <a-select
          style="width: 100%"
          v-model:value="state.scene"
          :options="state.sceneList"
          :placeholder="$t('请选择模式')"
          @change="handleSceneChange"
        >
        </a-select>
      </div>
      <div>
      <p>{{ $t('选择分区') }}</p>
      <a-select
        style="width: 100%"
        v-model:value="state.partitionValue"
        :options="state.partitionOptions"
        :placeholder="$t('请选择分区')"
        @change="handlePartitionChange"
      >
      </a-select>
      </div>
    </div>

    <p>{{ $t('选择类型') }}</p>
    <div class="cascaderClass">
      <a-select
      ref="select"
      v-model:value="state.select_category"
      show-search
      :options="state.category_options_one"
      :filter-option="filterOption"
      @change="ChangeSelectCategory"
      style="width: 48%"
      ></a-select>
      <a-select
      ref="select"
      v-model:value="state.select_type"
      :options="state.type_options"
      @change="ChangeSelectType"
      style="width: 48%"
      ></a-select>
    </div>
    <div class="select_line">
      <div class="space-between align-items-center">
      <p>{{ $t('可选曲线') }}</p>
      <a-input :disabled="!state.select_category || !state.select_type" v-model:value="state.search" :placeholder="$t('请输入曲线名称')" />
      <div class="icon_list space-between ">
        <a-button :disabled="state.category_list.length==0" type="primary" shape="circle" @click="state.visible = true" size="small">
        <template #icon>
          <ZoomInOutlined />
        </template>
        </a-button>
        <a-button :disabled="!state.select_category || !state.select_type" type="primary" shape="circle" @click="download" size="small">
        <template #icon>
          <DownloadOutlined />
        </template>
        </a-button>
        <a-button :disabled="state.category_list.length==0||state.category_list.length>20||state.category_list.every(item=>item.checked)" type="primary" shape="circle" @click="selectAll" size="small">
        <template #icon>
          <CheckOutlined />
        </template>
        </a-button>
        <a-button :disabled="state.category_list.length==0||!state.category_list.some(item=>item.checked)" type="primary" shape="circle" @click="selectNone" size="small">
        <template #icon>
          <CloseOutlined />
        </template>
        </a-button>
      </div>
      </div>
      <div class="select_line_list">
      <div v-for="(item,index) in state.category_list" :key="index">
        <a-checkbox v-model:checked="item.checked" @change="changeCheck(item)">{{item.name.replace(/\*(.*?)\*/g, '')}}</a-checkbox>
      </div>
      </div>
    </div>
  </div>
  <div class="middle_line_box">
      <!-- <p>{{ state.unit }}</p> -->
      <div class="chart_select">
        <div>
          <a-select
            ref="select"
            style="width: 140px"
            v-model:value="state.pickerType"
            :placeholder="$t('颗粒度选择')"
            @change="changePickerType"
          >
            <a-select-option value="dates">{{ $t('自定义') }}</a-select-option>
            <a-select-option value="date">{{ $t('日') }}</a-select-option>
            <a-select-option value="week">{{ $t('周') }}</a-select-option>
            <a-select-option value="month">{{ $t('月') }}</a-select-option>
            <a-select-option value="quarter">{{ $t('季度') }}</a-select-option>
          </a-select>
        </div>
        <div>
          <a-range-picker v-if="state.pickerType == 'dates'" v-model:value="state.searchTime" @change="changeTime" :disabled="!state.pickerType" picker="date" :disabled-date="disabledDate" :valueFormat="'YYYY-MM-DD'" :allowClear="false" style="width: 250px"></a-range-picker>
          <a-date-picker v-else v-model:value="state.searchSingleTime" @change="changeSingleTime" :picker="state.pickerType" :disabled="!state.pickerType" :disabled-date="disabledDate" valueFormat="YYYY-MM-DD" :allowClear="false" style="width: 250px"/>
        </div>
        <a-button @click="resetEcharts">{{ $t('确认') }}</a-button>
      </div>
      <div
        class="line"
        ref="line"
        :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;`"
      >
      </div>
  </div>
  <div class="select_line">
    <div>
      <p>{{ $t('已选曲线') }}</p>
    </div>
    <div class="selected_line_list">
        <div
          v-for="(item,index) in state.select_curve_list"
          :class="getClassName(index)"
          @mouseenter="onMouseenter(item,index)"
          @mouseleave="onMouseLeave(item,index)"
          :key="index"
        >
          {{ item.name.replace(/\*(.*?)\*/g, '') }} <close-square-outlined @click="remove(item,index)" />
        </div>
    </div>
    <a-button type="primary" :disabled="state.select_curve_list.length==0" @click="clear" :style="{backgroundColor: '#454E78'}">{{ $t('全部清除') }}</a-button>
  </div>
</div>

<!-- 曲线汇总 -->
<a-modal
  wrapClassName="modal_confirm"
  :afterClose="closeModal"
  :centered="true"
  v-model:open="state.visible"
  :footer="null"
  :closable="false"
  width="575px"
  :maskClosable="false"
>
  <div class="user-select">
    <div class="modal_top">
      <p>{{ $t('曲线汇总') }}</p>
      <close-outlined class="pointer" @click="closeModal" />
    </div>
    <div class="modal_content relative">
      <Transfer v-if="state.visible" :data="state.category_list" @confirm="confirmTransfer"></Transfer>
      <div class="modal_btns">
        <a-button @click="closeModal" :style="{marginRight:'17px'}" size="small">{{ $t('取消') }}</a-button>
        <a-button @click="handleSumList" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
          <!-- <a-button @click="handleAllTable" type="primary" ghost size="small" :style="{margin:'0 17px'}"> 全部表格</a-button> -->

        </div>
    </div>
  </div>
</a-modal>
</template>
<script setup>
/* eslint-disable no-unreachable */
import { ref, reactive, inject, onMounted, computed, onUnmounted, markRaw, defineEmits, onActivated, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { CloseSquareOutlined } from '@ant-design/icons-vue'
// eslint-disable-next-line no-unused-vars
import { GetSimulationTaskResult, GetSimulationTaskResultBlob } from '@/api/index'
import { getCurveLineOptions } from '@/utils/teap.js'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
// eslint-disable-next-line no-unused-vars
import { t, downloadApiFile } from '@/utils/common.js'
import { debounce } from '@/utils/gis'
const route = useRoute()
const store = settingStore()
const { add_watermark, watermark_text, isChromeHigh } = storeToRefs(store)
const echarts = inject('ec')
const emit = defineEmits(['hideLoading', 'showLoading', 'refresh'])

const line = ref()
const lineChart = ref()

const state = reactive({
	visible: false,
	sum_list: [],
	sim_mode: '',
	select_category: '',
	select_type: '',
	category_options_all: [],
	category_options_one: [],
	category_options_all1: [],
	category_options_one1: [],
	category_options_all2: [],
	category_options_one2: [],
	type_options: [],
	category_list: [], // 可选曲线
	category_listAll: [], // 可选曲线
	select_curve_list: [], // 已选曲线 值
	category_name_one: [],
	category_name_two: [],
	category_list_base: [],
	select_power_list_a: [],
	select_status_list: [],
	timeArr: [],
	timeArr1: [],
	timeArr2: [],
	isScale: false,
	routePath: route.fullPath,
	filePath: route.query.filePath,
	activeIndex: undefined,
	search: undefined,
	unit: '',
	start: 0,
	end: 100,
	scales: '',
	zooms: 1,
	scene: 2,
	startIndex: 0,
	endIndex: 0,
	partitionValue: '',
	partitionOptions: [],
	sceneList: [
		{
			value: 2,
			label: t('保供模式')
		}
	],
	pickerType: 'dates',
	searchTime: undefined,
	searchSingleTime: undefined,
	countMode: ''
})

const changePickerType = (val) => {
	// if (state.pickerType == 'dates' && state.searchTime[0].length < 11) {
	// state.searchSingleTime = state.searchTime[0]
	// }
	state.startIndex = 0
	state.endIndex = 0
	state.pickerType == 'dates' ? changeTime() : changeSingleTime()
}

const disabledDate = (current) => {
	// const currentYear = dayjs().year()
	// const date = dayjs(current)
	// return date.year() !== state.year
	// const startDate = new Date(state.startDate)
	// const endDate = new Date(state.endDate)
	return current < new Date(state.startDate) || current > new Date(state.endDate)
}

const changeTime = () => {
	state.searchSingleTime = state.searchTime[0]
	state.startIndex = state.timeArr.findIndex(item => item == state.searchTime[0].slice(0, 10) + ' 00:00:00')
	state.endIndex = state.timeArr.findIndex(item => item == state.searchTime[1].slice(0, 10) + ' 23:00:00')
}

const changeSingleTime = () => {
	if (state.pickerType == 'date') {
		state.startIndex = state.timeArr.findIndex(item => item == state.searchSingleTime + ' 00:00:00')
		state.endIndex = state.timeArr.findIndex(item => item == state.searchSingleTime + ' 23:00:00')
	} else if (state.pickerType == 'week') {
		const startOfWeek = new Date(state.searchSingleTime).getDay() == 0 ? 7 : new Date(state.searchSingleTime).getDay()
		const endOfWeek = new Date(state.searchSingleTime).getDay() == 0 ? 7 : new Date(state.searchSingleTime).getDay()
		state.startIndex = (state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') - 24 * (startOfWeek - 1)) < 0 ? 0 : state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') - 24 * (startOfWeek - 1)
		state.endIndex = (state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') + 24 * (7 - endOfWeek) + 23) > state.timeArr.length - 1 ? state.timeArr.length - 1 : state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') + 24 * (7 - endOfWeek) + 23
	} else if (state.pickerType == 'month') {
		state.startIndex = state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00')
		const month = +state.searchSingleTime.slice(5, 7)
		const day = [1, 3, 5, 7, 8, 10, 12].includes(month) ? 31 : [4, 6, 9, 11].includes(month) ? 30 : state.year % 4 === 0 ? 29 : 28
		// state.endIndex = state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month >= 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		const lastIndex = state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month >= 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		if (state.startIndex >= 0 && lastIndex < 0) {
			state.endIndex = state.timeArr.length - 1
		} else {
			state.endIndex = lastIndex
		}
	} else if (state.pickerType == 'quarter') {
		state.startIndex = state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00')
		const month = +state.searchSingleTime.slice(5, 7) + 2
		const day = [1, 3, 5, 7, 8, 10, 12].includes(month) ? 31 : 30
		// state.endIndex = state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month > 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		const lastIndex = state.timeArr.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month > 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		if (state.startIndex >= 0 && lastIndex < 0) {
			state.endIndex = state.timeArr.length - 1
		} else {
			state.endIndex = lastIndex
		}
	}
}

const handleSceneChange = (val) => {
	state.category_options_all = state['category_options_all' + val]
	state.category_options_one = state['category_options_one' + val]
	state.search = ''
	state.select_category = ''
	state.select_type = ''
	nextTick(() => {
		state.category_list = []
	})
	state.select_curve_list = []
	initEcharts()
}

// 选择分区
const handlePartitionChange = (val) => {
	if (state.select_category == '' || state.select_type == '') return
	emit('showLoading')
	state.search = ''
	getResult()
}

// 选择类型
const ChangeSelectCategory = (val) => {
	state.category_options_all.forEach(item => {
		if (item.value == val) {
			state.type_options = item.children
		}
	})
	state.search = ''
	state.select_type = ''
	state.category_list = []
}
const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const ChangeSelectType = (val) => {
	emit('showLoading')
	state.search = ''
	if (!val) return
	getResult()
}
// 单个删除已选曲线
const remove = (item, index) => {
	state.activeIndex = undefined
	state.select_curve_list.splice(index, 1)
	state.category_listAll.forEach(val => {
		if (item.name == val.name) {
			val.checked = false
		}
	})
	initEcharts()
}
// 全部清除
const clear = () => {
	state.category_list.forEach(item => {
		state.select_curve_list.forEach(item2 => {
			if (item.name == item2.name) {
				item.checked = false
			}
		})
	})
	state.select_curve_list = []
	initEcharts()
}
// 单个 勾选/取消勾选
const changeCheck = (val) => {
	if (val.checked) {
		if (state.select_curve_list.length == 0) {
			state.select_curve_list.push(val)
		} else {
			if (val.unit.description == state.select_curve_list[0].unit.description) {
				state.select_curve_list.push(val)
			} else {
				val.checked = false
				message.warning(t('必须选择相同单位的曲线') + '！')
				return
			}
		}
	} else {
		state.select_curve_list = state.select_curve_list.filter(item => item.name !== val.name)
	}
	initEcharts()
}
// 可选曲线 全选
const selectAll = () => {
	if (state.select_curve_list.length == 0) {
		state.category_list.forEach(item => {
			if (!item.checked) {
				state.select_curve_list.push(item)
				item.checked = true
			}
		})
	} else {
		if (state.category_list[0].unit == state.select_curve_list[0].unit) {
			state.category_list.forEach(item => {
				if (!item.checked) {
					state.select_curve_list.push(item)
					item.checked = true
				}
			})
		} else {
			message.warning(t('必须选择相同单位的曲线') + '！')
			return
		}
	}
	initEcharts()
}
// 可选曲线 取消全选
const selectNone = () => {
	state.category_list.forEach(item => {
		item.checked = false
	})
	state.select_curve_list = state.select_curve_list.filter(item => {
		return !state.category_list.some(item2 => item2.name == item.name)
	})
	initEcharts()
}

const confirmTransfer = (val) => {
	state.sum_list = val
}

const closeModal = () => {
	state.visible = false
}

const handleSumList = (val) => {
	const arrays = state.sum_list.map(item => item.data)
	// 使用 map 和 reduce 进行求和
	const result = arrays[0].map((_, index) => {
		return arrays.reduce((sum, cur) => {
			return sum + cur[index]
		}, 0)
	})
	changeCheck({
		checked: true,
		data: result,
		label: `${t('汇总曲线')}${arrays.length}-${new Date().getTime()}`,
		name: `${t('汇总曲线')}${arrays.length}-${new Date().getTime()}`,
		unit: state.sum_list[0].unit
	})
	state.visible = false
}

const onMouseenter = (item, index) => {
	if (item.unit.description == '启停状态' || item.unit.description == '投运状态') return // 启停状态图 禁止highlight

	state.activeIndex = index
	lineChart.value.dispatchAction({
		type: 'highlight',
		seriesIndex: index
	})
}
const onMouseLeave = (item, index) => {
	if (item.unit.description == '启停状态' || item.unit.description == '投运状态') return // 启停状态图 禁止highlight
	state.activeIndex = undefined
	lineChart.value.dispatchAction({
		type: 'downplay',
		seriesIndex: index
	})
}
const getClassName = computed(() => (index) => {
	return index == state.activeIndex ? 'center animate ' + 'class' + (index % 10) : 'center ' + 'class' + (index % 10)
})

const resetEcharts = () => {
	if (!state.pickerType) return
	if (!state.searchTime) return

	initEcharts()
}

const initEcharts = (val, index) => {
	if (state.select_curve_list.length != 0) {
		state.unit = state.select_curve_list[0].unit.description
	} else {
		state.unit = undefined
	}

	const option = getCurveLineOptions(state.select_curve_list, state.timeArr, lineChart.value, add_watermark.value, watermark_text.value, state.startIndex, state.endIndex, state.start, state.end, state.unit)
	lineChart.value.setOption(option, true)
	lineChart.value.on('datazoom', (event) => {
		state.start = event.start
		state.end = event.end
	})
}
// 获取可选曲线
const getResult = (val) => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	GetSimulationTaskResult(url, {
		'group': state.sim_mode == 'long_term' ? `_result.result_dict.${state.select_category}.${state.select_type}.${state.partitionValue}` : state.scene == 1 ? `_result.result_dict.${state.select_category}.${state.select_type}.${state.partitionValue}` : `_result.scenario_result_dict.${state.select_category}.${state.select_type}.${state.partitionValue}`,
		'st_only': false,
		'result_file_path': state.filePath
	}).then(res => {
		if (res.code == 1) {
			state.category_listAll = state.category_list = res.name.map((item, index) => {
				return {
					checked: state.select_curve_list.some(item2 => item2.name == ('*' + res.index[index] + '*' + item)),
					name: '*' + res.index[index] + '*' + item,
					label: item,
					key: res.index[index],
					data: res.data[index].map(item => item * res.structure[state.select_type].unit[0].coefficient),
					unit: res.structure[state.select_type].unit[0]
				}
			})
			if (val) {
				state.category_list = state.category_listAll.filter(item => item.name.includes(val))
			}
		}
		emit('hideLoading')
	}).catch(() => {
		emit('hideLoading')
	})
}
// 下载
const download = () => {
	emit('showLoading')
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	GetSimulationTaskResultBlob(url, {
		result_file_path: state.filePath,
		'group': state.scene == 1 ? `_result.result_dict.${state.select_category}.${state.select_type}.${state.partitionValue}` : `_result.scenario_result_dict.${state.select_category}.${state.select_type}.${state.partitionValue}`,
		'st_only': false,
		'to_csv_format': true
	}).then(res => {
		downloadApiFile(res)
		emit('hideLoading')
	}).catch(() => {
		emit('hideLoading')
	})
}
const screenScale = (val) => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	if (val != 1) {
		if (state.routePath == route.fullPath) {
			emit('refresh', 3)
		} else {
			state.isScale = true
		}
	} else {
		if (sessionStorage.getItem(state.filePath + 'partitionValue3')) {
			state.partitionValue = sessionStorage.getItem(state.filePath + 'partitionValue3')
			sessionStorage.removeItem(state.filePath + 'partitionValue3')
		}
		if (sessionStorage.getItem(state.filePath + 'startIndex3') || sessionStorage.getItem(state.filePath + 'endIndex3')) {
			state.startIndex = +sessionStorage.getItem(state.filePath + 'startIndex3')
			state.endIndex = +sessionStorage.getItem(state.filePath + 'endIndex3')
			sessionStorage.removeItem(state.filePath + 'startIndex3')
			sessionStorage.removeItem(state.filePath + 'endIndex3')
		}
		if (sessionStorage.getItem(state.filePath + 'searchSingleTime3')) {
			state.searchSingleTime = sessionStorage.getItem(state.filePath + 'searchSingleTime3')
			sessionStorage.removeItem(state.filePath + 'searchSingleTime3')
		}
		if (sessionStorage.getItem(state.filePath + 'pickerType3')) {
			state.pickerType = sessionStorage.getItem(state.filePath + 'pickerType3')
			sessionStorage.removeItem(state.filePath + 'pickerType3')
		}
		if (sessionStorage.getItem(state.filePath + 'searchTime3')) {
			state.searchTime = JSON.parse(sessionStorage.getItem(state.filePath + 'searchTime3'))
			sessionStorage.removeItem(state.filePath + 'searchTime3')
		}
		if (sessionStorage.getItem(state.filePath + 'scene3')) {
			state.scene = +sessionStorage.getItem(state.filePath + 'scene3')
			sessionStorage.removeItem(state.filePath + 'scene3')
		}
		if (sessionStorage.getItem(state.filePath + 'startZoom3')) {
			state.start = +sessionStorage.getItem(state.filePath + 'startZoom3')
			sessionStorage.removeItem(state.filePath + 'startZoom3')
		}
		if (sessionStorage.getItem(state.filePath + 'endZoom3')) {
			state.end = +sessionStorage.getItem(state.filePath + 'endZoom3')
			sessionStorage.removeItem(state.filePath + 'endZoom3')
		}
		if (sessionStorage.getItem(state.filePath + 'select_category')) {
			state.select_category = sessionStorage.getItem(state.filePath + 'select_category')
			state.type_options = JSON.parse(sessionStorage.getItem(state.filePath + 'type_options'))
			sessionStorage.removeItem(state.filePath + 'select_category')
			sessionStorage.removeItem(state.filePath + 'type_options')
			if (sessionStorage.getItem(state.filePath + 'select_type')) {
				state.select_type = sessionStorage.getItem(state.filePath + 'select_type')
				sessionStorage.removeItem(state.filePath + 'select_type')
				if (sessionStorage.getItem(state.filePath + 'search')) {
					state.search = sessionStorage.getItem(state.filePath + 'search')
					sessionStorage.removeItem(state.filePath + 'search')
				}
			}
		}
		if (sessionStorage.getItem(state.filePath + 'select_curve_list')) {
			state.select_curve_list = JSON.parse(sessionStorage.getItem(state.filePath + 'select_curve_list'))
			sessionStorage.removeItem(state.filePath + 'select_curve_list')
		}
	}
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	sessionStorage.setItem(state.filePath + 'scene3', state.scene)
	if (state.partitionValue) sessionStorage.setItem(state.filePath + 'partitionValue3', state.partitionValue)
	sessionStorage.setItem(state.filePath + 'startIndex3', state.startIndex)
	sessionStorage.setItem(state.filePath + 'endIndex3', state.endIndex)
	sessionStorage.setItem(state.filePath + 'searchSingleTime3', state.searchSingleTime)
	sessionStorage.setItem(state.filePath + 'pickerType3', state.pickerType)
	if (state.searchTime) sessionStorage.setItem(state.filePath + 'searchTime3', JSON.stringify(state.searchTime))
	if (state.search) {
		sessionStorage.setItem(state.filePath + 'search', state.search)
	}
	if (state.select_curve_list.length != 0) {
		sessionStorage.setItem(state.filePath + 'select_curve_list', JSON.stringify(state.select_curve_list))
	}
	if (state.select_category) {
		sessionStorage.setItem(state.filePath + 'select_category', state.select_category)
		sessionStorage.setItem(state.filePath + 'type_options', JSON.stringify(state.type_options))
		if (state.select_type) {
			sessionStorage.setItem(state.filePath + 'select_type', state.select_type)
		}
	}
	sessionStorage.setItem(state.filePath + 'startZoom3', state.start)
	sessionStorage.setItem(state.filePath + 'endZoom3', state.end)
	window.removeEventListener('resize', debouncedScreenScale)
})
onActivated(() => {
	if (state.isScale) {
		emit('refresh', 3)
		state.isScale = false
	}
})
watch(() => state.search, v => {
	if (v) {
		state.category_list = state.category_listAll.filter(item => item.name.includes(v))
	} else {
		state.category_list = state.category_listAll
	}
})
const handleSessionStorage = () => {
	state.category_options_all = state['category_options_all' + state.scene]
	state.category_options_one = state['category_options_one' + state.scene]

	if (state.select_type) {
		getResult(state.search)
	} else {
		emit('hideLoading')
	}
	if (state.select_curve_list.length > 0)initEcharts()
}

const getPartition = () => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `zone`
	}
	tempQuery.result_file_path = route.query.filePath
	GetSimulationTaskResult(url, tempQuery).then(res => {
		if (res.code == 1) {
			state.partitionOptions = res.zone
			// [{
			// 	value: '',
			// 	label: '全系统'
			// }].concat(res.zone.map(item => {
			// 	return {
			// 		value: item,
			// 		label: item
			// 	}
			// }))
		}
	}).catch(() => {
		// emits('cancel')
	})
}

onMounted(() => {
	window.addEventListener('resize', debouncedScreenScale)
	emit('showLoading')
	getPartition()
	screenScale(1)
	lineChart.value = markRaw(echarts.init(line.value))
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result`,
		result_file_path: state.filePath
	}
	GetSimulationTaskResult(url, tempQuery).then(res => {
		state.sim_mode = res.sim_mode
		if (res.sim_mode == 'mid_term' && res.security_run) {
			res.accommodation_run ? state.sceneList = [
				{
					value: 2,
					label: t('保供模式')
				},
				{
					value: 1,
					label: t('促消纳模式')
				}

			] : state.sceneList = [
				{
					value: 2,
					label: t('保供模式')
				}
			]
			state.scene = 2
			state.countMode = 'security'
			Promise.all([GetSimulationTaskResult(url, {
				result_file_path: state.filePath,
				group: '_result.result_dict',
				'st_only': true
			}), GetSimulationTaskResult(url, {
				result_file_path: state.filePath,
				group: '_result.scenario_result_dict',
				'st_only': true
			})]).then(([res1, res2]) => {
				state.category_options_all1 = res1.data
				state.category_options_one1 = res1.data.map(item => {
					return {
						label: item.label,
						value: item.value
					}
				})
				state.timeArr1 = res1.time
				state.category_options_all2 = res2.data
				state.category_options_one2 = res2.data.map(item => {
					return {
						label: item.label,
						value: item.value
					}
				})
				state.timeArr2 = res2.time
				state.timeArr = state.timeArr1
				state.endIndex = state.endIndex ? state.endIndex : res1.time.length
				state.searchTime = state.searchTime ? state.searchTime : [state.timeArr[0].split(' ')[0], state.timeArr[state.timeArr.length - 1].split(' ')[0]]
				if (!state.searchSingleTime) {
					state.searchSingleTime = state.timeArr[0].split(' ')[0]
				}
				handleSessionStorage()
			})
		} else {
			state.sceneList = [
				{
					value: 1,
					label: t('促消纳模式')
				}
			]
			state.scene = 1
			state.countMode = 'accommodation'
			GetSimulationTaskResult(url, {
				result_file_path: state.filePath,
				group: '_result.result_dict',
				'st_only': true
			}).then(res => {
				const { data } = res
				state.category_options_all1 = data
				state.category_options_one1 = data.map(item => {
					return {
						label: item.label,
						value: item.value
					}
				})
				state.timeArr = res.time
				state.endIndex = state.endIndex ? state.endIndex : res.time.length
				state.searchTime = state.searchTime ? state.searchTime : [state.timeArr[0].split(' ')[0], state.timeArr[state.timeArr.length - 1].split(' ')[0]]
				if (!state.searchSingleTime) {
					state.searchSingleTime = state.timeArr[0].split(' ')[0]
				}
				handleSessionStorage()
			})
		}
	})
})
</script>
<style lang="scss" scoped>
.modal_result_content{
    width: 100%;
    height: 100%;
    >div{
      height: 100%;
      width: 50%;
    }
    >div:first-child,>div:last-child{
      width: 25%;
    }
    display: flex;
    [class*="class0"] {
        border: 2px solid rgb(84, 112, 198);
        box-shadow: rgb(84, 112, 198) 0px 2px 4px;
    }
    [class*="class1"]{
        border: 2px solid rgb(145, 204, 117);
        box-shadow: rgb(145, 204, 117) 0px 2px 4px;
    }
    [class*="class2"]{
        border: 2px solid rgb(250, 200, 88);
        box-shadow: rgb(250, 200, 88) 0px 2px 4px;
    }
    [class*="class3"]{
        border: 2px solid rgb(238, 102, 102);
        box-shadow: rgb(238, 102, 102) 0px 2px 4px;
    }
    [class*="class4"]{
        border: 2px solid rgb(115, 192, 222);
        box-shadow: rgb(115, 192, 222) 0px 2px 4px;
    }
    [class*="class5"]{
        border: 2px solid rgb(59, 162, 114);
        box-shadow: rgb(59, 162, 114) 0px 2px 4px;
    }
    [class*="class6"]{
        border: 2px solid rgb(252, 132, 82);
        box-shadow: rgb(252, 132, 82) 0px 2px 4px;
    }
    [class*="class7"]{
        border: 2px solid rgb(154, 96, 180);
        box-shadow: rgb(154, 96, 180) 0px 2px 4px;
    }
    [class*="class8"]{
        border: 2px solid rgb(234, 124, 204);
        box-shadow: rgb(234, 124, 204) 0px 2px 4px;
    }
    [class*="class9"] {
        border: 2px solid gray ;
        box-shadow: gray  0px 2px 4px;
    }
    .animate{
      // animation: shakeX 1s;
      transform: scale(1.05);
      font-size: 20px;
      font-weight: 600;
      color: #f10808!important;
    }
    p{
      font-size: 16px;
      line-height: 32px;
    }
    .select_first_box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      > div {
        width: 48%;
      }
    }
    .cascaderClass {
      display: flex;
      justify-content: space-between;
    }

}
.middle_line_box {
  position: relative;
  width: 100%;
  .chart_select {
  //   position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
  //   width: 100%;
  //   z-index: 9;
    >div{
      margin: 0 20px;
    }
  }
  .line{
    // margin-top: 10px;
  //   height: calc(100% - 42px);
    height: calc(100% - 10px);
    transform-origin: 0 0;
  }
}
.select_line{
//   height: calc(100% - 64px);
  height: calc(100% - 64px - 62px);
  z-index: 30;
  >div:first-child{
    position: relative;
    margin-top: 15px;
    border-radius: 5px 5px 0 0;
    border: 1px solid #d3d3d3;
    border-top: 3px solid #047cff;
    p{
      line-height: 50px;
      font-size: 16px;
      font-weight: normal;
      text-indent: 1em;
    }
    .ant-input{
      width: 250px;
    }
    .icon_list{
      width: 110px;
      margin-right: 10px;
      button{
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .select_line_list,.selected_line_list{
    overflow-y: auto;
    border: 1px solid #d3d3d3;
    border-top: none;
    border-radius:0 0 5px 5px;
    >div{
      font-size: 16px;
    }
  }
  .select_line_list{
    // height: calc(100% - 131px);
    height: calc(100% - 63px);
    >div{
      line-height: 16px;
      padding:8px 20px;
      span{
        font-size: 16px;
      }
    }
  }
  .selected_line_list{
    height: calc(100% - 110px);
      >div{
        height: 40px;
        margin:10px 20px;
        color: #047cff;
        &:hover{
          cursor: pointer;
        }
        span{
          margin-left: 20px;
          &:hover{
            cursor: pointer;
            color: #000;
          }
        }
      }
  }
  >button{
    width: 100%;
    margin-top: 10px;
    height: 40px;
    font-size: 16px;
  }
}
// .modal_result_content>div:last-child{
//   .select_line{
//     height: 100%;
//   }
// }

.modal_confirm{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
            img {
              width: 36px;
              height: 36px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
