<template>
  <a-modal
    wrapClassName="modal_apply"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    :keyboard="false"
    width="375px"
    :maskClosable="false"
  >
    <a-spin :spinning="state.spinning">
      <div class="user-select">
        <div class="modal_top">
          <p>{{ $t('激活软件证书') }}</p>
          <close-outlined class="pointer" @click="handleCancel" />
        </div>
        <div class="modal_content relative">
          <a-form
            :model="formState"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
          >
            <a-form-item :label="`${ $t('授权地址') }`">
              <a-input v-model:value="formState.license_server" :placeholder="$t('请输入授权服务器地址')" />
            </a-form-item>
            <a-form-item :label="`${ $t('授权码') }`">
              <a-input v-model:value="formState.cdk" :placeholder="$t('请输入授权码')" />
            </a-form-item>
          </a-form>
          <div class="modal_btns">
            <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('激活') }}</a-button>
            <a-button @click="handleCancel" size="small" :style="{marginLeft: '10px'}">{{ $t('取消') }}</a-button>
          </div>
        </div>
      </div>
    </a-spin>
    <a-modal
      wrapClassName="modal_apply"
      :centered="true"
      v-model:open="state.messageVisible"
      :footer="null"
      :closable="false"
      width="375px"
      :maskClosable="false"
      :autoClose="3000"
    >
        <div class="user-select">
          <div class="messageBox">
            <CheckCircleFilled :style="{fontSize: '36px', color: '#5bb930'}" />
            <h4>{{ $t('软件证书激活成功') }}</h4>
            <p>{{ $t('证书有效时间') }}：{{ state.exp_datetime }}</p>
            <h4>{{ $t('可用模块') }}：</h4>
            <p v-for="(item,index) in state.module_list" :key="index">{{ item }}</p>
          </div>
        </div>
    </a-modal>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'
import message from '@/utils/message'
import { applySoftLic } from '@/api/index'

const emits = defineEmits(['cancel', 'confirm'])

const formState = reactive({
	license_server: 'https://auth.tode.cn/license',
	cdk: null
})

const state = reactive({
	visible: true,
	messageVisible: false,
	spinning: false,
	exp_datetime: '',
	module_list: []
})

const handleOk = () => {
	state.spinning = true
	applySoftLic({
		license_server_address: formState.license_server,
		cdk_auth_code: formState.cdk
	}).then(res => {
		if (res.code == 1) {
			state.messageVisible = true
			state.exp_datetime = res.exp_datetime
			state.module_list = res.module_list

			setTimeout(() => {
				state.messageVisible = false
			}, 5000)
			setTimeout(() => {
				emits('confirm')
			}, 5500)
		} else {
			message.error(res.message)
			emits('confirm')
		}
		state.spinning = false
	}).catch(() => {
		state.spinning = false
	})
}

const handleCancel = () => {
	emits('confirm')
}

onMounted(() => {})

</script>
<style lang="scss" scoped>
  .modal_apply{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 25px 15px 25px 0px;
            text-align: center;
          }
          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

          .messageBox {
            padding: 17px 35px;
            text-align: center;

          }

        }
      }
    }
  }
</style>
