<template>
  <a-modal wrapClassName="modal_unit" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t('检修安排') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large" :tip="state.tip">
        <div class="modal_unit_content relative">
          <div class="modal_checked">
            <a-radio-group v-model:value="state.unitType">
              <a-radio :value="1">{{ $t('年内检修安排') }}</a-radio>
              <a-radio :value="2">{{ $t('跨年检修安排') }}</a-radio>
            </a-radio-group>
          </div>
          <p>{{ $t('参数设置') }}</p>
          <div v-show="state.unitType==1">
            <div class="modal_unit_setting">
              <div class="modal_unit_setting_scenario">
                <div>
                  <p>{{ $t('场景选择') }}:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
                  <a-select
                    v-model:value="state.scenario"
                    :options="state.scenarioOptions"
                    :placeholder="$t('请选择场景')"
                    style="width: 100%;"
                  >
                  </a-select>
                </div>
                <div>
                  <p>{{ $t('水电场景') }}:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
                  <a-select
                    v-model:value="state.hydropowerScenario"
                    :options="state.hydropowerScenarioOptions"
                    :placeholder="$t('请选择水电场景')"
                    :disabled="state.hydropowerScenarioOptions.length <= 0"
                    style="width: 100%;"
                  >
                  </a-select>
                </div>
              </div>

              <div class="modal_unit_setting_line">
                <p>{{ $t('是否考虑分区平衡') }}:</p>
                <a-radio-group v-model:value="state.zone_flag" button-style="solid" @change="onZoneFlagChange">
                  <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                  <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                </a-radio-group>
              </div>

              <div class="modal_unit_setting_table">
                <div class="modal_unit_setting_table_line">
                  <div>
                    <a-checkbox
                      v-model:checked="state.checkAll"
                      :indeterminate="state.indeterminate"
                      @change="onCheckAllChange"
                    >
                    </a-checkbox>
                  </div>
                  <div>{{ $t('类别') }}</div>
                  <div>{{ $t('可同时检修最大机组的数目') }}</div>
                  <div>{{ $t('可同时检修机组的最大容量') }}</div>
                </div>
                <div class="modal_unit_setting_table_line" v-for="(item, index) in state.tabOptions" :key="index">
                  <div>
                    <a-checkbox v-model:checked="item.checked" v-show="item.value !== 'total_gen'" ></a-checkbox>
                  </div>
                  <div>{{ item.label }}</div>
                  <div>
                    <a-input-number v-model:value="item.gensync_maxnum" :disabled="!item.checked" :formatter="formatInteger" :controls="false" style="width: 160px;">
                      <template #addonAfter>{{ $t('台') }}</template>
                    </a-input-number>
                  </div>
                  <div>
                    <a-input-number v-model:value="item.gridmaxtotalmtcap" :disabled="!item.checked" :controls="false" style="width: 160px;">
                      <template #addonAfter>MW</template>
                    </a-input-number>
                  </div>
                </div>
              </div>
              <div class="modal_unit_setting_table">
                <div class="modal_unit_setting_table_line">
                  <div>
                    <a-checkbox
                      v-model:checked="state.checkAll1"
                      :indeterminate="state.indeterminate1"
                      @change="onCheckAllChange1"
                    >
                    </a-checkbox>
                  </div>
                  <div>{{ $t('类别') }}</div>
                  <div>{{ $t('可同时检修最大机组的数目') }}</div>
                  <div>{{ $t('可同时检修机组的最大容量') }}</div>
                </div>
                <div class="modal_unit_setting_table_line" v-for="(item, index) in state.energyOptions" :key="index">
                  <div>
                    <a-checkbox v-model:checked="item.checked" v-show="item.value !== 'total_stogen'" ></a-checkbox>
                  </div>
                  <div>{{ item.label }}</div>
                  <div>
                    <a-input-number v-model:value="item.gensync_maxnum" :disabled="!item.checked" :formatter="formatInteger" :controls="false" style="width: 160px;">
                      <template #addonAfter>{{ $t('台') }}</template>
                    </a-input-number>
                  </div>
                  <div>
                    <a-input-number v-model:value="item.gridmaxtotalmtcap" :disabled="!item.checked" :controls="false" style="width: 160px;">
                      <template #addonAfter>MW</template>
                    </a-input-number>
                  </div>
                </div>
              </div>
              <div class="modal_unit_setting_checkbox">
                <div>
                  <a-checkbox v-model:checked="state.force_plan">{{ $t('强制检修') }}</a-checkbox>
                  <a-tooltip>
                    <template #title>{{ $t('勾选表示备用容量不足时仍然安排检修') }}</template>
                    <ExclamationCircleOutlined />
                  </a-tooltip>
                </div>
                <div v-show="state.zone_flag"><a-checkbox v-model:checked="state.optimize_payload">{{ $t('净负荷优化') }}</a-checkbox></div>
                <div v-show="state.zone_flag">
                  <a-checkbox v-model:checked="state.partition_transfer_limit">{{ $t('考虑线路传输功率') }}</a-checkbox>
                  <a-tooltip>
                    <template #title>{{ $t('若考虑该项，请确保区域间交直流线路参数填写正确') }}</template>
                    <ExclamationCircleOutlined />
                  </a-tooltip>
                </div>

              </div>
              <div class="modal_unit_setting_checkbox">
                <a-checkbox v-model:checked="state.arrange_maintenance_plan_flag">{{ $t('根据多年检修计划安排年内检修次数和天数') }}</a-checkbox>
                <a-checkbox  v-show="state.zone_flag" v-model:checked="state.use_min_reserve">{{ $t('按最小备用容量计算') }}</a-checkbox>
              </div>
            </div>

            <div class="modal_unit_upload">
              <p >{{ $t('检修计划上传') }}({{ $t('可选') }})</p>
              <a-upload
                v-model:file-list="state.fileList"
                v-if="state.fileList.length==0"
                name="file"
                @change="handleChange"
                accept=".csv"
                :multiple="false"
                :beforeUpload="()=>false"
                :showUploadList="false"
                >
                <a-button>
                <upload-outlined></upload-outlined>
                {{ $t('点击上传') }}
                </a-button>
              </a-upload>
              <div class="modal_unit_upload_name" v-else>
                <!-- {{ state.fileList[0].name }} -->
                <p class="ellipsis">{{ state.fileList[0].name }}</p>
                <close-outlined @click="removeFile(1)" class="pointer" />
              </div>
            </div>
            <p>{{ $t('选择生成方式') }}</p>
            <div class="modal_unit_checked display—box">
              <div>
                <a-checkbox v-model:checked="state.checked1" :disabled="state.fileList.length!=0">{{ $t('生成并下载') }}</a-checkbox>
              </div>
              <div>
                <a-checkbox v-model:checked="state.checked2" :disabled="state.fileList.length!=0">{{ $t('生成并插入算例') }}</a-checkbox>
                <a-checkbox v-if="state.checked2" v-model:checked="state.follow_scenario_flag">{{ $t('仅对应所选场景') }}</a-checkbox>
              </div>
              <div>
                <a-checkbox v-model:checked="state.checked3" :disabled="state.fileList.length==0">{{ $t('直接插入算例') }}</a-checkbox>
              </div>
            </div>
          </div>
          <div v-show="state.unitType==2">
            <div class="modal_unit_setting">
              <div>
                <p>{{ $t('跨年检修算法') }}:</p>
                <a-select v-model:value="state.unitYearType">
                  <a-select-option :value="0">{{ $t('蒙特卡罗随机抽样算法') }}</a-select-option>
                  <!-- <a-select-option :value="1">优化算法(年总检修容量均衡)</a-select-option> -->
                </a-select>
              </div>
            </div>
            <p>{{ $t('选择生成方式') }}</p>
            <div class="modal_unit_checked">
              <a-checkbox v-model:checked="state.checked1s">{{ $t('生成并下载') }}</a-checkbox>
              <a-checkbox v-model:checked="state.checked2s">{{ $t('生成并插入算例') }}</a-checkbox>
            </div>
          </div>
        </div>
        <div class="modal_btn">
          <a-button v-show="state.unitType==1" :disabled="!state.checked1&&!state.checked2&&!state.checked3" @click="confirm" type="primary">{{ $t('确认') }}</a-button>
          <a-button v-show="state.unitType==2" :disabled="!state.checked1s&&!state.checked2s" @click="confirmYears" type="primary">{{ $t('确认') }}</a-button>
          <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
        </div>
      </a-spin>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive, watch } from 'vue'
// eslint-disable-next-line no-unused-vars
import { GenMaintenancePlanConfig, GenYearsMaintenancePlanDownload,
	GenYearsMaintenancePlanInsert, GenMaintenancePlan, DownloadTempFile
} from '@/api/index'
import { downloadApiFile } from '@/utils/common.js'
import { globalParameters } from '@/api/exampleApi'
import { useRoute } from 'vue-router'
import { Modal } from 'ant-design-vue'
import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'
import { t } from '@/utils/common'
const route = useRoute()
const state = reactive({
	zoom: '',
	ifShow: true,
	tip: t('接口请求中'),
	value1: t('煤电'),
	indeterminate: false,
	checkAll: true,
	indeterminate1: false,
	checkAll1: true,
	zone_flag: false,
	unitYearType: 0,
	loading: false,
	tc_filename: undefined,
	fileList: [],
	checked1: false,
	checked2: false,
	checked1s: false,
	checked2s: false,
	checked3: false,
	downUrl: '',
	unitType: 1,
	scenario: '',
	optimize_payload: false, // 净负荷优化
	partition_transfer_limit: false, // 考虑线路传输功率
	force_plan: true, // 强制检修
	arrange_maintenance_plan_flag: false, // 根据多年检修计划安排年内检修次数和天数
	use_min_reserve: true, // 按最小备用容量计算
	// new_energy_scenario: '',
	scenarioOptions: [],
	hydropowerScenario: '',
	hydropowerScenarioOptions: [], // 水电场景
	follow_scenario_flag: false,
	tabOptions: [
		{
			checked: true,
			label: t('燃煤'),
			value: 'coal',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('燃气'),
			value: 'gas',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('生物质'),
			value: 'biomass',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('水电'),
			value: 'hydro',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('核电'),
			value: 'nuclear',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('总计'),
			value: 'total_gen',
			gensync_maxnum: 10000,
			gridmaxtotalmtcap: 2000000
		}
	],
	energyOptions: [
		{
			checked: true,
			label: t('储能'),
			value: 'battery',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('抽蓄'),
			value: 'pump',
			gensync_maxnum: 2000,
			gridmaxtotalmtcap: 400000
		},
		{
			checked: true,
			label: t('总计'),
			value: 'total_stogen',
			gensync_maxnum: 4000,
			gridmaxtotalmtcap: 800000
		}
	]
})

const onZoneFlagChange = e => {
	e.target.value ? state.tip = t('分区检修计划生成中，请耐心等待。') : state.tip = t('接口请求中')
}

const onCheckAllChange = e => {
	state.tabOptions.forEach(item => {
		if (item.value !== 'total_gen') {
			item.checked = e.target.checked
		}
	})
	state.indeterminate = false
}

const onCheckAllChange1 = e => {
	state.energyOptions.forEach(item => {
		if (item.value !== 'total_stogen') {
			item.checked = e.target.checked
		}
	})
	state.indeterminate1 = false
}

watch(() => state.tabOptions, (newValues, oldValues) => {
	const tempCheckedArr = newValues.filter(item => item.checked)
	if (tempCheckedArr.length >= 6) {
		state.indeterminate = false
		state.checkAll = true
	} else if (tempCheckedArr.length <= 1) {
		state.indeterminate = false
		state.checkAll = false
	} else {
		state.indeterminate = true
		state.checkAll = false
	}
}, { deep: true })

watch(() => state.energyOptions, (newValues, oldValues) => {
	const tempCheckedArr = newValues.filter(item => item.checked)
	if (tempCheckedArr.length >= 3) {
		state.indeterminate1 = false
		state.checkAll1 = true
	} else if (tempCheckedArr.length <= 1) {
		state.indeterminate1 = false
		state.checkAll1 = false
	} else {
		state.indeterminate1 = true
		state.checkAll1 = false
	}
}, { deep: true })

const emit = defineEmits(['close', 'refresh'])
const closeModal = () => {
	emit('close')
}
const removeFile = (val) => {
	state.fileList = []
	state.checked3 = false
}
const handleChange = () => {
	state.checked1 = false
	state.checked2 = false
}

const formatInteger = (value) => {
	return value ? `${parseInt(value, 10)}` : ''
}

const confirm = async() => {
	state.loading = true
	let url
	const formdata = new FormData()

	if (state.checked1) {
		if (state.checked2) {
			url = `/backend/teap_api_v3/gen_maintenance_plan/gen_and_insert_and_download/`
		} else {
			url = `/backend/teap_api_v3/gen_maintenance_plan/gen_and_download/`
		}
	} else if (state.checked2) {
		url = `/backend/teap_api_v3/gen_maintenance_plan/gen_and_insert/`
	} else {
		url = `/backend/teap_api_v3/gen_maintenance_plan/upload_and_insert/`
		// 上传检修计划并插入算例文件
		if (navigator.userAgent.includes('Electron')) {
			formdata.append('csv_file_path', state.fileList[0].originFileObj.path)
		} else {
			formdata.append('file', state.fileList[0].originFileObj)
		}
	}
	formdata.append('tc_filename', state.tc_filename)
	formdata.append('scenario', state.scenario) // 场景
	formdata.append('hydropower_scenario', state.hydropowerScenario) // 水电场景

	formdata.append('zone_flag', state.zone_flag) // 是否考虑分区
	formdata.append('follow_scenario_flag', state.follow_scenario_flag) // 仅对应所选场景

	const filterArr = state.tabOptions.filter(item => item.checked)
	const filterEnergyArr = state.energyOptions.filter(item => item.checked)

	if (filterArr.length <= 1 && filterEnergyArr.length <= 1) {
		state.loading = false
		return message.warning(t('请至少选择一个分区'))
	}
	if (filterArr.some(item => item.gensync_maxnum == null || item.gridmaxtotalmtcap == null) || filterEnergyArr.some(item => item.gensync_maxnum == null || item.gridmaxtotalmtcap == null)) {
		state.loading = false
		return message.warning(t('已勾选的类别，数值不能为空'))
	}

	const genMaintenanceType1 = state.tabOptions.map(item => item.checked)
	const maxNumLimit1 = state.tabOptions.map(item => item.gensync_maxnum)
	const maxPowerLimit1 = state.tabOptions.map(item => item.gridmaxtotalmtcap)

	const genMaintenanceType2 = state.energyOptions.map(item => item.checked)
	const maxNumLimit2 = state.energyOptions.map(item => item.gensync_maxnum)
	const maxPowerLimit2 = state.energyOptions.map(item => item.gridmaxtotalmtcap)

	// 机组

	const gen_number_max = filterArr.find(item => item.value == 'total_gen').gensync_maxnum
	const gen_power_max = filterArr.find(item => item.value == 'total_gen').gridmaxtotalmtcap
	const gen_total_number = state.tabOptions.filter(item => item.checked && item.value !== 'total_gen').map(item => item.gensync_maxnum).reduce((acc, cur) => acc + cur, 0)
	const gen_total_power = state.tabOptions.filter(item => item.checked && item.value !== 'total_gen').map(item => item.gridmaxtotalmtcap).reduce((acc, cur) => acc + cur, 0)
	console.log(gen_total_power)

	// 储能

	const stogen_number_max = filterEnergyArr.find(item => item.value == 'total_stogen').gensync_maxnum
	const stogen_power_max = filterEnergyArr.find(item => item.value == 'total_stogen').gridmaxtotalmtcap
	const stogen_total_number = state.energyOptions.filter(item => item.checked && item.value !== 'total_stogen').map(item => item.gensync_maxnum).reduce((acc, cur) => acc + cur, 0)
	const stogen_total_power = state.energyOptions.filter(item => item.checked && item.value !== 'total_stogen').map(item => item.gridmaxtotalmtcap).reduce((acc, cur) => acc + cur, 0)

	formdata.append('gen_maintenance_type', genMaintenanceType1.concat(genMaintenanceType2)) // 机组检修类型
	formdata.append('max_num_limit', maxNumLimit1.concat(maxNumLimit2)) // 可同时检修最大机组的数目列表
	formdata.append('max_power_limit', maxPowerLimit1.concat(maxPowerLimit2)) // 可同时检修机组的最大容量
	formdata.append('force_plan', state.force_plan)
	formdata.append('arrange_maintenance_plan_flag', state.arrange_maintenance_plan_flag)

	if (state.zone_flag) {
		formdata.append('optimize_payload', state.optimize_payload)
		formdata.append('partition_transfer_limit', state.partition_transfer_limit)
		formdata.append('use_min_reserve', state.use_min_reserve)
	}

	// 各项的总计与
	if (filterArr.length > 1 && filterEnergyArr.length > 1) {
		if (gen_number_max < gen_total_number || gen_power_max < gen_total_power || stogen_number_max < stogen_total_number || stogen_power_max < stogen_total_power) {
			getConfirm(url, formdata)
		} else {
			genMaintenancePlan(url, formdata)
		}
	} else if (filterArr.length > 1 && filterEnergyArr.length <= 1) {
		if (gen_number_max < gen_total_number || gen_power_max < gen_total_power) {
			getConfirm(url, formdata)
		} else {
			genMaintenancePlan(url, formdata)
		}
	} else if (filterArr.length <= 1 && filterEnergyArr.length > 1) {
		if (stogen_number_max < stogen_total_number || stogen_power_max < stogen_total_power) {
			getConfirm(url, formdata)
		} else {
			genMaintenancePlan(url, formdata)
		}
	} else {
		genMaintenancePlan(url, formdata)
	}
}

const getConfirm = (url, formdata) => {
	Modal.confirm({
		title: t('提示'),
		content: t('设定的机组最大同时检修容量或数量总计值小于各类型机组分项设定值之和！'),
		okText: t('确认'),
		cancelText: t('取消'),
		onOk() {
			genMaintenancePlan(url, formdata)
		},
		onCancel() {
			state.loading = false
		}
	})
}

const genMaintenancePlan = (url, formdata) => {
	GenMaintenancePlan(url, formdata).then(res => {
		if (res.code == 1) {
			if (state.checked1) {
				message.success(res.message)
				DownloadTempFile({
					'file_name': res.download_file_name
				}).then(res => {
					downloadApiFile(res)
				})
				if (state.checked2) emit('refresh')
			} else if (state.checked2) {
				// 生成检修计划并插入算例文件
				message.success(res.message)
				emit('refresh')
			} else {
				// 上传检修计划并插入算例文件
				message.success(res.message)
				emit('refresh')
			}
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}

const confirmYears = async() => {
	state.loading = true
	let res
	try {
		if (state.checked2s) {
			// 生成跨年检修计划并插入算例的API
			res = await GenYearsMaintenancePlanInsert({
				tc_filename: state.tc_filename,
				arrange_type: state.unitYearType
			})
		} else {
			// 生成并下载跨年检修计划文件的API
			res = await GenYearsMaintenancePlanDownload({
				tc_filename: state.tc_filename,
				arrange_type: state.unitYearType
			})
		}
		if (res.code == 1) {
			if (state.checked1s) {
				message.success(res.message)
				DownloadTempFile({
					'file_name': res.download_file_name
				}).then(res => {
					downloadApiFile(res)
				})
			}
			if (state.checked2s) emit('refresh')
		}
		state.loading = false
	} catch (error) {
		state.loading = false
	}
}

const getScenario = () => {
	Promise.all([GenMaintenancePlanConfig(), globalParameters({
		'import_string_func': 'teapcase:list_all_scenario',
		'func_arg_dict': {
			'file_name': route.query.filePath
		}
	})]).then(([res1, res2]) => {
		if (res1.code == 1) {
			const { max_num_limit, max_power_limit, arrange_maintenance_plan_flag, gen_maintenance_type,
				force_plan, zone_flag, optimize_payload, partition_transfer_limit, use_min_reserve } = res1.data
			state.tabOptions.forEach((item) => {
				item.gensync_maxnum = max_num_limit[item.value]
				item.gridmaxtotalmtcap = max_power_limit[item.value]
				item.checked = gen_maintenance_type[item.value]
				// if (item.value != 'total_gen') {
				// 	gen_maintenance_type.includes(item.value) ? item.checked = true : item.checked = false
				// }
			})
			state.energyOptions.forEach((item) => { // 待调整
				item.gensync_maxnum = max_num_limit[item.value]
				item.gridmaxtotalmtcap = max_power_limit[item.value]
				item.checked = gen_maintenance_type[item.value]
				// if (item.value != 'total_stogen') {
				// 	gen_maintenance_type.includes(item.value) ? item.checked = true : item.checked = false
				// }
			})

			state.arrange_maintenance_plan_flag = arrange_maintenance_plan_flag
			state.force_plan = force_plan
			state.zone_flag = zone_flag
			state.optimize_payload = optimize_payload
			state.partition_transfer_limit = partition_transfer_limit
			state.use_min_reserve = use_min_reserve
		}
		if (res2.code == 1) {
			const { scenario, hydropower_scenario } = res2.func_result

			state.scenarioOptions = scenario.data.map(item => {
				return {
					value: item,
					label: item
				}
			})
			state.hydropowerScenarioOptions = hydropower_scenario.data.map(item => {
				return {
					value: item,
					label: item
				}
			})

			if (hydropower_scenario.data.length > 0) {
				hydropower_scenario.data.includes(t('枯水年')) ? state.hydropowerScenario = t('枯水年') : state.hydropowerScenario = hydropower_scenario.data[0]
			}
		}
	}).catch(() => {
		state.loading = false
	})
}
onMounted(() => {
	state.tc_filename = route.query.filePath
	getScenario()
})
</script>
<style lang="scss">
  .modal_unit{
    // transform: scale(1);
    .ant-modal{
      width: auto!important;

      // transform-origin: 0 0; /* 控制缩放起点 */
      .ant-modal-body{
        >div{
            .modal_checked{
                display: flex;
                justify-content: center;
                padding:10px;
                span{
                    font-size: 16px;
                }
            }
          .modal_unit_content{
			min-width: 595px;
            padding: 10px 30px 70px;
            >p,>div>p{
                font-size: 18px;
                font-weight: bolder;
                line-height: 26px;
            }
            .modal_unit_setting{
                background: rgb(248, 248, 248);
                padding: 5px 20px;
                p{
                    font-size: 14px;
                    letter-spacing: 1px;
                }
                .modal_unit_setting_scenario {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  grid-gap: 20px;
                  height: 50px;
                  margin-bottom: 15px;
                }
                .modal_unit_setting_line{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    >p{
                        margin-right: 5px!important;
                    }
                    .ant-select-selector{
                        width: 290px;
                    }
                    .ant-input-number{
                        width: 140px;
                    }
                    .ant-input-number-group-addon{
                        width: 50px;
                    }
                }
                .modal_unit_setting_table {
                  width: 100%;
                  display: block;
                  margin-top: 15px;
                  .modal_unit_setting_table_line {
                    display: grid;
                    grid-template-columns: 1fr 2fr 6fr 6fr;
                    // grid-gap: 10px;
                    height: 40px;
                  }
                }
                .modal_unit_setting_checkbox{
                    display: flex;
                    justify-content: space-between;
                    margin-top: 5px;
                    // height: 50px;
                }
            }
            .modal_unit_upload{
                // padding: 10px 0;
                height: 55px;
                display: flex;
                align-items: center;
                >p {
                  font-size: 18px;
                  font-weight: bolder;
                  line-height: 30px;
                }
                .modal_unit_upload_name{
                    width: 280px;
                    display: flex;
                    justify-content: space-between;
                    border: 1px solid rgb(244, 244, 244);
                    border-radius: 5px;
                    padding: 5px;
                    p{
                        color: var(--base-color);
                        font-size: 15px;
                    }
                    span{
                        font-size: 15px;
                        background-color: rgb(244, 244, 244);
                        padding: 3px;
                        border-radius: 5px;
                        &:hover{
                            cursor: pointer;
                        }
                    }
                }
            }
            .modal_unit_checked{
              padding: 5px 0;
              .ant-checkbox-inner {
                  width: 20px;
                  height: 20px;
                  font-size: 16px;
                  // 设置对勾的 大小 和 位置
                  &::after {
                      top: 6px;
                      left: 2px;
                      width: 8px;
                      height: 16px;
                  }
              }
              .ant-checkbox-wrapper{
                margin-right: 10px;
                span{
                  font-size: 16px;
                }
              }
              >div {
                margin: 5px 0;
              }
            }
            .display—box {
              display: grid;
              grid-template-columns: 1fr 1fr 1fr;
              text-align: center;
            }
          }
        }
      }
    }
  }
</style>
