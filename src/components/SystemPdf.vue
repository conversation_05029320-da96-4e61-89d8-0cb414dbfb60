<template>
  <a-modal wrapClassName="modal_systemPdf" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t('系统帮助') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large" :tip="$t('数据加载中')">
        <div class="modal_system_content relative">
          <iframe :src="state.iframeUrl" width="100%" height="100%" title="系统帮助"></iframe>
        </div>
      </a-spin>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import network from '@/config/teap.config'
import { onMounted, reactive } from 'vue'
// import { DownLoadLogApi, getSystemConfig, updateSystemConfig } from '@/api/index'
import { CloseOutlined } from '@ant-design/icons-vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const store = settingStore()
// eslint-disable-next-line no-unused-vars
const { wsAcceptType, isNotify, systemInfo, color, fontSize, agFontSize } = storeToRefs(store)
const state = reactive({
	ifShow: true,
	iframeUrl: '',
	loading: true
})

const emit = defineEmits(['close'])

const closeModal = () => {
	emit('close')
}
onMounted(() => {
	if (navigator.userAgent.includes('Electron')) {
		const baseUrl = network.baseURL
		state.iframeUrl = `${baseUrl}backend/%E6%96%B0%E5%9E%8B%E7%94%B5%E5%8A%9B%E7%B3%BB%E7%BB%9F%E8%A7%84%E5%88%92%E7%A0%94%E7%A9%B6%E4%BB%BF%E7%9C%9F%E5%88%86%E6%9E%90%E8%BD%AF%E4%BB%B6%EF%BC%88PRSAS%EF%BC%89%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E.pdf`
	} else {
		const baseUrl = process.env.NODE_ENV == 'development' ? 'http://master.teap.tode.ltd' : window.location.origin
		state.iframeUrl = `${baseUrl}/backend/%E6%96%B0%E5%9E%8B%E7%94%B5%E5%8A%9B%E7%B3%BB%E7%BB%9F%E8%A7%84%E5%88%92%E7%A0%94%E7%A9%B6%E4%BB%BF%E7%9C%9F%E5%88%86%E6%9E%90%E8%BD%AF%E4%BB%B6%EF%BC%88PRSAS%EF%BC%89%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E.pdf`
	}
	state.loading = false
	console.log('pdfUrl', state.iframeUrl)
	// window.open(pdfUrl, '_blank')
})
</script>
<style lang="scss">
  .modal_systemPdf{
    .ant-modal{
      width: auto!important;
      .ant-modal-body{
        >div{
          .modal_system_content{
            width: 865px;
            height: 680px!important;
            padding: 0 15px;
          }
        }
      }
    }
  }
</style>
