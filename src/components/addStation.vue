<template>
    <a-modal wrapClassName="add-station" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('添加站点') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <div class="add-station-transfer relative">
                <a-transfer
                    v-model:target-keys="targetKeys"
                    :data-source="dataSource"
                    :show-search="true"
                    :filter-option="(inputValue, item) => item.name.indexOf(inputValue) !== -1"
                    :show-select-all="false"
                    @change="onChange"
                    >
                    <template
                        #children="{
                            direction,
                            filteredItems,
                            selectedKeys,
                            onItemSelectAll,
                            onItemSelect,
                        }"
                    >
                        <a-table
                        :row-selection="
                            getRowSelection({
                                selectedKeys,
                                onItemSelectAll,
                                onItemSelect,
                            })
                        "
                        :columns="direction === 'left' ? leftColumns : rightColumns"
                        :data-source="filteredItems"
                        :pagination="{
                            'simple':true
                        }"
                        :custom-row="
                            ({ key }) => ({
                                onClick: () => {
                                    onItemSelect(key, !selectedKeys.includes(key));
                                },
                            })
                        "
                        />
                    </template>
                </a-transfer>
            </div>
            <div class="modal_btn">
                <a-button :disabled="targetKeys.length===0" @click="confirm" type="primary">{{ $t('确认') }}</a-button>
                <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
            </div>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { t } from '@/utils/common'
import { CloseOutlined } from '@ant-design/icons-vue'
import { onMounted, reactive, ref } from 'vue'
const props = defineProps({
	data: {
		type: Array
	},
	targetKeys: {
		type: Array
	}
})
const state = reactive({
	ifShow: true
})
const targetKeys = ref(props.targetKeys)
const dataSource = ref(props.data.map(item => {
	return Object.assign({ ...item }, { key: (item.index).toString() })
}))
const leftTableColumns = [
	// {
	//     dataIndex: 'index',
	//     title: '序号',
	// },
	{
		dataIndex: 'name',
		title: t('名称'),
		width: 120
	},
	{
		dataIndex: 'vn_kv',
		title: t('电压等级'),
		width: 60,
		filters: [{
			text: '1050kV',
			value: 1050
		},
		{
			text: '525kV',
			value: 525
		},
		{
			text: '230kV',
			value: 230
		}
		],
		onFilter: (value, record) => record.vn_kv == (value)
	}
]
const rightTableColumns = [
	// {
	//     dataIndex: 'index',
	//     title: '序号',
	// },
	{
		dataIndex: 'name',
		title: t('名称'),
		width: 120
	},
	{
		dataIndex: 'vn_kv',
		title: t('电压等级'),
		width: 60
	}
]
const getRowSelection = ({ disabled, selectedKeys, onItemSelectAll, onItemSelect }) => {
	return {
		onSelectAll(selected, selectedRows) {
			const treeSelectedKeys = selectedRows.map(({ key }) => key)
			onItemSelectAll(treeSelectedKeys, selected)
		},
		onSelect({ key }, selected) {
			onItemSelect(key, selected)
		},
		selectedRowKeys: selectedKeys
	}
}
const leftColumns = ref(leftTableColumns)
const rightColumns = ref(rightTableColumns)
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}
const confirm = () => {
	emit('confirm', targetKeys.value.map(item => +item))
}
const onChange = nextTargetKeys => {
}
onMounted(() => {

})
</script>
<style lang="scss">
    .add-station {
        .ant-modal{
            width: auto!important;
            .ant-modal-body{
                .add-station-transfer{
                    padding: 30px 25px 85px;
                    height: 600px;
                    width: 850px;
                    .ant-table-cell{
                      padding:4px 10px!important;
                    }
                    .ant-empty-normal{
                      margin-block: 95px;
                    }
                    .ant-table-pagination{
                        margin: 8px 0;
                    }
                    .ant-table{
                        height: 338px!important;
                    }
                    .ant-table-placeholder{
                        td{
                            border: none!important;
                        }
                    }
                }
            }
        }
    }
</style>

