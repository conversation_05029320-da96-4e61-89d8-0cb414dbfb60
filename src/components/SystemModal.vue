<template>
  <a-modal wrapClassName="modal_system" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
      <div class="modal_top relative">
        <p>{{ $t('系统设置') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large">
        <div class="modal_system_content relative">
          <div class="main-content">
            <p>{{ $t('系统信息') }}</p>
            <div class="top">
              <p>{{ $t('操作系统') }}：</p>
              <p>{{ systemInfo.os_version_info }}</p>
              <p>{{ $t('处理器') }}：</p>
              <p>{{ systemInfo.cpu_vendor_info + ' '+ systemInfo.physical_cpu_count+'C'+' '+systemInfo.logical_cpu_count+'T'}}</p>
              <p>{{ $t('物理内存') }}：</p>
              <p>{{ systemInfo.virtual_memory }}</p>
              <p>{{ $t('软件版本') }}：</p>
              <p>{{ systemInfo.app_version }}</p>
            </div>
            <p>{{ $t('功能管理') }}</p>
            <div class="bottom">
              <p>{{ $t('输出日志') }}</p>
              <div class="download_log relative">
                <p>{{ $t('选择日志输出规则') }}</p>
                <a-checkbox-group v-model:value="state.checkedList"  :options="state.checkedOptions" />
                <a-button  @click="downloadLog" type="primary">{{ $t('下载日志') }}</a-button>
              </div>
              <p v-if="userAgent.includes('Electron')">{{ $t('程序目录设置') }}</p>
              <div>
                <!-- <p>
                  数据文件夹占用磁盘空间上限为
                  <a-input-number :controls="false" placeholder="未设上限" v-model:value="state.max_size" :min="0" />
                  GB
                </p>
                <p>当前占用为 {{ state.folder_size }} GB , 占比 {{ state.folder_percent }} % , 磁盘剩余空间 {{ state.free }} GB</p> -->
                <p class="result_p" v-if="userAgent.includes('Electron')">{{ $t('结果文件保存路径') }} <span @click="openFile">{{ state.result_save_path }}</span></p>
              </div>
              <p>{{ $t('字号设置') }}</p>
              <div class="font_size">
                <a-radio-group v-model:value="state.fontSize" button-style="solid">
                  <a-radio-button value="0">{{ $t('小') }}</a-radio-button>
                  <a-radio-button value="1">{{ $t('中') }}</a-radio-button>
                  <a-radio-button value="2">{{ $t('大') }}</a-radio-button>>
                </a-radio-group>
              </div>

              <p>{{ $t('完成通知') }}</p>
              <div class="download_switch">
                <a-switch v-model:checked="isNotify" :checked-children="$t('开')" :un-checked-children="$t('关')" />
              </div>

            </div>
            <p>{{ $t('参数配置') }}</p>
            <div class="bottom">
              <div class="balance_set">
                <p>{{ $t('工作位置图') }}</p>
                <!-- <FormOutlined @click="state.systemChartVisible = true" style="margin-left: 35px;color: rgb(100, 100, 100);"/> -->
                <a-button  @click="state.systemChartVisible = true" type="primary">{{ $t('自定义配置') }}</a-button>
              </div>
              <p>{{ $t('单位选择') }}</p>
              <div class="download_switch" v-for="(item,index) in state.unit_list" :key="index">
                <a-radio-group v-model:value="item.value">
                  <a-radio v-for="(item1,index1) in item.options" :key="index1" :value="item1">{{ item1 }}</a-radio>
                </a-radio-group>
              </div>
            </div>
          </div>
          <div class="bottom-btn">
            <a-button @click="confirm" type="primary">{{ $t('确认') }}</a-button>
            <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
          </div>
        </div>
      </a-spin>
    </screen-scale>
  </a-modal>
  <!-- 工作位置图 -->
  <SystemChart v-if="state.systemChartVisible"
    ref="systemChartRef"
    @cancel="state.systemChartVisible = false"
    @confirm="setBalanceChart"
    @download="downloadBalanceChart"
    :data="state.power_balance_df_structure_list"
    :default_data="state.default_power_balance_df_structure"
    :exampleData="state.balance_df_power_example_data" />
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue'
import { DownLoadLogApi, getSystemConfig, updateSystemConfig, downloadSystemConfig } from '@/api/index'
import { fileBlobFun, downloadApiFile, t } from '@/utils/common.js'
import { CloseOutlined } from '@ant-design/icons-vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'

const store = settingStore()
// eslint-disable-next-line no-unused-vars
const { wsAcceptType, isNotify, systemInfo, color, fontSize, agFontSize } = storeToRefs(store)
const state = reactive({
	ifShow: true,
	max_size: undefined,
	loading: true,
	color: color.value,
	fontSize: fontSize.value,
	agFontSize: agFontSize.value,
	folder_size: 0,
	folder_percent: 0,
	free: 0,
	checkedList: [],
	result_save_path: '',
	unit_list: [],
	power_balance_df_structure: {},
	power_balance_df_structure_list: [],
	default_power_balance_df_structure: [],
	balance_df_power_example_data: [],
	checkedOptions: [
		{ label: 'Info', value: 'info' },
		{ label: 'Warning', value: 'warning' },
		{ label: 'Error', value: 'error' }
	]
})
const userAgent = ref(navigator.userAgent)
const emit = defineEmits(['close', 'confirm'])
if (wsAcceptType.value.show_error_log) state.checkedList.push('error')
if (wsAcceptType.value.show_warning_log) state.checkedList.push('warning')
if (wsAcceptType.value.show_info_log) state.checkedList.push('info')
const downloadLog = () => {
	DownLoadLogApi().then(res => {
		downloadApiFile(res)
	})
}

const downloadBalanceChart = (data) => {
	for (const key in state.power_balance_df_structure.columns) {
		if (key != 'Time') {
			state.power_balance_df_structure.columns[key].color =	data.find(item => item.lineName == key).color
			state.power_balance_df_structure.columns[key].type =	data.find(item => item.lineName == key).type
			state.power_balance_df_structure.columns[key].sequence =	data.find(item => item.lineName == key).sequence
		}
	}
	confirm('download')
}
const setBalanceChart = (data) => {
	state.systemChartVisible = false
	for (const key in state.power_balance_df_structure.columns) {
		if (key != 'Time') {
			state.power_balance_df_structure.columns[key].color =	data.find(item => item.lineName == key).color
			state.power_balance_df_structure.columns[key].type =	data.find(item => item.lineName == key).type
			state.power_balance_df_structure.columns[key].sequence =	data.find(item => item.lineName == key).sequence
		}
	}
	confirm()
}

const closeModal = () => {
	emit('close')
}
const openFile = () => {
	window.electronApi.waitToMain('openFolder', 'openFolder').then(res => {
		if (res) state.result_save_path = res
	})
}
const confirm = (val) => {
	updateSystemConfig({
		max_size: state.max_size ? state.max_size : 0,
		finish_notify: isNotify.value,
		show_info_log: !!state.checkedList.includes('info'),
		show_warning_log: !!state.checkedList.includes('warning'),
		show_error_log: !!state.checkedList.includes('error'),
		result_save_path: state.result_save_path,
		display_unit_config: state.unit_list.map(item => item.value),
		power_balance_df_structure: state.power_balance_df_structure
	}).then(res => {
		if (res.code == 1) {
			store.fontSize = state.fontSize
			const root = document.documentElement
			root.setAttribute('data-size', state.fontSize)
			localStorage.setItem('fontSize', state.fontSize)

			state.agFontSize = state.fontSize == '2' ? '18px' : state.fontSize == '1' ? '16px' : '14px'
			localStorage.setItem('agFontSize', state.agFontSize)
			store.changeAgFontSize(state.agFontSize)

			store.changeWsAcceptType(state.checkedList.includes('error'), state.checkedList.includes('info'), state.checkedList.includes('warning'))
			if (val == 'download') { // 保存完成后 下载工作位置图自定义配置
				downloadSystemConfig().then(res => {
					fileBlobFun(res.data, t('工作位置图自定义配置') + '.json')
				})
			} else {
				emit('confirm')
			}
		}
	})
}
onMounted(() => {
	getSystemConfig().then(res => {
		if (res.code == 1) {
			const { power_balance_df_structure, default_power_balance_df_structure, balance_df_power_example_data } = res
			state.folder_size = res.folder_size
			state.folder_percent = res.folder_percent
			state.free = res.free
			state.max_size = res.max_size == 0 ? undefined : res.max_size
			state.result_save_path = res.result_save_path
			state.unit_list = res.unit_config.map((item, index) => {
				return {
					options: item,
					value: res.display_unit_config[index]
				}
			})
			state.balance_df_power_example_data = balance_df_power_example_data
			state.power_balance_df_structure = power_balance_df_structure
			state.power_balance_df_structure_list = Object.keys(power_balance_df_structure.columns).filter(item => item != 'Time').map((item, index) => Object.assign({ lineName: item, key: item }, power_balance_df_structure.columns[item]))
			state.default_power_balance_df_structure = Object.keys(default_power_balance_df_structure.columns).filter(item => item != 'Time').map((item, index) => Object.assign({ lineName: item, key: item }, default_power_balance_df_structure.columns[item]))
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
})
</script>
<style lang="scss">
  .modal_system{
    .ant-modal{
      width: 50%!important;
      .ant-modal-body{
        >div{
          .modal_system_content{
              width: 100%;
              padding: 15px 30px;
              overflow: auto;
              p{
                line-height: 40px;
              }
              font-size: 22px;
              color: #000;
              .main-content {
                width: 100%;
                >p{
                  font-weight: bolder;
                  font-size: 18px;
                  position: relative;
                  &::after{
                    content: '';
                    display: block;
                    height: 1px;
                    width: 88%;
                    background-color: #ccc;
                    position: absolute;
                    right: 0;
                    top: 50%;
                  }
                }
                >div{
                  padding:5px 30px;
                }
                .top{
                  display: grid;
                  grid-template-columns: 1fr 4fr;
                  p{
                      font-size: 16px;
                      color: gray;
                  }
                  p:nth-of-type(2n+1){
                      font-weight: bolder;
                      color: #000;
                  }
                }

                .bottom{
                  .ant-input-number{
                    margin: 0 10px;
                  }
                    p{
                        font-size: 16px;
                        display: flex;
                        align-items: center;
                    }
                    >p{
                        font-size: 16px;
                        font-weight: bolder;
                    }

                    .result_p{
                      span{
                        width: 500px;
                        border: 1px solid #d9d9d9;
                        border-radius: 5px;
                        padding: 0 10px;
                        line-height: 30px;
                        max-width: 500px;
                        margin-left: 10px;
                        min-height: 30px;
                        &:hover{
                          cursor: pointer;
                        }
                      }
                    }
                }
                .download_log{
                  padding: 0px 20px;
                  margin-bottom: 10px;
                  .ant-checkbox-group{
                      span{
                          font-size: 18px;
                          color: #000;
                      }
                      .ant-checkbox-inner {
                          width: 20px;
                          height: 20px;
                          font-size: 16px;
                          &::after {
                              top: 7px;
                              left: 2px;
                              width: 8px;
                              height: 16px;
                          }
                      }
                  }
                  button{
                    position: absolute;
                    bottom: 10px;
                    right: 0;
                    font-size: 16px;
                    line-height: 20px;
                    padding: 0 30px;
                    border-radius: 10px;
                  }
                }
                .font_size {
                  padding: 0px 20px;
                  margin-bottom: 10px;
                }
                .download_switch{
                  padding: 0px 20px;
                  margin-bottom: 10px;
                    .ant-switch{
                      width: 80px;
                      height: 28px;
                      .ant-switch-inner{
                          span{
                              font-size: 16px;
                          }
                          .ant-switch-inner-checked{
                              line-height: 26px;
                          }
                          .ant-switch-inner-unchecked{
                              line-height: 18px;
                          }
                      }
                      .ant-switch-handle{
                          height: 24px;
                          width: 24px;
                          top: 2px;
                      }
                      .ant-switch-handle::before{
                        border-radius: 100%;
                      }

                  }
                  .ant-switch-checked{
                      .ant-switch-handle{
                          left: calc(100% - 23px - 2px);
                      }
                  }
                }
                .balance_set {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 10px;
                  p{
                    line-height: 40px;
                    font-weight: bolder;
                    color: #000;
                  }
                }

              }

              .bottom-btn {
                width: 100%;
                text-align: right;
                button{
                  // width: 90px;
                  // height: 30px;
                  // letter-spacing: 0;
                  padding: 0 25px;
                  margin-left: 25px;
                }
              }

          }
        }
      }
    }
  }
</style>
