<template>
    <a-modal wrapClassName="modal_load" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('负荷生成') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <a-spin :spinning="state.loading" size="large">
                <div class="modal_content relative">
                    <div>
                        <p>{{ $t('预测条件设置') }}</p>
                        <div class="modal_setting">
                            <div class="modal_setting_item">
                                <p>{{ $t('预测年份') }}：</p>
                                <a-select v-model:value="state.year" :options="state.yearList">
                                </a-select>
                            </div>
                            <div class="modal_setting_item">
                                <p>{{ $t('预测区域') }}：</p>
                                <a-select mode="multiple" v-model:value="state.area" :options="state.areaList" @change="changeArea">
                                </a-select>
                            </div>
                            <div class="modal_setting_item">
                                <p>{{ $t('历史参考') }}：</p>
                                <a-select :disabled="state.disabled" v-model:value="state.hist_year" :options="state.hist_year_list">
                                </a-select>
                            </div>
                            <div class="modal_setting_item">
                                <p>{{ $t('极端系数') }}：</p>
                                <a-input-number v-model:value="state.extreme_ratio" max="100" min="-100">
                                    <template #addonAfter>%</template>
                                </a-input-number>
                            </div>
                        </div>
                        <p>{{ $t('参数配置') }}（{{ $t('可选') }}）</p>
                        <div class="modal_setting modal_setting_sp">
                            <div class="modal_setting_item">
                                <p>{{ $t('全年最大负荷') }}：</p>
                                <a-input-number v-model:value="state.max_power_limit" :controls="false">
                                <template #addonAfter>{{ $t('万千瓦') }}</template>
                                </a-input-number>
                            </div>
                            <div class="modal_setting_item">
                                <p>{{ $t('全年总用电量') }}：</p>
                                <a-radio-group v-model:value="state.powerType" name="radioGroup">
                                    <a-radio :value="1">{{ $t('总量') }}</a-radio>
                                    <a-radio :value="2">{{ $t('分行业电量') }}</a-radio>
                                </a-radio-group>
                            </div>
                            <div v-show="state.powerType==1">
                                <div class="modal_setting_item">
                                    <p>{{ $t('未来年份总用电量') }}：</p>
                                    <a-input-number v-model:value="state.pred_power_consumption" :controls="false">
                                        <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                    </a-input-number>
                                </div>
                            </div>
                            <div v-show="state.powerType==2">
                                <div class="modal_setting_item">
                                    <p>{{ $t('第一产业用电量') }}：</p>
                                    <a-input-number v-model:value="state.pred_fir_power" :controls="false">
                                        <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                    </a-input-number>
                                </div>
                                <div class="modal_setting_item">
                                    <p>{{ $t('第二产业用电量') }}：</p>
                                    <a-input-number v-model:value="state.pred_sec_power" :controls="false">
                                        <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                    </a-input-number>
                                </div>
                                <div class="modal_setting_item">
                                    <p>{{ $t('第三产业用电量') }}：</p>
                                    <a-input-number v-model:value="state.pred_tri_power" :controls="false">
                                        <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                    </a-input-number>
                                </div>
                                <div class="modal_setting_item">
                                    <p>{{ $t('城乡居民生活用电量') }}：</p>
                                    <a-input-number v-model:value="state.pred_city_power" :controls="false">
                                        <template #addonAfter>{{ $t('亿千瓦时') }}</template>
                                    </a-input-number>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <p>{{ $t('关联负荷') }}（{{ $t('可选') }}）</p>
                        <div class="modal_transfer">
                            <Transfer  v-if="state.transferShow" :data="state.stationList" :disabled="state.area && state.area.length > 1" @confirm="confirmTransfer"></Transfer>
                        </div>
                    </div>
                </div>
                <div class="modal_btn">
                    <a-button class="btn_sp" @click="state.loadHistoryShow=true">
                        <template #icon>
                            <SettingOutlined />
                        </template>
                        <a-tooltip>
                          <template #title>{{ $t('历史负荷管理') }}</template>
                          {{ $t('历史负荷管理') }}
                        </a-tooltip>
                    </a-button>
                    <a-button class="btn_sps" @click="state.modelShow=true">
                        <template #icon>
                            <ControlOutlined />
                        </template>
                        <a-tooltip>
                          <template #title>{{ $t('模型参数管理') }}</template>
                          {{ $t('模型参数管理') }}
                        </a-tooltip>
                    </a-button>
                    <a-button @click="confirm" type="primary">{{ $t('确认') }}</a-button>
                    <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
                </div>
            </a-spin>
        </screen-scale>
        <curve-modal v-if="state.modalShow" type="load" :state="state" @close="close"></curve-modal>
        <model-setting v-if="state.modelShow" @close="closeModel"></model-setting>
        <load-history :areaList="state.areaList" v-if="state.loadHistoryShow" @close="closeLoadHistory"></load-history>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive, h } from 'vue'
import { getBaseDataApi } from '@/api/exampleApi'
import { AllLoadForecastArea, LoadForecastDownload, LoadHistoryQueryView } from '@/api/index'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'
import { t } from '@/utils/common'
/*eslint-disable no-unused-vars*/
const route = useRoute()
const state = reactive({
	ifShow: true,
	loadHistoryShow: false,
	isEdit: false,
	type: 1,
	loading: false,
	tc_filename: undefined,
	year: undefined,
	area: undefined,
	pred_fir_power: undefined,
	pred_sec_power: undefined,
	pred_tri_power: undefined,
	pred_city_power: undefined,
	stationList: [],
	yearList: [],
	areaList: [],
	index_list: [],
	modalShow: false,
	modelShow: false,
	transferShow: false,
	loadData: {},
	hist_year_list: [],
	hist_year: undefined,
	extreme_ratio: undefined,
	disabled: true,
	caseYear: undefined,
	powerType: undefined,
	max_power_limit: undefined,
	pred_power_consumption: undefined
})
const emit = defineEmits(['close', 'refresh'])
const renderFunc = (item) => {
	return h('p', { onDblclick: (e) => handleDoubleClick(item, e) }, item.label)
}
const handleDoubleClick = (item, event) => {
	if (state.index_list.includes(item.key)) {
		state.index_list = state.index_list.filter(items => items != item.key)
	} else {
		state.index_list.push(item.key)
	}
}
const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const closeModel = () => {
	state.modelShow = false
}
const closeLoadHistory = () => {
	state.loadHistoryShow = false
	if (state.area) {
		changeArea()
	}
}
const changeArea = () => {
	state.loading = true
	state.disabled = true
	state.hist_year = undefined
	if (state.area.length == 0 || !state.area) {
		state.loading = false
		return
	}
	LoadHistoryQueryView({
		area: state.area
	}).then(res => {
		state.hist_year_list = res.hist_years.map((item, index) => {
			return {
				label: item,
				value: item,
				flag: res.load_hist_flag[index]
			}
		}).filter(item => item.flag)
		if (state.hist_year_list.length == 0) {
			message.warning(t('请至少提供两年的历史用电量数据和一年的时序负荷曲线'))
		} else if (state.hist_year_list.length == 1) {
			state.hist_year = state.hist_year_list[0].label
		} else {
			state.disabled = false
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}
const closeModal = () => {
	emit('close')
}
const close = (val) => {
	state.modalShow = false
	if (val) {
		emit('refresh')
	}
}

const confirmTransfer = (val) => {
	state.index_list = val.map(item => item.key)
}

const confirm = async() => {
	if (!state.area) {
		message.warning(t('请选择预测区域！'))
		return
	}
	if (state.year == undefined) {
		message.warning(t('请选择预测年份！'))
		return
	}
	if (state.hist_year == undefined) {
		message.warning(t('请选择历史参考年份！'))
		return
	}
	state.loading = true
	LoadForecastDownload(Object.assign({
		pred_year: state.year,
		area: state.area,
		hist_year: state.hist_year,
		extreme_ratio: state.extreme_ratio,
		max_power_limit: state.max_power_limit
	},
	state.powerType == 1 ? {
		pred_power_consumption: state.pred_power_consumption
	} : {
		pred_sec_power: state.pred_sec_power,
		pred_tri_power: state.pred_tri_power,
		pred_city_power: state.pred_city_power
	},
	state.year == 0 ? {
		tc_filename: state.tc_filename
	} : {}
	)).then(res => {
		state.loading = false
		state.loadData = res.data
		state.caseYear = res.pred_year
		state.modalShow = true
	}).catch(() => {
		state.loading = false
	})
}
onMounted(() => {
	state.isEdit = (route.query.type == 'isEditor' || route.query.type == 'isNewBuilt' || route.query.type == 'firstSave' || route.query.type == 'isResultBuilt')
	state.tc_filename = route.query.filePath
	state.yearList = (state.isEdit ? [{
		value: 0,
		label: t('算例时间范围')
	}] : []).concat([...Array(81)].map((item, index) => {
		return {
			// value: index + new Date().getFullYear() + 1,
			// label: index + new Date().getFullYear() + 1
			value: index + 2019,
			label: index + 2019
		}
	}))
	AllLoadForecastArea({}).then(res => {
		state.areaList = res.data.map((item, index) => {
			return {
				label: item,
				value: item
			}
		})
	})
	if (state.isEdit) {
		state.loading = true
		getBaseDataApi({
			'import_string_func': 'teapcase:read_name_col_from_tc',
			'func_arg_dict': {
				file_name: state.tc_filename,
				sheet_names: ['load']
			}
		}).then(res => {
			state.stationList = res.func_result.data.load.map((item, index) => {
				return {
					label: item.name,
					key: (item.index).toString()
				}
			})
			state.transferShow = true
			state.loading = false
		}).catch(() => {
			state.loading = false
		})
	}
})
</script>
<style lang="scss">
.modal_load{
    .ant-modal{
        width: auto!important;
        .ant-modal-body{
            >div{
                .modal_content{
                    padding: 10px 30px 80px;
                    display: flex;
                    justify-content: space-between;
                    >div{
                        min-width: 400px;
                        >p{
                            font-size: 18px;
                            font-weight: bolder;
                            line-height: 40px;
                        }
                    }
                    >div:first-child{
                        margin-right: 20px;
                    }
                    >div:last-child{
                        width: 500px;
                    }
                    .modal_setting{
                        background: rgb(248, 248, 248);
                        margin-bottom: 10px;
                        padding: 15px 40px 15px;
                        .modal_setting_item{
                            margin: 10px 0;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 20px;
                            p{
                                line-height: 30px;
                                text-align: right;
                                min-width: 70px;
                            }
                            .ant-select-selector{
                                width: 240px;
                            }
                            .ant-input-number{
                                width: 205px;
                            }
                        }
                        >div:last-child{
                            margin-bottom: 5px;
                        }
                    }
                    .modal_setting_sp{
                        margin-bottom: 0px;
                        .modal_setting_item{
                            p{
                                width: auto;
                            }
                            .ant-input-number{
                                width: 100px;
                            }
                            .ant-input-number-group-addon{
                                width: 80px;
                            }
                        }
                    }
                    .modal_transfer{
                        background: rgb(248, 248, 248);
                        padding: 15px 20px 20px;
                        .ant-transfer-list{
                            // height: 377px;
                            height: 429px;
                            width: 220px;
                            background-color: #fff;
                        }
                        // .ant-table-cell{
                        //   padding:4px 10px!important;
                        // }
                        // .ant-table-body{
                        //   min-height: 223px;
                        // }
                        // .ant-empty-normal{
                        //   margin-block: 72px;
                        // }
                    }
                }
                .modal_btn{
                    button{
                        letter-spacing: 0;
                        overflow: hidden; /* 隐藏溢出的内容 */
                        font-size: 12px;
                    }
                    .btn_sp{
                        width: 180px;
                        background-color: rgb(223, 237, 246);
                        border-color: var(--base-color);
                        color: var(--base-color);
                    }
                    .btn_sps{
                        width: 180px;
                        background-color: var(--base-color);
                        border-color: var(--base-color);
                        color: #fff;
                        margin-right: 205px
                    }
                }
            }
        }
    }
}
</style>

