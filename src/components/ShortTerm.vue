<!-- 滚动模拟 -->
<template>
    <div class="global-main-plan">
        <div class="main-left">
          <p>{{ $t('参数设置') }}</p>
          <div :class="['parameterSet-short']">
                <div>
                    <p>{{ $t('上备用率') }}</p>
                    <a-input-number v-model:value="stateBase.load_resv_up_coe" :min="0" addon-after="%"></a-input-number>
                </div>
                <div>
                    <p>{{ $t('下备用率') }}</p>
                    <a-input-number v-model:value="stateBase.load_resv_down_coe" :min="0" addon-after="%"></a-input-number>
                </div>
                <div>
                    <p>{{ $t('风电备用率') }}</p>
                    <a-input-number v-model:value="stateMore.simulation.wind_resv_up_coe" :min="0" addon-after="%"></a-input-number>
                </div>
                <div>
                    <p>{{ $t('光伏备用率') }}</p>
                    <a-input-number v-model:value="stateMore.simulation.solar_resv_up_coe" :min="0" addon-after="%"></a-input-number>
                </div>
          </div>
          <p>{{ $t('仿真设置') }}</p>
          <div :class="['solutionMethod']">
                <div class="solutionMethod-top">
                    <div>
                        <p>{{ $t('仿真时间') }}</p>
                        <a-range-picker v-model:value="state.simulation_short_time" valueFormat="YYYY-MM-DD" @change="handleChangeShortTime"/>
                    </div>
                    <div>
                        <p>{{ $t('仿真步长') }}</p>
                        <a-select ref="select" v-model:value="stateMore.simulation.sim_freq" :style="{width: '100%'}">
                            <a-select-option value="M" disabled>{{ $t('分钟') }}</a-select-option>
                            <a-select-option value="H">{{ $t('小时') }}</a-select-option>
                            <a-select-option value="D" disabled>{{ $t('天') }}</a-select-option>
                        </a-select>
                    </div>
                </div>
                <div  class="solutionMethod-bottom">
                    <div>
                        <p>{{ $t('场景选择') }}</p>
                        <a-select
                            v-model:value="stateCase.scenario_selected"
                            :style="{width: '100%'}"
                            :options="state.scenariOptions"
                            @change="handleChange"
                        ></a-select>
                    </div>
                    <div>
                        <p>{{ $t('水电场景') }}</p>
                        <a-select
                            v-model:value="stateCase.hydropower_scenario"
                            style="width: 100%"
                            :options="state.hydropowerOptions"
                            :disabled="state.hydropowerOptions.length <= 0"
                        >
                        </a-select>
                    </div>
                </div>
            </div>
            <p>{{ $t('滚动计算设置') }}</p>
            <div :class="['setContent-balance-reduce-short', state.solution_method == 'simulate' ? 'shortTerm-reduce' : '']">
                    <div class="setContent-balance-way">
                        <div>
                            <p>{{ $t('单次滚动小时数') }}</p>
                            <a-input-number v-model:value="stateMore.scuc.roll_snap" :min="1"></a-input-number>
                        </div>
                        <div>
                            <p>{{ $t('前瞻小时数') }}</p>
                            <a-input-number v-model:value="stateMore.scuc.lookahead_snap" :min="0"></a-input-number>
                        </div>
                        <div>
                            <p>{{ $t('时序最大分段数') }}</p>
                            <a-input-number v-model:value="stateMore.scuc.max_num_jobs" :min="1"></a-input-number>
                        </div>
                    </div>
            </div>
            <p>{{ $t('案例备注') }}</p>
            <div :class="['remarkBox']">
                <a-textarea v-model:value="stateCase.description" :placeholder="$t('请输入')+'...'" :rows="2" />
            </div>
        </div>
        <div class="main-right-shortTerm">
            <!-- <p>专业参数设置</p> -->
            <p @click="state.shortTermShow = !state.shortTermShow">{{ $t('专业参数设置') }}<CaretRightOutlined v-show="!state.shortTermShow"/><CaretDownOutlined v-show="state.shortTermShow"/></p>
            <div class="main-right-shortTerm-content">
                <div
                v-show="state.shortTermShow"
                :class="['setContent-set-short']"
                >
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('负荷松弛惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.load_relax_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.load_relax_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.load_relax_cost_auto" :min="0" v-model:value="stateBase.load_relax_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input-number></a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('支路过载惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.branch_relax_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.branch_relax_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">
                                {{ $t('指定值') }}
                                <a-input-number :disabled="stateAuto.branch_relax_cost_auto" :min="0" v-model:value="stateBase.branch_relax_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input-number>
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('负荷上调量上限百分比') }}(%)</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.short_term.simulation.load_increase_limit_percentage).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.load_increase_limit_percentage_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">
                                {{ $t('指定值') }}
                                <a-input-number :disabled="stateAuto.load_increase_limit_percentage_auto" :min="0" v-model:value="stateMore.simulation.load_increase_limit_percentage" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input-number>
                                </a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('弃风惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.wind_curtailment_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.wind_curtailment_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">
                                {{ $t('指定值') }}
                                <a-input-number :disabled="stateAuto.wind_curtailment_cost_auto" :min="0" v-model:value="stateBase.wind_curtailment_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input-number>
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('弃光惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.solar_curtailment_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.solar_curtailment_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">
                                {{ $t('指定值') }}
                                <a-input-number :disabled="stateAuto.solar_curtailment_cost_auto" :min="0" v-model:value="stateBase.solar_curtailment_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input-number>
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('弃水惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.water_curtailment_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.water_curtailment_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">
                                {{ $t('指定值') }}
                                <a-input-number :disabled="stateAuto.water_curtailment_cost_auto" :min="0" v-model:value="stateBase.water_curtailment_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input-number>
                                </a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div>
                            <p>{{ $t('启停约束') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.consider_start_shut_flag" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('爬坡约束') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.consider_ramp_flag" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('备用约束') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.consider_reserve_flag" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div>
                            <p>{{ $t('网架约束') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.consider_branch_flag" button-style="solid" @change="considerBranchChange">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('断面约束') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.consider_interface_flag" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('考虑网损') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.consider_transmission_losses" button-style="solid" :disabled="!stateMore.simulation.consider_branch_flag">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div>
                            <p>{{ $t('模型选择') }}</p>
                            <a-select ref="select" v-model:value="stateMore.simulation.pcs_objective_function" :style="{width: '92%'}">
                                <a-select-option value="minimize_production_cost">{{ $t('全社会运行成本最低') }}</a-select-option>
                                <a-select-option value="minimize_co2_emission">{{ $t('碳排放最低') }}</a-select-option>
                            </a-select>
                        </div>
                        <div>
                            <p>{{ $t('分段首时刻无启停成本') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.zero_beginning_onoff_cost_per_process" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('指定出力机组必开') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.gen_p_bounds_forced_must_on" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div>
                            <p>{{ $t('时序分解重叠天数') }}</p>
                            <a-input-number v-model:value="stateMore.scuc.overlap_number" :min="0" style="width: 92%" :placeholder="$t('请输入')"></a-input-number>
                        </div>
                        <div class="advancedSet_line_input">
                            <p>{{ $t('并行计算进程数') }}</p>
                            <div class="defaultVal">{{ parseFloat(state.recommended_parallel_number).toLocaleString() }}</div>
                            <a-input-number v-model:value="stateMore.scuc.n_parallel" :min="1" style="width: 92%" :placeholder="$t('请输入')"></a-input-number>
                        </div>
                        <div>
                        <p>{{ $t('最大回滚次数') }}</p>
                        <a-input-number v-model:value="stateMore.scuc.max_rollback" :min="0" style="width: 92%" :placeholder="$t('请输入')"></a-input-number>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div>
                            <p>{{ $t('单次默认求解时间上限') }}（s）</p>
                            <a-input v-model:value="stateMore.scuc.solver_timelimit" style="width: 92%" :placeholder="$t('请输入')" />
                        </div>
                        <div>
                            <p>{{ $t('单次最大求解时间上限') }}（s）</p>
                            <a-input v-model:value="stateMore.scuc.upscaled_solver_timelimit" style="width: 92%" :placeholder="$t('请输入')" />
                        </div>
                        <div>
                            <p>MIP Gap(%)</p>
                            <a-input v-model:value="stateMore.simulation.mipgap" :style="{'width': '92%'}" :placeholder="$t('请输入')" />
                        </div>
                    </div>
                    <!-- <div class="advancedSet_line">
                        <div>
                            <p>水电提供备用模式</p>
                            <a-select v-model:value="stateMore.simulation.hydro_curt_as_reserve" style="width: 98%">
                                <a-select-option :value="true">水电弃水时提供备用</a-select-option>
                                <a-select-option :value="false">水电开机时即可提供备用</a-select-option>
                            </a-select>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
        <div class="main-btn">
            <a-button style="width: 96px" @click="handleReset">{{ $t('重置') }}</a-button>
            <a-button
                :style="{'width': '96px', 'backgroundColor': '#dbe9f4'}"
                @click="confirm(false)">{{ $t('仅保存') }}</a-button>
            <a-button
                type="primary"
                style="width: 96px"
                @click="confirm(true)">{{ $t('计算') }}</a-button>
        </div>
    </div>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import Mitt from '@/utils/mitt.js'
import { onMounted, reactive, ref, watch, defineComponent } from 'vue'
import message from '@/utils/message'
const props = defineProps({
	stateDefault: {
		type: Object
	},
	stateSetting: {
		type: Object
	},
	stateAuto: {
		type: Object
	},
	stateBase: {
		type: Object
	},
	state: {
		type: Object
	},
	isReady: {
		type: Boolean
	}
})
const VNodes = defineComponent({
	props: {
		vnodes: {
			type: Object,
			required: true
		}
	},
	render() {
		return this.vnodes
	}
})
const state = reactive({
	shortTermShow: true,
	simulation_short_time: props.state.simulation_short_time,
	hydropowerOptions: props.state.hydropowerOptions,
	solution_method: props.state.solution_method,
	gapLongAdjustValue: null,
	gapLongValue: null
})
const stateDefault = reactive({
	general_parameters: {

	},
	short_term: {
		simulation: {

		}
	}
})
const stateCase = ref({

})
const stateBase = ref({

})
const stateAuto = ref({

})
const stateMore = ref({
	simulation: {

	},
	fitted: {

	},
	partial: {

	},
	scuc: {

	}
})
const inputLongRef = ref()
const inputLongAdjustRef = ref()
const emit = defineEmits(['reset', 'saveSetting'])
const handleChange = val => {
	Mitt.emit('changeTree', val)
}
// 网架关闭 才能 网损关闭
const considerBranchChange = (e) => {
	if (!e.target.value) {
		stateMore.value.simulation.consider_transmission_losses = false
	}
}
const handleChangeShortTime = (val) => {
	stateMore.value.simulation.start_datetime = `${val[0]} 00:00:00`
	stateMore.value.simulation.end_datetime = `${val[1]} 23:00:00`
}
onMounted(() => {

})
const handleReset = () => {
	emit('reset')
}
const confirm = (val) => {
	emit('saveSetting', val ? 3 : false, 'short_term', {
		stateCase: stateCase.value,
		stateBase: stateBase.value,
		stateAuto: stateAuto.value,
		stateMore: stateMore.value
	})
}
watch(() => props.isReady, (val) => {
	stateAuto.value = props.stateAuto
	stateBase.value = props.stateBase
	stateCase.value = props.stateSetting.case_info
	stateDefault.general_parameters = props.stateDefault.general_parameters
	stateDefault.short_term = props.stateDefault.short_term
	stateMore.value = props.stateSetting.short_term
	state.simulation_short_time = props.state.simulation_short_time
	state.recommended_parallel_number = props.state.recommended_parallel_number
	state.hydropowerOptions = props.state.hydropowerOptions
	state.solution_method = props.state.solution_method
}, { immediate: true })
</script>
<style lang="scss">

</style>
