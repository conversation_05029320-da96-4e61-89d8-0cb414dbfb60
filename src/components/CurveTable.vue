<!-- eslint-disable no-unused-vars -->
<template>
  <a-modal
    wrapClassName="curve_modal"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.ifShow"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
          <p>{{ props.type == 'preview' ? $t('曲线预览') : $t('绑定情况检查') }}</p>
          <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <div class="modal_content">
        <div v-if="props.type == 'check'" class="modal_bind">
          <a-radio-group v-model:value="state.bindType" button-style="solid" @change="bindTypeChange">
            <a-radio-button value="multiple">{{ $t('被多个设备绑定') }}</a-radio-button>
            <a-radio-button value="none">{{ $t('未被绑定') }}</a-radio-button>
          </a-radio-group>
        </div>
          <div class="agClass">
            <AgGrid ref="agGridRef" :isEdit="'curveTimeseries'"></AgGrid>
          </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
// eslint-disable-next-line no-unused-vars
import { onMounted, reactive, ref, watch, nextTick } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
// import { getBaseDataApi } from '@/api/exampleApi'
// import { useRoute } from 'vue-router'
// import message from '@/utils/message'
// import { Table } from 'ant-design-vue'

// const route = useRoute()

const emit = defineEmits(['close', 'refresh'])

const props = defineProps({
	type: {
		type: String,
		default: ''
	}
})

// AG-Grid表格
const agGridRef = ref(null)

const state = reactive({
	ifShow: true,
	bindType: 'multiple',
	tableData: {},
	data: []

})

const closeModal = () => {
	emit('close')
}

const bindTypeChange = () => {
	const tempData = state.tableData
	if (state.bindType == 'none') {
		tempData.data = state.data.filter(item => item.relation_count == 0)
	} else {
		tempData.data = state.data.filter(item => item.relation_count > 1)
	}

	nextTick(() => {
		agGridRef.value.setCurveTimeData(tempData)
	})
}

const getInitData = (tableData) => {
	tableData.columns.forEach(item => {
		item.editable = false
	})

	state.tableData = tableData
	state.data = tableData.data

	if (props.type == 'check') {
		tableData.data = tableData.data.filter(item => item.relation_count > 1)
	}

	nextTick(() => {
		agGridRef.value.setCurveTimeData(tableData)
	})
	// getBaseDataApi(
	// 	{
	// 		'import_string_func': 'teapcase:read_relation_ts_from_tc',
	// 		'func_arg_dict': {
	// 			'sheet_name': props.treeNode,
	// 			'file_name': route.query.filePath
	// 		}
	// 	}
	// ).then(res => {
	// 	if (res.code == 1) {
	// 		const { func_result } = res

	// 		state.tableData = func_result.timeseries

	// 		nextTick(() => {
	// 			agGridRef.value.setCurveTimeData(state.tableData)
	// 		})
	// 	}
	// }).catch(err => {
	// 	console.log(err)
	// })
}

defineExpose({ getInitData })

onMounted(async() => {

})
</script>
<style lang="scss">
  .curve_modal{
    .ant-modal{
      width: 70%!important;
      .ant-modal-body{
        >div{
            .modal_content{
                position: relative;
                width: 100%;
                padding: 15px 20px 30px;
                .modal_bind {
                  position: absolute;
                  z-index: 30;
                }
                .agClass {
                  width: 100%;
                  height: 580px;
                }
            }
        }
      }
    }
  }
</style>

