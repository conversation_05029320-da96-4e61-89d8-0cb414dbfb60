<template>
  <div class="itemized-main">
    <div class="device-left">
      <div class="device-list">
        <div class="device-title">{{ $t('设备列表') }}</div>
        <div class="device-table">
          <a-table
            class="ant-table-striped"
            :columns="timeseriesColumns"
            :data-source="state.deviceData"
            :rowClassName="rowClassName"
            :scroll="{ y: 240 }"
            :customRow="customRow"
            size="small"
            :pagination="false"
            bordered
          >
            <template #bodyCell="{column, record, index}">
              <template v-if="column.key === 'index'">
                <span>{{ index + 1 }}</span>
              </template>
              <template v-if="column.key === 'name'">
                <span>{{ record.name }}</span>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <div class="device-cor">
        <div class="device-cor-title">{{ $t('关联设备') }}</div>
        <div class="device-cor-card">
          <div  v-for="(item, index) in state.outsideData" :key="index" class="device-cor-card-line">
            <div class="device-cor-card-line-title">
              <CaretDownOutlined v-show="item.checked" @click="item.checked = !item.checked"/>
              <CaretRightOutlined v-show="!item.checked" @click="item.checked = !item.checked"/>
              <!-- <img src="@/assets/toolbar-icon/start/网侧内部.png" alt=""> -->
              {{ item.type }}
            </div>

            <a-table
              v-show="item.checked"
              :columns="item.columns"
              :data-source="item.data"
              :scroll="{ x: 400 }"
              bordered
              size="small"
              :pagination="false"
              row-class-name="table-striped"
            >
              <!-- <template #bodyCell="{ column, text, record }"> -->
                <!-- <template v-if="typeof record[column.dataIndex] === 'boolean'">
                  <div>
                    <a-radio-group v-if="state.isEdit" v-model:value="editableData[column.dataIndex]" style="margin: -5px 0" name="radioGroup">
                      <a-radio :value="true">是</a-radio>
                      <a-radio :value="false">否</a-radio>
                    </a-radio-group>
                    <template v-else>
                      {{ text === true ? '是' : text }}
                    </template>
                  </div>
                </template>
                <template v-else-if="column.key == 'bus' ||column.key == 'from_bus' || column.key == 'to_bus'">
                  <div>
                    <a-select
                      v-if="state.isEdit"
                      v-model:value="editableData[column.dataIndex]"
                      show-search
                      :placeholder="$t('请输入')"
                      :options="state.busOptions"
                      :field-names="{ label: 'name', value: 'index' }"
                      :filter-option="filterOption"
                      style="width: 100%"
                    ></a-select>
                    <template v-else>
                      {{ text }}
                    </template>
                  </div>
                </template>
                <template v-else-if="column.key == 'timeseries'">
                  <div>
                    <a-select
                      v-if="state.isEdit"
                      v-model:value="editableData[column.dataIndex]"
                      show-search
                      :placeholder="$t('请输入')"
                      :mode="state.isMultiple"
                      :options="state.timeOptions"
                      :field-names="{ label: 'name', value: 'index' }"
                      :filter-option="filterOption"
                      style="width: 100%"
                    ></a-select>
                    <template v-else>
                      {{ text }}
                    </template>
                  </div>
                </template> -->
                <!-- <template>
                  {{ text }}
                </template> -->

              <!-- </template> -->
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <div class="line-right">
      <div class="line-title">
        <div>{{ state.treeName }}</div>
        <!-- <div v-show="!state.isEdit" @click="edit()"><EditOutlined /></div> -->
        <div v-show="state.isEdit" class="line-title-icon">
          <!-- <close-circle-filled :style="{fontSize: '16px', color: '#333'}" @click.stop="cancel"/>
          &nbsp; -->
          <check-circle-filled :style="{fontSize: '16px', color: '#729ec8'}" @click.stop="save(record)"/>

        </div>
      </div>
      <div class="line-table">
        <div class="paramsItem" v-for="(item, index) in state.paramsData" :key="index">
          <div class="paramsTableName"><span>{{ item.type }}</span></div>
          <div class="paramsTable">
            <a-table
              :columns="item.columns"
              :data-source="item.tableData"
              :scroll="{ x: 800, y: 80 }"
              bordered
              size="small"
              :pagination="false"
              row-class-name="table-striped"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="typeof record[column.dataIndex] === 'boolean'">
                  <div>
                    <a-radio-group v-if="state.isEdit" v-model:value="editableData[column.dataIndex]" style="margin: -5px 0" name="radioGroup">
                      <a-radio :value="true">{{$t('是')}}</a-radio>
                      <a-radio :value="false">{{$t('否')}}</a-radio>
                    </a-radio-group>
                    <template v-else>
                      {{ text === true ? $t('是'): text }}
                    </template>
                  </div>
                </template>
                <template v-else-if="column.key == 'bus' ||column.key == 'from_bus' || column.key == 'to_bus'">
                  <div>
                    <a-select
                      v-if="state.isEdit"
                      v-model:value="editableData[column.dataIndex]"
                      show-search
                      :placeholder="$t('请输入')"
                      :options="state.busOptions"
                      :field-names="{ label: 'name', value: 'index' }"
                      :filter-option="filterOption"
                      style="width: 100%"
                    ></a-select>
                    <template v-else>
                      {{ text }}
                    </template>
                  </div>
                </template>
                <template v-else-if="column.key == 'timeseries'">
                  <div>
                    <!-- <a-radio-group v-if="state.isEdit" v-model:value="editableData[column.dataIndex]" style="margin: -5px 0" name="radioGroup">
                      <a-radio :value="true">是</a-radio>
                      <a-radio :value="false">否</a-radio>
                    </a-radio-group> -->
                    <a-select
                      v-if="state.isEdit"
                      v-model:value="editableData[column.dataIndex]"
                      show-search
                      :placeholder="$t('请输入')"
                      :mode="state.isMultiple"
                      :options="state.timeOptions"
                      :field-names="{ label: 'name', value: 'index' }"
                      :filter-option="filterOption"
                      style="width: 100%"
                    ></a-select>
                    <template v-else>
                      {{ text }}
                    </template>
                  </div>
                </template>
                <template v-else>
                  <div>
                    <a-input
                      v-if="state.isEdit"
                      v-model:value="editableData[column.dataIndex]"
                      style="margin: -5px 0"
                    />
                    <template v-else>
                      {{ text }}
                    </template>
                  </div>
                </template>

              </template>
            </a-table>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getReadNameCol, getReadOneRow, basicApi } from '@/api/exampleApi'
import { t } from '@/utils/common.js'
const route = useRoute()

// const emit = defineEmits(['hideLoading'])

const state = reactive({
	isEdit: false,
	deviceData: [],
	paramsData: [],
	outsideData: [],
	rowId: null,
	treeName: '',
	busOptions: [],
	timeOptions: [],
	isMultiple: 'multiple'
})

const timeseriesColumns = ref([
	{
		title: t('序号'),
		dataIndex: 'index',
		key: 'index',
		width: '50px',
		align: 'center',
		ellipsis: true
	},
	{
		title: t('名称'),
		dataIndex: 'name',
		key: 'name',
		ellipsis: true
	}
])

const rowClassName = (record, index) => {
	if (record.index == state.rowId) {
		console.log(789, record)
		return 'clickRowStyl'
	} else if (index % 2 === 1) {
		return 'table-striped'
	} else {
		return ''
	}
}

const customRow = (record, index) => {
	return {
		props: {
			// draggable: 'true'
		},
		style: {
			// cursor: 'pointer'
		},
		onClick: () => {
			// console.log(8909, record)
			state.rowId = record.index
			deviceRowClick(record.index)
		}

	}
}

const filterOption = (input, option) => {
	return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const editableData = ref({})
// const edit = () => {
// 	state.paramsData.forEach(item => {
// 		Object.assign(editableData.value, item.tableData[0])
// 	})
// 	state.isEdit = true
// }

const save = key => {
	basicApi({
		'import_string_func': 'teapcase:write_one_row_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath, // # 要带扩展名.h5
			'sheet_name': sessionStorage.getItem('treeType'), // # 此参数可以不传，也可以为空列表，两种情况都会返回所有表
			'row_id': state.rowId,
			'data_dict': editableData.value
		}

	}).then(res => {
		if (res.code == 1) {
			Mitt.emit('updateOneRow')
			deviceRowClick(state.rowId)
		}
	})
}
// const cancel = () => {
// 	state.isEdit = false
// }

const deviceRowClick = (id) => {
	getReadOneRow({
		'import_string_func': 'teapcase:read_one_row_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath, // # 要带扩展名.h5
			// 'file_path': fileName_h5.value,
			'sheet_name': sessionStorage.getItem('treeType'), // # 此参数可以不传，也可以为空列表，两种情况都会返回所有表
			'row_id': id
		}
	}).then(res => {
		if (res.code == 1) {
			const { data, outside_data_list } = res.func_result
			state.outsideData = outside_data_list.map(item => {
				item.checked = false
				// item.columns = item.columns.map(item => {
				// 	item.width = 150
				// 	return item
				// })
				// item.columns.forEach(element => {
				// 	element.width = 120
				// })
				item.columns.shift()
				return item
			})

			// item.columns.shift()
			console.log(898989, state.outsideData)

			state.paramsData = data.map(item => {
				item.tableData = [{}]
				item.data.forEach(ele => {
					item.tableData[0][ele.key] = ele.value
				})
				item.columns = item.data.map(ele => {
					return {
			      title: ele.name,
			      dataIndex: ele.key,
			      key: ele.key,
						align: 'center',
			      ellipsis: true,
						width: 150
					}
				})
				return item
			})
			state.paramsData.forEach(item => {
				Object.assign(editableData.value, item.tableData[0])
			})
			console.log(789, state.paramsData)
			state.isEdit = true
		}
	})
}

const getReadNameColData = (val, tree) => {
	state.treeName = tree
	getReadNameCol({
		'import_string_func': 'teapcase:read_name_col_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath, // # 要带扩展名.h5
			// 'file_path': fileName_h5.value,
			'sheet_names': [val, 'bus', 'timeseries'] // # 此参数可以不传，也可以为空列表，两种情况都会返回所有表
		}
	}).then(res => {
		if (res.code == 1) {
			const { data } = res.func_result
			state.deviceData = data[val || sessionStorage.getItem('treeType')]
			state.busOptions = data.bus
			state.timeOptions = data.timeseries
			state.paramsData = []
	    state.outsideData = []
		}
	})
}

// const onClick = (column, text, record) => {
// 	console.log(8888, column, text, record)
// }

defineExpose({ getReadNameColData })

onMounted(() => {

})
</script>
<style lang="scss" scoped>
.itemized-main {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // display: grid;
  // grid-template-columns: 1fr 2fr;
  // grid-column-gap: 18px;
  display: flex;
  justify-content: space-between;
  padding-top: 4px;
  .device-left {
    width: 500px;
    height: 100%;
    background: #F6F6F6;
    box-sizing: border-box;
    // border: 1px solid #A2ADB8;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .device-list {
      width: 100%;
      height: 49%;
      .device-title {
        width: 100%;
        height: 36px;
        padding-left: 22px;
        border-radius: 6px;
        background: #ECECEC;
        box-sizing: border-box;
        border: 1px solid #A2ADB8;
        font-size: 15px;
        color: #474747;
        line-height: 36px;
      }
      .device-table {
        width: 100%;
        height: calc(100% - 38px);
        border-radius: 6px;
        background: #F6F6F6;
        box-sizing: border-box;
        border: 1px solid #A2ADB8;
        .ant-table-striped :deep(.table-striped) td {
          background-color: #f8f8f8;
        }
        :deep(.clickRowStyl) td {
          background-color: rgb(184, 199, 216)!important;
        }
      }
    }
    .device-cor {
      width: 100%;
      height: 49%;
      .device-cor-title {
        width: 100%;
        height: 36px;
        padding-left: 22px;
        border-radius: 6px;
        background: #ECECEC;
        box-sizing: border-box;
        border: 1px solid #A2ADB8;
        font-size: 15px;
        color: #474747;
        line-height: 36px;
      }
      .device-cor-card {
        width: 100%;
        height: calc(100% - 38px);
        border-radius: 6px;
        background: #F4F4F4;
        box-sizing: border-box;
        border: 1px solid #A2ADB8;
        font-size: 15px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0px;
        color: #4E4E4E;
        overflow-y: auto;
        overflow-x: hidden;
        .device-cor-card-line {
          width: 100%;
          // padding: 5px 10px 5px 10px;
          padding:  5px 10px;
          // margin-bottom: 15px;
          border-bottom: 1px dashed #A2ADB8;
          box-sizing: border-box;
          .device-cor-card-line-title {
            line-height: 30px;
            // border-bottom: 1px dashed #A2ADB8;
          }
          // .device-cor-card-item {
          //   height: 60px;
          //   display: flex;
          //   flex-wrap:nowrap;
          //   margin-right: 10px;
          //   border-bottom: 1px dashed #A2ADB8;
          //   line-height: 30px;
          //   .device-cor-card-item-id {
          //     width: 60px;
          //   }
          //   .device-cor-card-item-content {
          //     width: 150px;
          //     margin: 0 2px;
          //     >div {
          //       white-space: nowrap; /* 不换行 */
          //       overflow: hidden;    /* 隐藏超出部分 */
          //       text-overflow: ellipsis; /* 使用省略号表示被裁切的文本 */
          //     }
          //   }

          // }
          // .device-cor-card-item:last-child {
          //   border: 0;
          // }

          // img {
          //   width: 16px;
          //   height: 16px;
          //   margin-right: 5px;
          // }

        }
      }
    }
  }
  .line-right {
    width: calc(100% - 518px);
    height: 100%;
    background: #F4F4F4;
    box-sizing: border-box;
    // border: 1px solid #A2ADB8;
    .line-title {
      width: 100%;
      height: 36px;
      padding: 0 15px;
      border-radius: 6px;
      background: #ECECEC;
      box-sizing: border-box;
      border: 1px solid #A2ADB8;
      line-height: 36px;
      display: flex;
      justify-content: space-between;
    }
    .line-table {
      width: 100%;
      height: calc(100% - 38px);
      padding: 13px;
      border-radius: 6px;
      background: #F4F4F4;
      box-sizing: border-box;
      border: 1px solid #A2ADB8;
      line-height: 36px;
      overflow-y: auto;
      .paramsItem {
        width: 100%;
        height: 122px;
        padding: 2px 13px 13px 13px;
        border-radius: 6px;
        background: #FAFAFA;
        box-sizing: border-box;
        border: 1px solid #AEADAD;
        margin-top: 25px;
        .paramsTableName {
          font-size: 15px;
          font-weight: normal;
          line-height: normal;
          letter-spacing: 0px;
          color: #424246;
        }
        .paramsTable {
          // :deep(.ant-table-tbody > tr > td){
          //   padding: 0px;
          // }
          // :deep(.ant-table-thead > tr > th){
          //   padding: 8px 0;
          //   text-align: center;
          //   background: #E3ECF4;
          //   border: 1px solid #AEADAD;
          // }
        }
      }
      .paramsItem:first-child {
        margin-top: 0;
      }
    }
  }
  .line-right {
    width: calc(100% - 518px);
    height: 100%;
    background: #F4F4F4;
    box-sizing: border-box;
    // border: 1px solid #A2ADB8;
    .line-title {
      width: 100%;
      height: 36px;
      padding: 0 15px;
      border-radius: 6px;
      background: #ECECEC;
      box-sizing: border-box;
      border: 1px solid #A2ADB8;
      line-height: 36px;
      display: flex;
      justify-content: space-between;
    }
    .line-table {
      width: 100%;
      height: calc(100% - 38px);
      padding: 13px;
      border-radius: 6px;
      background: #F4F4F4;
      box-sizing: border-box;
      border: 1px solid #A2ADB8;
      line-height: 36px;
      overflow-y: auto;
      .paramsItem {
        width: 100%;
        height: 122px;
        padding: 2px 13px 13px 13px;
        border-radius: 6px;
        background: #FAFAFA;
        box-sizing: border-box;
        border: 1px solid #AEADAD;
        margin-top: 25px;
        .paramsTableName {
          font-size: 15px;
          font-weight: normal;
          line-height: normal;
          letter-spacing: 0px;
          color: #424246;
        }
        .paramsTable {
          // :deep(.ant-table-tbody > tr > td){
          //   padding: 0px;
          // }
          // :deep(.ant-table-thead > tr > th){
          //   padding: 8px 0;
          //   text-align: center;
          //   background: #E3ECF4;
          //   border: 1px solid #AEADAD;
          // }
        }
      }
      .paramsItem:first-child {
        margin-top: 0;
      }
    }
  }

}
</style>
