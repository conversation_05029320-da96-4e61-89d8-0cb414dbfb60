<template>
    <div :class="['tab-container',props.className]" ref="tabContainer">
        <div :class="['tab-item',state.activeKey==item.value?'active':'']" v-for="(item) in props.tabData" :key="item.value" @click="clickTab(item)">
            {{item.label}}
        </div>
        <div class="tabs-underline" :style="{left:state.left+'px',width:state.width+'px'}"></div>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted, watch, onUnmounted } from 'vue'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const emit = defineEmits(['close', 'change', 'update:modelValue'])
const tabContainer = ref()
const props = defineProps({
	tabData: {
		type: Array,
		default: () => []
	},
	className: {
		type: String,
		default: ''
	},
	modelValue: {
		type: String,
		required: true
	}
})
const state = reactive({
	activeKey: props.modelValue,
	scales: 1
})
const clickTab = (item) => {
	if (state.activeKey == item.value) return
	emit('change', item.value)
}
watch(() => props.modelValue, (newValue, oldValue) => {
	state.activeKey = newValue
	getUnderLine()
})
const getUnderLine = () => {
	const childDom = tabContainer.value.children[props.tabData.findIndex(item => item.value == state.activeKey)]
	if (childDom) {
		state.width = childDom.clientWidth
		const rect = childDom.getBoundingClientRect()
		const parentRect = tabContainer.value.getBoundingClientRect()
		state.left = (rect.left - parentRect.left) / state.scales
	}
}
const screenScale = () => {
	if (isChromeHigh.value) {
		state.scales = document.getElementsByClassName('home-body')[0].style.zoom || 1
	}
	getUnderLine()
}
const debouncedScreenScale = debounce(screenScale, 200)
onMounted(() => {
	debouncedScreenScale()
	if (isChromeHigh.value) window.addEventListener('resize', debouncedScreenScale)
})
onUnmounted(() => {
	if (isChromeHigh.value) window.removeEventListener('resize', debouncedScreenScale)
})
</script>
<style lang="scss" scoped>
    .tab-container{
        display: flex;
        align-items: center;
        position: relative;
        .tab-item{
            margin: 0 0 0 32px;
            font-size: 14px;
            padding: 12px 15px;
            line-height: 4px;
            cursor: pointer;
            box-sizing: border-box;
            font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
            &:hover{
                color: #5a95cc;
            }
        }
        .active{
            color: #3678bf;
            text-shadow: 0 0 0.25px currentcolor;
            &:hover{
                color: #3678bf;
            }

        }
        >div:first-child{
            margin: 0;
        }
        .tabs-underline{
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background-color: #3678bf;
            transition: all .3s;
        }
    }
</style>
