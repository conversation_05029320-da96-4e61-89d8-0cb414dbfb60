<template>
  <div class="transfer-modal">

    <div class="transfer-title">
      <div>
        <a-checkbox
          v-model:checked="state.checkAll"
          :indeterminate="state.indeterminate"
          @change="onCheckAllChange"
        >
          {{ $t('全选') }}
        </a-checkbox>
      </div>
      <div>{{ $t('已选择') }}</div>
    </div>
    <div class="transfer-box">
      <div>
        <div class="transfer-search">
          <a-input-search
            v-model:value="state.searchSource"
            :placeholder="$t('搜索')"
            style="width: 100%"
            @search="onSearchSource"
          />
        </div>
        <div class="transfer-content">
          <RecycleScroller
            v-slot="{ item }"
            ref="recycleScrollRef"
            class="RecycleScrollerClass"
            :item-size="34"
            :items="state.sourceOptions"
            key-field="key"
          >
            <a-col :span="24" >
              <a-checkbox v-model:checked="item.checked" :disabled="props.disabled" @change="onCheckChange(item)">{{ item.label }}</a-checkbox>
            </a-col>
          </RecycleScroller>

        </div>
      </div>
      <div>
        <div class="transfer-search">
          <a-input-search
            v-model:value="state.searchTarget"
            :placeholder="$t('搜索')"
            style="width: 100%"
            @search="onSearchTarget"
          />
        </div>
        <div class="transfer-content">
          <RecycleScroller
            v-slot="{ item }"
            ref="recycleScrollRef1"
            class="RecycleScrollerClass"
            :item-size="34"
            :items="state.targetOptions"
            key-field="key"
          >
            <a-col :span="24">
              <CloseSquareOutlined @click="removeItem(item)" /> {{ item.label }}
            </a-col>
          </RecycleScroller>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { reactive, onMounted, watch } from 'vue'
// import { SelectAllLabel } from 'ant-design-vue/es/transfer'
// import { useRoute } from 'vue-router'
// import message from '@/utils/message'
// import { getReadNameCol } from '@/api/exampleApi'

const props = defineProps({
	disabled: {
		type: Boolean,
		default: false
	},
	data: {
		type: Array,
		default: () => []
	}
})

const emits = defineEmits(['cancel', 'confirm'])

const state = reactive({
	isMultiple: 'multiple',
	allData: [],
	checkAll: false,
	indeterminate: false,
	searchSource: '',
	searchTarget: '',
	sourceValue: [],
	sourceOptions: [],
	targetOptions: []
})
const onSearchSource = searchValue => {
	if (state.sourceValue.length == 0) {
		state.checkAll = false
		state.indeterminate = false
	} else if (state.sourceValue.length < state.allData.length) {
		state.checkAll = false
		state.indeterminate = true
	} else {
		state.checkAll = true
		state.indeterminate = false
	}

	state.sourceOptions = state.allData
	state.sourceOptions = state.sourceOptions.filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onSearchTarget = searchValue => {
	state.targetOptions = state.allData.filter(item => state.sourceValue.includes(item.key)).filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onCheckAllChange = e => {
	state.sourceOptions.forEach(item => {
		item.checked = e.target.checked
	})
	state.allData.forEach(item => {
		if (state.sourceOptions.find(item1 => item1.key == item.key)) {
			item.checked = e.target.checked
		}
	})
	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	state.indeterminate = false
}

const onCheckChange = (val) => {
	state.sourceOptions.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.allData.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	changeIndeterminate()
}

const removeItem = (val) => {
	if (props.disabled) return

	state.sourceOptions.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})
	state.allData.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})

	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	changeIndeterminate()
}

// 单条增删 修改全选框的状态
const changeIndeterminate = () => {
	if (state.targetOptions.length == 0) {
		state.checkAll = false
		state.indeterminate = false
	} else if (state.targetOptions.length < state.allData.length) {
		state.checkAll = false
		state.indeterminate = true
	} else {
		state.checkAll = true
		state.indeterminate = false
	}
}

watch(() => state.targetOptions, (newValues, oldValues) => {
	emits('confirm', newValues)
})

onMounted(() => {
	const data = JSON.parse(JSON.stringify(props.data))
	state.allData = state.sourceOptions = data.map(item => Object.assign(item, { checked: false }))
})

</script>
<style lang="scss" scoped>
.transfer-modal{
  width: 100%!important;
  padding: 5px 15px 10px 15px;
  // text-align: center;
  background: #fff;
  border-radius: 4px;
}
.transfer-title {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  >div{
    background: #fff;
    text-align: left;
    padding: 0 10px;
  }
}

.transfer-box {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  >div{
    width: 258px;
    height: 300px;
    padding: 5px 10px;
    box-sizing: border-box;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    .transfer-search {
      // padding: 0 10px
      height: 40px;
    }
    .transfer-content{
      height: 250px;
      background: #fff;
      text-align: left;
      white-space: nowrap;
      overflow: auto;
    }

  }
  >div:first-child {
      margin-right: 15px;
    }
}
</style>
