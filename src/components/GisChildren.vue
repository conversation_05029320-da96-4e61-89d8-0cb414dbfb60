<template>
    <div class="map-container">
        <div class="gis_kanban" v-if="state.selectType=='point'&&state.mode==0">
            <div class="connect_info">
                <p>{{ $t('关联节点') }}</p>
                <div v-if="state.isReady">
                    <a-checkbox-group v-model:value="state.activeStationList1" @change="changeConnect1">
                        <a-checkbox @change="changeConnects" :disabled="state.activeStationList1.includes(item.index)" :value="item.index" v-for="(item) in state.connectStationList1" :key="item.index">{{ item.name }}</a-checkbox>
                    </a-checkbox-group>
                </div>
				<div v-else>
					<a-spin size="large">
						<div class="loading"></div>
					</a-spin>
				</div>
            </div>
            <div class="connect_info">
                <p>{{ $t('同站节点') }}</p>
                <div v-if="state.isReady">
                    <a-checkbox-group v-model:value="state.activeStationList2" @change="changeConnect2">
                        <a-checkbox @change="changeConnects" :disabled="state.activeStationList2.includes(item.index)" :value="item.index" v-for="(item) in state.connectStationList2" :key="item.index">{{ item.name }}</a-checkbox>
                    </a-checkbox-group>
                </div>
				<div v-else>
					<a-spin size="large">
						<div class="loading"></div>
					</a-spin>
				</div>
            </div>
        </div>
        <div class="gis_kanban" v-if="state.mode==1">
			<div class="equipment_list">
				<p>{{ $t('网侧元件库') }}</p>
				<div>
					<div v-for="(item) in menuList.filter(items=>[0,7].includes(items.type))" :key="item.type">
						<svg @pointerdown="selectSvg(item,$event)">
							<path  :d="item.path" fill="#fff" stroke="#FF0000" stroke-width="1" :style="{transform:`matrix(0.58,0,0,0.58,0.5,0.5)`}"></path>
						</svg>
						<p>{{ $t(item.name) }}</p>
					</div>
					<div>
						<svg @pointerdown="addEquipment(99)" class="svg_sp">
							<path d="M 25,10
									L 25,40
									Z
									M 20,5
									A 5,5 0 1,1 30,5
									A 5,5 0 1,1 20,5
									M 20,45
									A 5,5 0 1,1 30,45
									A 5,5 0 1,1 20,45"
							fill="#fff"
							stroke="#FF0000"
							:style="{transform:`matrix(0.59,0,0,0.59,0.2,0.2)`}"
							stroke-width="1"></path>
						</svg>
						<p>{{ $t('通道') }}</p>
					</div>
				</div>
			</div>
			<div class="equipment_list">
				<p>{{ $t('源侧元件库') }}</p>
				<div>
					<div v-for="(item) in menuList.filter(items=>[1,2,3,4,5].includes(items.type))" :key="item.type">
						<svg @pointerdown="selectSvg(item,$event)">
							<path :d="item.path" fill="#fff" stroke="#FF0000" stroke-width="1" :style="{transform:`matrix(0.59,0,0,0.59,0.15,0.5)`}"></path>
						</svg>
						<p>{{ $t(item.name) }}</p>
					</div>
				</div>
			</div>
			<div class="equipment_list">
				<p>{{ $t('储能元件库') }}</p>
				<div>
					<div v-for="(item) in menuList.filter(items=>[6].includes(items.type))" :key="item.type">
						<svg @pointerdown="selectSvg(item,$event)">
							<path :d="item.path" fill="#fff" stroke="#FF0000" stroke-width="1" :style="{transform:`matrix(0.59,0,0,0.59,0.15,0.5)`}"></path>
						</svg>
						<p>{{ $t(item.name) }}</p>
					</div>
				</div>
			</div>
			<svg class="add_svg" :style="{top: state.svgTop, left: state.svgLeft}">
				<path :style="{transform:`matrix(0.59,0,0,0.59,0.2,0.2)`}" :d="state.path" fill="#fff" stroke="#FF0000" stroke-width="1"></path>
			</svg>
        </div>
		<gis-info
            v-if="state.gisInfoShow"
            ref="gisInfoRef"
            @saveEdit="saveEdit"
            @refreshGis="refreshGis"
            @cancelEdit="cancelEdit"
            :mode="state.mode"
			:zlevelId="state.zlevelId"
            :busData="state.busData"
            :selectedStationId="state.selectedStationId"
			:equipmentType="state.equipmentType"
            :type="state.selectType"
            :data="state.selectData"
            :isAdd="state.isAdd"
        ></gis-info>
        <div class="gis">
            <div ref="gis" @click="clickMap" @contextmenu="contextmenu" @wheel="wheel" @mousedown="mousedown" @mouseup="mouseup" @mouseleave="mouseleave" :style="{transform:`scale(${state.scales})`,zoom:`${state.zooms}`}">

            </div>
            <div class="gis_menu absolute" @mouseleave="state.menuShow1=false" v-if="state.menuShow1" :style="{'left':(state.menuLeft)+'px','top':(state.menuTop)+'px'}">
				<p @click.stop="addEquipment(0)">{{ $t('新建变电站') }}</p>
				<p @click.stop="addEquipment(1)">{{ $t('新建火电机组') }}</p>
				<p @click.stop="addEquipment(2)">{{ $t('新建水电厂') }}</p>
				<p @click.stop="addEquipment(4)">{{ $t('新建风电场') }}</p>
				<p @click.stop="addEquipment(5)">{{ $t('新建光伏电站') }}</p>
				<p @click.stop="addEquipment(6)">{{ $t('新建储能电站') }}</p>
				<p @click.stop="addEquipment(3)">{{ $t('新建核电机组') }}</p>
				<p @click.stop="addEquipment(7)">{{ $t('新建电力流') }}</p>
				<p @click.stop="addEquipment(99)">{{ $t('新建通道') }}</p>
			</div>
			<div class="gis_menu absolute" @mouseleave="state.menuShow2=false" v-if="state.menuShow2" :style="{'left':(state.menuLeft)+'px','top':(state.menuTop)+'px'}">
				<div v-if="state.selectType=='lines'">
                    <p v-if="state.selectType=='lines'" @click.stop="removeEquipment(0)">{{ $t('删除通道') }}</p>
                </div>
                <div v-else>
                    <p v-if="state.mode==0" @click.stop="hidePoint">{{ $t('删除') }}</p>
                    <p v-if="state.mode==0" @click.stop="changeStation">{{ $t('修改') }}</p>
                    <p v-if="state.mode==1" @click.stop="addChannel">{{ $t('新建通道') }}</p>
                    <p v-if="state.mode==1" @click.stop="removeEquipment(1)">{{ $t('删除场站') }}</p>
                </div>
			</div>
			<div class="gis_option absolute" v-if="state.ctrlKey" @pointerdown="selectMap">
				<div class="gis_option_item" :style="{'left':state.option_left+'px','top':state.option_top+'px','width':state.option_width+'px','height':state.option_height+'px'}">

				</div>
			</div>
		</div>
    </div>
    <add-station :targetKeys="[]" v-if="state.addShow" @close="state.addShow=false" @confirm="confirmAdd" :data="state.baseData"></add-station>
	<add-station :targetKeys="state.editStationList.map(item=>item.toString())" v-if="state.editShow" @close="state.editShow=fasle" @confirm="confirmEdit" :data="state.editData"></add-station>
	<a-modal v-model:open="state.saveAsShow" wrapClassName="gis_save_modal" :afterClose="()=>state.saveAsShow=false" :centered="true" :footer="null" :closable="false" :maskClosable="false">
        <div class="user-select" :style="{zoom: state.scales}">
			<div>
				<div class="modal_top">
					<p>{{ $t('导出tg') }}</p>
					<close-outlined class="pointer" @click="state.saveAsShow=false" />
				</div>
				<div class="gis_save_content">
					<a-form
						ref="formRef"
            :model="formState"
						:rules="rules"
					>
						<a-form-item
							:label="$t('电压上限')"
							name="max_vn_kv"
						>
							<a-input-number :controls="false" v-model:value="formState.max_vn_kv" :min="0"/>
						</a-form-item>
						<a-form-item
							:label="$t('电压下限')"
							name="min_vn_kv"
						>
							<a-input-number :controls="false" v-model:value="formState.min_vn_kv" :min="0"/>
						</a-form-item>
					</a-form>
					<a-button @click="saveAs" type="primary">{{ $t('导出') }}</a-button>
				</div>
			</div>
		</div>
    </a-modal>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import { markRaw, reactive, ref, onUnmounted, inject, onMounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { getMapServies, getTangentialPoint, echartsResize, getGisStyle, initLineData, handleChannel, menuList, openModal, debounce, getRotate, centerOnLine } from '@/utils/gis'
import { basicApi } from '@/api/exampleApi'
import { DownloadTgFile } from '@/api/gis'
import message from '@/utils/message'
import { loadingStore } from '@/store/loadingStore'
import { downloadApiFile, t } from '@/utils/common'
import Mitt from '@/utils/mitt.js'
import { routeStore } from '@/store/routeStore'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeRoute = routeStore()
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const checkVnkV1 = async(_rule, value) => {
	if (formState.min_vn_kv && formState.min_vn_kv > value) {
		return Promise.reject(t('电压等级上限限不能小于下限'))
	} else {
		return Promise.resolve()
	}
}
const checkVnkV2 = async(_rule, value) => {
	if (formState.max_vn_kv && value > formState.max_vn_kv) {
		return Promise.reject(t('电压等级下限不能高于上限'))
	} else {
		return Promise.resolve()
	}
}
const rules = {
	max_vn_kv: [
		{
			validator: checkVnkV1
		}
	],
	min_vn_kv: [
		{
			validator: checkVnkV2
		}
	]
}
const storeModal = loadingStore()
const emit = defineEmits(['changeScale', 'changeZlevel'])
const echarts = inject('ec')
const route = useRoute()
const gis = ref()
const props = defineProps({
	mode: {
		type: Number
	},
	gisData: {
		type: Object
	},
	busData: {
		type: Array,
		default: () => []
	},
	isBlank: {
		type: Boolean,
		default: false
	},
	showStationName: {
		type: Boolean,
		default: true
	},
	mapZoom: {
		type: Number,
		default: 5
	},
	scale: {
		type: Number,
		default: 100
	},
	iconZoom: {
		type: Number,
		default: 1
	},
	originCenter: {
		type: Array
	},
	geoScale: {
		type: Number
	},
	partition: {
		type: String
	}
})
const state = reactive({
	mode: props.mode,
	isFileChanged: false,
	gisInfoShow: false,
	saveAsShow: false,
	min_vn_kv: undefined,
	max_vn_kv: undefined,
	scales: 1,
	zooms: 1,
	option_left: undefined,
	option_top: undefined,
	option_width: undefined,
	option_height: undefined,
	menuShow1: false,
	menuShow2: false,
	menuLeft: 0,
	menuTop: 0,
	addShow: false,
	editShow: false,
	isAdd: false,
	isEdit: false,
	coords: [],

	position: {},
	svgTop: undefined,
	svgLeft: undefined,
	path: '',
	equipmentType: 0,
	busData: [],

	vn_kv: undefined,
	connectId: undefined,
	isReady: false,
	zlevelId: undefined,

	baseData: [],
	editData: [],
	showStationList: [],
	showStationData: [],
	activeStationList1: [],
	activeStationList2: [],
	connectStationList1: [],
	connectStationList2: [],
	editStationList: [],
	selectedStationId: undefined,
	selectType: undefined,
	selectData: {},
	moveStation: [],
	moveLine: [],
	activeLineList: [],
	waitMoveLine: []
})
const formState = reactive({
	min_vn_kv: undefined,
	max_vn_kv: undefined
})
const gisState = reactive({
	partition: props.partition,
	scale: props.scale,
	originCenter: props.originCenter,
	mapZoom: props.mapZoom,
	iconZoom: props.iconZoom,
	geoScale: props.geoScale,
	center: undefined,
	isBlank: props.isBlank,
	showStationName: props.showStationName,
	isDraging: false,
	isMoving: false,
	ctrlKey: false,
	isCtrlSelect: false,
	isEchartsClick: false
})
const mapData = ref({
	point_data: [],
	line_data: [],
	switch_data: []
})
const gisInfoRef = ref()
const mapChart = ref()
const mapOption = ref({})
const formRef = ref()
const cancelEdit = () => {
	state.selectedStationId = undefined
	state.gisInfoShow = false
	state.isAdd = false
	state.path = undefined
	state.svgTop = undefined
	state.svgLeft = undefined
	state.selectType = undefined
	state.selectData = undefined
}
const saveAs = () => {
	formRef.value.validate()
		.then(() => {
			DownloadTgFile({
				tc_filename: route.query.filePath,
				min_vn_kv: formState.min_vn_kv,
				max_vn_kv: formState.max_vn_kv
			}).then(res => {
				state.saveAsShow = false
				downloadApiFile(res)
			})
		})
		.catch(error => {
			console.log('error', error)
		})
}
const selectMap = (e) => {
	const move = (event) => {
		state.option_height = Math.abs((event.clientY - e.clientY)) / state.scales
		state.option_width = Math.abs((event.clientX - e.clientX)) / state.scales
		state.option_top = ((event.clientY - e.clientY) > 0 ? e.layerY / state.scales : e.layerY / state.scales - state.option_height)
		state.option_left = ((event.clientX - e.clientX) > 0 ? e.layerX / state.scales : e.layerX / state.scales - state.option_width)
	}
	const handleUp = (event) => {
		function isPointInRange(pointA, pointB, targetPoint) {
			const [lon1, lat1] = pointA
			const [lon2, lat2] = pointB
			const [targetLon, targetLat] = targetPoint
			const minLon = Math.min(lon1, lon2)
			const maxLon = Math.max(lon1, lon2)
			const minLat = Math.min(lat1, lat2)
			const maxLat = Math.max(lat1, lat2)
			const isLonInRange = targetLon >= minLon && targetLon <= maxLon
			const isLatInRange = targetLat >= minLat && targetLat <= maxLat
			return isLonInRange && isLatInRange
		}
		if (state.selectType) {
			state.selectType = undefined
		    state.selectData = undefined
			state.gisInfoShow = false
		}
		state.moveStation = mapOption.value.series[2].data.filter(item => {
			if (isPointInRange(mapChart.value.convertFromPixel('geo', [state.option_left, state.option_top]), mapChart.value.convertFromPixel('geo', [state.option_left + state.option_width, state.option_top + state.option_height]), item.value)) {
				item.itemStyle.borderColor = 'purple'
				return item
			} else {
				item.itemStyle.borderColor = state.mode == 0 ? item.color || getGisStyle(item).color : getGisStyle(item).color
			}
		}).map(items => items.station_id)
		state.moveLine = mapOption.value.series[1].data.filter(item => {
			if (state.moveStation.includes(item.from_station) && state.moveStation.includes(item.to_station)) {
				item.lineStyle.color = 'purple'
				return item
			} else {
				item.lineStyle.color = state.mode == 0 ? item.color || getGisStyle(item).color : getGisStyle(item).color
			}
		}).map(items => items.index)
		state.waitMoveLine = mapOption.value.series[1].data.filter(item => state.moveStation.includes(item.from_station) && !state.moveStation.includes(item.to_station) || !state.moveStation.includes(item.from_station) && state.moveStation.includes(item.to_station)).map(item => item.type + item.index)
		mapChart.value.setOption(mapOption.value)
		state.ctrlKey = false
		state.option_height = undefined
		state.option_width = undefined
		state.option_left = undefined
		state.option_top = undefined
		window.removeEventListener('pointermove', move) // 移除事件
		window.removeEventListener('pointerup', handleUp)
	}
	window.addEventListener('pointermove', move) // 全局添加移动事件
	window.addEventListener('pointerup', handleUp) // 全局添加结束事件
}
const selectSvg = (item, e) => {
	clearSelect()
	state.path = item.path
	state.svgTop = (e.layerY - 15) / state.scales
	state.svgLeft = (e.layerX - 15) / state.scales
	const moveSvg = (event) => {
		state.svgTop = (e.layerY - 15 + (event.clientY - e.clientY)) / state.scales
		state.svgLeft = (e.layerX - 15 + (event.clientX - e.clientX)) / state.scales
	}
	const handleMenuUp = (event) => {
		if (event.clientX > (310 - 15) * state.scales && (event.clientY > (77 + 120 + 33 + 34 - 15) * state.scales)) {
			state.isAdd = true
			if (item.type == 99) {
				state.selectType = 'lines'
			} else {
				state.coords = mapChart.value.convertFromPixel('geo', [state.svgLeft + 15, state.svgTop + 15])
				state.equipmentType = item.type
				state.selectData = {
					lon: state.coords[0],
					lat: state.coords[1]
				}
				state.selectType = 'point'
			}
			state.gisInfoShow = true
		} else {
			state.path = undefined
			state.svgTop = undefined
			state.svgLeft = undefined
		}
		window.removeEventListener('pointermove', moveSvg) // 移除事件
		window.removeEventListener('pointerup', handleMenuUp)
	}
	window.addEventListener('pointermove', moveSvg) // 全局添加移动事件
	window.addEventListener('pointerup', handleMenuUp) // 全局添加结束事件
}
const addEquipment = (val) => {
	state.menuShow1 = false
	if (state.selectType == 'point') {
		addChannel()
		return
	} else {
		clearSelect()
	}
	state.isAdd = true
	if (val == 99) {
		state.selectType = 'lines'
	} else {
		state.equipmentType = val
		state.selectData = {
			lon: state.coords[0],
			lat: state.coords[1]
		}
		state.selectType = 'point'
	}
	nextTick(() => {
		state.gisInfoShow = true
	})
}
const saveEdit = (edit_data) => {
	storeModal.hiddenModal()
	state.gisInfoShow = false
	if (state.selectType == 'point') {
		mapOption.value.series[2].data.forEach((item, index) => {
			if (item.station_id == state.selectData.station_id) {
				item.station_name = edit_data.name
				item.station_type = edit_data.type
				item.itemStyle.borderColor = getGisStyle(edit_data).color
			}
		})
		mapChart.value.setOption(mapOption.value)
		state.selectType = undefined
		state.selectData = undefined
	} else if (state.selectType == 'lines') {
		if (state.selectData.line_type == 'bolder') {
			const find = mapOption.value.series[1].data.find(item => item.from_station == state.selectData.from_station && item.to_station == state.selectData.to_station)
			find.color = edit_data.color
			find.lineStyle.color = edit_data.color
			find.line_types = edit_data.line_types
			find.lineStyle.type = edit_data.line_types
		} else {
			edit_data.line_data.forEach(item => {
				const find = mapOption.value.series[1].data.find(item1 => item1.index == item.index)
				find.color = item.color
				find.lineStyle.color = item.color
				find.line_types = item.line_types
				find.lineStyle.type = item.line_types
			})
		}
		mapChart.value.setOption(mapOption.value)
		state.selectType = undefined
		state.selectData = undefined
	}
	if (state.mode == 1) {
		gisChange()
	}
}
const hidePoint = () => {
	storeModal.showModal()
	state.gisInfoShow = false
	state.menuShow2 = false
	basicApi({
		'import_string_func': 'teapgis:delete_station_simple',
		'func_arg_dict': {
			'tg_file_path': route.query.filePath,
			'station_id': state.selectData.station_id
		}
	}).then(res => {
		storeModal.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message)
			mapOption.value.series[2].data = mapOption.value.series[2].data.filter(items => items.station_id != state.selectData.station_id)
			mapOption.value.series[1].data = mapOption.value.series[1].data.filter(items => state.selectData.station_id != items.to_station && state.selectData.station_id != items.from_station)
			state.showStationList = state.showStationList.filter(items => !state.selectData.bus_id_list.includes(items))
			state.showStationData = state.showStationData.filter(item => {
				return JSON.stringify(item) !== JSON.stringify(state.selectData.bus_id_list)
			})
			state.baseData = state.busData.filter(item => !state.showStationList.includes(item.index))
			state.selectData = undefined
			state.selectType = undefined
			mapChart.value.setOption(mapOption.value)
			initDrag()
		}
	}).catch(() => {
		storeModal.hiddenModal()
	})
}
const changeStation = () => {
	state.editStationList = state.selectData.bus_id_list
	state.editData = state.baseData.concat(state.busData.filter(item => state.editStationList.includes(item.index)))
	state.editShow = true
}
const handleNewPoint = async(val, isChangeConnect) => {
	const arr = state.busData.filter(item => val.includes(item.index)).sort((a, b) => b.vn_kv - a.vn_kv)
	state.vn_kv = arr[0].vn_kv
	const station = Object.assign({ ...state.selectData }, {
		bus_id_list: val,
		vn_kv: state.vn_kv,
		color: getGisStyle({ vn_kv: state.vn_kv }).color,
		itemStyle: {
			borderColor: isChangeConnect ? 'purple' : getGisStyle({ vn_kv: state.vn_kv }).color
		}
	})
	const res = await basicApi({
		'import_string_func': 'teapgis:update_station_simple',
		'func_arg_dict': {
			'tg_file_path': route.query.filePath,
			'station_id': state.selectData.station_id,
			'bus_id_list': val,
			'vn_kv': state.vn_kv,
			'color': station.color
		}
	})
	state.showStationList = state.showStationList.filter(item => !state.editStationList.includes(item))
	state.showStationData = state.showStationData.filter(item => {
		return JSON.stringify(item) !== JSON.stringify(state.selectData.bus_id_list)
	})
	mapOption.value.series[2].data = mapOption.value.series[2].data.filter(item => item.station_id != station.station_id)
	mapOption.value.series[2].data.push(station)
	let lineData = []
	if (state.showStationList.length > 0) {
		lineData = await reloadLine(station, val, true, true)
		if (lineData.length > 0) {
			const res = await basicApi({
				'import_string_func': 'teapgis:update_channel',
				'func_arg_dict': {
					'tg_file_path': route.query.filePath,
					'channel_data': handleChannel([].concat(...lineData))
				}
			})
			// mapOption.value.series[1].data = mapOption.value.series[1].data.concat(...lineData)
			mapOption.value.series[1].data = mapOption.value.series[1].data.filter(item => item.from_station !== station.station_id && item.to_station !== station.station_id).concat(...lineData)
			state.activeLineList = mapOption.value.series[1].data.filter(item => station.station_id == item.from_station || station.station_id == item.to_station).map(item => item.type + '_' + item.index)
		}
	}
	state.showStationList = state.showStationList.concat(val)
	state.showStationData.push(val)
	// mapOption.value.series[1].data = mapOption.value.series[1].data.filter(item => station.station_id != item.to_station && station.station_id != item.from_station).concat(...lineData)
	return station
}
const confirmEdit = async(val) => {
	if (state.editStationList.every(item => val.includes(item))) {
		if (state.editStationList.length == val.length) {
			state.editShow = false
			return
		} else {
			await handleNewPoint(val)
		}
	} else {
		await handleNewPoint(val)
	}
	state.editShow = false
	state.selectData = undefined
	state.selectType = undefined
	state.gisInfoShow = false
	mapChart.value.setOption(mapOption.value)
	initDrag()
	state.baseData = state.busData.filter(item => !state.showStationList.includes(item.index))
}
const confirmAdd = async(val) => {
	const arr = state.baseData.filter(item => val.includes(item.index)).sort((a, b) => b.vn_kv - a.vn_kv)
	state.vn_kv = arr[0].vn_kv
	const station = {
		station_name: arr[0].name + t('场站'),
		value: state.coords,
		lon: state.coords[0],
		lat: state.coords[1],
		bus_id_list: arr.length > 1 ? arr.map(item => item.index) : [arr[0].index],
		station_type: 'station',
		vn_kv: state.vn_kv,
		color: getGisStyle({ vn_kv: state.vn_kv }).color,
		itemStyle: {
			borderColor: getGisStyle({ vn_kv: state.vn_kv }).color
		}
	}
	basicApi({
		'import_string_func': 'teapgis:add_station_simple',
		'func_arg_dict': {
			'tg_file_path': route.query.filePath,
			'station_name': station.station_name,
			'station_type': station.station_type,
			'bus_id_list': station.bus_id_list,
			'vn_kv': state.vn_kv,
			'lon': state.coords[0],
			'lat': state.coords[1],
			'color': getGisStyle({ vn_kv: state.vn_kv }).color
		}
	}).then(async res => {
		if (state.selectType == 'point') {
			state.activeStationList1 = state.activeStationList1.concat(state.connectStationList1.map(item => item.index).filter(item => station.bus_id_list.includes(item)))
			state.activeStationList2 = state.activeStationList2.concat(state.connectStationList2.map(item => item.index).filter(item => station.bus_id_list.includes(item)))
		}
		station.station_id = res.func_result.station_id
		if (state.showStationList.length > 0) {
			const lineData = await reloadLine(station, val, true, false)
			mapOption.value.series[1].data = mapOption.value.series[1].data.concat(...lineData)
			basicApi({
				'import_string_func': 'teapgis:update_channel',
				'func_arg_dict': {
					'tg_file_path': route.query.filePath,
					'channel_data': handleChannel([].concat(...lineData))
				}
			})
		}
		state.showStationList = state.showStationList.concat(station.bus_id_list)
		state.showStationData.push(station.bus_id_list)
		mapOption.value.series[2].data.push(station)
		state.addShow = false
		mapChart.value.setOption(mapOption.value)
		initDrag()
		state.baseData = state.busData.filter(item => !state.showStationList.includes(item.index))
	}).catch(() => {

	})
}
const clearSelect = () => {
	state.gisInfoShow = false
	if (state.selectType) {
		if (state.selectType == 'point') {
			mapOption.value.series[2].data.forEach(items => {
				if (items.station_id == state.selectData.station_id) {
					items.itemStyle.borderColor = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
				}
			})
		} else if (state.selectType == 'lines') {
			mapOption.value.series[1].data.forEach(items => {
				if (items.from_station == state.selectData.from_station && items.to_station == state.selectData.to_station) {
					items.lineStyle.color = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
				}
			})
		}
		state.selectType = undefined
		state.selectData = undefined
		mapChart.value.setOption(mapOption.value)
	}
	if (state.moveStation.length > 0) {
		mapOption.value.series[1].data.forEach(items => {
			if (state.moveLine.includes(items.index)) {
				items.lineStyle.color = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
			}
		})
		mapOption.value.series[2].data.forEach(items => {
			if (state.moveStation.includes(items.station_id)) {
				items.itemStyle.borderColor = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
			}
		})
		state.waitMoveLine = []
		state.moveStation = []
		state.moveLine = []
		mapChart.value.setOption(mapOption.value)
	}
}
const removeEquipment = (val) => {
	openModal(val == 0 ? t('确定删除该通道吗？') : t('确定删除该场站吗？'), val == 0 ? t('注：此操作将删除算例文件中的对应通道和其包含线路') : t('注：此操作将删除算例文件中的对应场站和其包含节点及设备')).then(res => {
		storeModal.showModal()
		state.gisInfoShow = false
		if (val == 0) {
			basicApi({
				'import_string_func': 'teapgis:delete_channel',
				'func_arg_dict': {
					'tc_file_path': route.query.filePath,
					'from_station': state.selectData.from_station,
					'to_station': state.selectData.to_station
				}
			}).then(res => {
				storeModal.hiddenModal()
				if (res.code == 1 && res.func_result.code == 1) {
					message.success(res.func_result.message)
					mapOption.value.series[1].data = mapOption.value.series[1].data.filter(items => !(state.selectData.from_station == items.from_station && state.selectData.to_station == items.to_station))
					state.selectType = undefined
					state.selectData = undefined
					mapChart.value.setOption(mapOption.value)
				}
			}).catch(() => {
				storeModal.hiddenModal()
			})
		} else {
			basicApi({
				'import_string_func': 'teapgis:delete_station',
				'func_arg_dict': {
					'tc_file_path': route.query.filePath,
					'station_id': state.selectData.station_id
				}
			}).then(res => {
				storeModal.hiddenModal()
				if (res.code == 1 && res.func_result.code == 1) {
					message.success(res.func_result.message)
					mapOption.value.series[2].data = mapOption.value.series[2].data.filter(items => items.station_id != state.selectData.station_id)
					mapOption.value.series[1].data = mapOption.value.series[1].data.filter(items => state.selectData.station_id != items.to_station && state.selectData.station_id != items.from_station)
					state.selectType = undefined
					state.selectData = undefined
					mapChart.value.setOption(mapOption.value)
					initDrag()
				}
			}).catch(() => {
				storeModal.hiddenModal()
			})
		}
	}).catch(() => {

	})
}
const addChannel = () => {
	state.menuShow2 = false
	state.selectedStationId = state.selectData.station_id
	clearSelect()
	state.isAdd = true
	state.selectType = 'lines'
	nextTick(() => {
		state.gisInfoShow = true
	})
}
const changeConnect1 = async() => {
	storeModal.showModal()
	const res = (await basicApi({
		'import_string_func': 'teapgis:add_relation_station',
		'func_arg_dict': {
			'tg_file_path': route.query.filePath,
			'bus_id_list': state.selectData.bus_id_list,
			'bus_id': state.connectId,
			'lat': state.selectData.lat,
			'lon': state.selectData.lon,
			'station_type': 'station',
			'pos_offset': gisState.isBlank ? 5 : undefined,
			'color': getGisStyle({ vn_kv: state.busData.find(item => item.index == state.connectId).vn_kv }).color,
			'station_name': state.busData.find(item => item.index == state.connectId).name + t('场站')
		}
	})).func_result
	const station = res.station
	const lineData = res.line_data
	mapOption.value.series[2].data.push(Object.assign({ ...station }, { itemStyle: { borderColor: station.color }, value: [station.lon, station.lat, 0] }))
	state.showStationList.push(state.connectId)
	state.showStationData.push([state.connectId])
	state.baseData = state.busData.filter(item => !state.showStationList.includes(item.index))
	let lineData_base = []
	if (state.showStationList.length > 0) {
		lineData_base = await reloadLine(station, [state.connectId], false, false)
	}
	const lineData_connect = handleLine(station, lineData, Math.min(state.selectData.vn_kv, station.vn_kv))
	basicApi({
		'import_string_func': 'teapgis:update_channel',
		'func_arg_dict': {
			'tg_file_path': route.query.filePath,
			'channel_data': handleChannel(lineData_connect.concat(...lineData_base))
		}
	})
	mapOption.value.series[1].data = mapOption.value.series[1].data.concat(lineData_connect, ...lineData_base)
	state.activeLineList = state.activeLineList.concat(lineData_connect.map(item => item.type + '_' + item.index))
	mapChart.value.setOption(mapOption.value)
	initDrag()
	storeModal.hiddenModal()
}
const changeConnect2 = async() => {
	const res = await basicApi({
		'import_string_func': 'teapgis:get_same_station_bus_by_bus',
		'func_arg_dict': {
			'file_path': route.query.filePath,
			'bus_id_list': state.selectData.bus_id_list.concat(state.connectId),
			'filter_row_id_list': state.activeStationList2
		}
	})
	state.editStationList = state.selectData.bus_id_list
	state.selectData = await handleNewPoint(state.selectData.bus_id_list.concat(state.connectId), true)
	const res1 = await basicApi({
		'import_string_func': 'teapgis:get_line_and_side_bus_by_bus',
		'func_arg_dict': {
			'file_path': route.query.filePath,
			'bus_id_list': state.selectData.bus_id_list,
			'parent_station_id': state.zlevelId
		}
	})
	gisInfoRef.value.refreshStation()
	state.activeStationList1 = res1.func_result.point_data.filter(item => state.showStationList.includes(item.index)).map(item => item.index)
	state.connectStationList1 = res1.func_result.point_data
	state.connectStationList2 = state.connectStationList2.concat(res.func_result.point_data.filter(item => !state.connectStationList2.find(items => items.index == item.index)))
	mapChart.value.setOption(mapOption.value)
	initDrag()
	state.baseData = state.busData.filter(item => !state.showStationList.includes(item.index))
}
const changeConnects = (val) => {
	state.connectId = val.target.value
}
const handleLine = (station, line_data, min_vn_kv) => {
	const channel = Object.assign({ }, {
		coords: [state.selectData.value, [station.lon, station.lat]],
		min_vn_kv,
		line_data,
		// line_data: line_data.map(item => {
		// 	return Object.assign({ ...item }, {
		// 		isSequence: station.bus_id_list.includes(item.to_bus)
		// 	})
		// }),
		from_station: state.selectData.station_id,
		to_station: station.station_id,
		from_station_name: state.selectData.station_name,
		to_station_name: station.station_name
	})
	return initLineData(
		{ 'type': 'create', 'channel': channel, 'apiZoom': gisState.iconZoom, 'geoScale': gisState.geoScale }
	)
}
const reloadLine = async(station, val, isContextmenu, isEdit) => {
	const { func_result } = await basicApi({
		'import_string_func': 'teapgis:get_same_station_and_line_bus_by_bus',
		'func_arg_dict': {
			'file_path': route.query.filePath,
			'bus_id_list': val
		}
	})
	if (func_result.point_data.find(item => isContextmenu ? state.showStationList.includes(item.index) : state.showStationList.filter(item1 => !state.selectData.bus_id_list.includes(item1)).includes(item.index))) {
		// eslint-disable-next-line prefer-const
		let arr = [];
		(isContextmenu ? state.showStationData : state.showStationData.filter(item => JSON.stringify(item) !== JSON.stringify(state.selectData.bus_id_list))).forEach(item => {
			const find = func_result.point_data.filter(items => item.includes(items.index))
			if (find.length > 0) {
				const connectPoint = mapOption.value.series[2].data.find(items => JSON.stringify(item) === JSON.stringify(items.bus_id_list))
				const obj = {
					line_data: [].concat(...find.map(item => item.line_data)),
					coords: isContextmenu ? [!isEdit ? state.coords : state.selectData.value, connectPoint.value] : [[station.lon, station.lat], connectPoint.value],
					from_station_name: station.station_name,
					to_station_name: connectPoint.station_name,
					to_station: connectPoint.station_id,
					from_station: station.station_id,
					min_vn_kv: isContextmenu ? Math.min(state.vn_kv, connectPoint.vn_kv) : Math.min(station.vn_kv, connectPoint.vn_kv)
				}
				arr.push(obj)
			}
		})
		// if (isEdit) {
		// 	// 待测试
		// 	arr = arr.filter(item => {
		// 		const filter_arr = mapOption.value.series[1].data.filter(items => (items.from_station == item.from_station && items.to_station == item.to_station) || items.from_station == item.to_station && items.to_station == item.from_station)
		// 		if (filter_arr.length != 0) {
		// 			if (item.line_data.length != filter_arr.length) {
		// 				mapOption.value.series[1].data = mapOption.value.series[1].data.filter(item1 => !(item1.from_station == item.from_station && item1.to_station == item.to_station) || !(item1.from_station == item.to_station && item1.to_station == item.from_station))
		// 				return item
		// 			}
		// 		} else {
		// 			return item
		// 		}
		// 	})
		// }
		return arr.map(item => initLineData(
			{ 'type': 'create', 'channel': item, 'apiZoom': gisState.iconZoom, 'geoScale': gisState.geoScale }
		))
	} else {
		return []
	}
}
const activePoint = async(activeData) => {
	if (state.mode == 0) state.isReady = false
	if (state.selectType == 'lines') {
		mapOption.value.series[1].data.forEach(items => {
			if (items.from_station == state.selectData.from_station && items.to_station == state.selectData.to_station) {
				items.lineStyle.color = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
			}
		})
	}
	if (state.moveStation.length > 0) {
		mapOption.value.series[1].data.forEach(items => {
			if (state.moveLine.includes(items.index)) {
				items.lineStyle.color = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
			}
		})
		state.waitMoveLine = []
		state.moveStation = []
		state.moveLine = []
	}
	state.selectType = 'point'
	state.selectData = activeData
	mapOption.value.series[2].data.forEach(items => {
		if (items.station_id == activeData.station_id) {
			items.itemStyle.borderColor = 'purple'
		} else {
			items.itemStyle.borderColor = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
		}
	})
	mapChart.value.setOption(mapOption.value)
	nextTick(() => {
		state.gisInfoShow = true
	})
	if (state.mode == 0) {
		const res1 = await basicApi({
			'import_string_func': 'teapgis:get_line_and_side_bus_by_bus',
			'func_arg_dict': {
				'file_path': route.query.filePath,
				'bus_id_list': activeData.bus_id_list,
				'parent_station_id': state.zlevelId
			}
		})
		const res2 = await basicApi({
			'import_string_func': 'teapgis:get_same_station_bus_by_bus',
			'func_arg_dict': {
				'file_path': route.query.filePath,
				'bus_id_list': activeData.bus_id_list,
				'filter_row_id_list': []
			// 'filter_row_id_list': state.activeStationList2
			}
		})
		state.activeStationList1 = res1.func_result.point_data.filter(item => state.showStationList.includes(item.index)).map(item => item.index)
		state.activeStationList2 = res2.func_result.point_data.filter(item => state.showStationList.includes(item.index)).map(item => item.index)
		state.connectStationList1 = res1.func_result.point_data
		state.connectStationList2 = res2.func_result.point_data
		state.isReady = true
	}
	state.activeLineList = mapOption.value.series[1].data.filter(item => activeData.station_id == item.from_station || activeData.station_id == item.to_station).map(item => item.type + '_' + item.index)
}
const refreshGis = (editData, type) => {
	storeModal.hiddenModal()
	state.gisInfoShow = false
	state.isAdd = false
	if (state.selectType == 'lines') {
		const findFrom = mapOption.value.series[2].data.find(item => item.station_id == editData.from_station)
		const findTo = mapOption.value.series[2].data.find(item => item.station_id == editData.to_station)
		editData.coords = [[findFrom.lon, findFrom.lat], [findTo.lon, findTo.lat]]
		if (type == 'edit') {
			mapOption.value.series[1].data = mapOption.value.series[1].data.filter(item => item.from_station != state.selectData.from_station || item.to_station != state.selectData.to_station).concat(...initLineData({ 'type': 'edit', 'channel': editData, 'apiZoom': gisState.iconZoom, 'geoScale': gisState.geoScale }))
		} else {
			state.selectedStationId = undefined
			mapOption.value.series[1].data = mapOption.value.series[1].data.concat(...initLineData(
				{ 'type': 'edit', 'channel': editData, 'apiZoom': gisState.iconZoom, 'geoScale': gisState.geoScale }
			))
		}
	} else {
		if (type == 'edit') {
			mapOption.value.series[2].data.forEach((item, index) => {
				if (item.station_id == state.selectData.station_id) {
					mapOption.value.series[2].data[index] = Object.assign({ ...editData }, {
						value: [editData.lon, editData.lat],
						itemStyle: {
							borderColor: getGisStyle({ vn_kv: editData.vn_kv }).color // editData.color || getGisStyle(editData).color
						}
					})
				}
			})
		} else {
			if (state.path) {
				state.path = undefined
				state.svgTop = undefined
				state.svgLeft = undefined
			}
			mapOption.value.series[2].data = mapOption.value.series[2].data.concat(Object.assign({ ...editData }, {
				value: [editData.lon, editData.lat],
				itemStyle: {
					borderColor: getGisStyle({ vn_kv: editData.vn_kv }).color // editData.color || getGisStyle(editData).color
				}
			}))
		}
	}
	state.selectType = undefined
	state.selectData = undefined
	mapChart.value.setOption(mapOption.value, true)
	if (state.mode == 1) {
		gisChange()
	}
	initDrag()
}
const initNextMap = (data) => {
	if (state.zlevelId == data.station_id) {
		state.zlevelId = undefined
	} else {
		state.zlevelId = data.station_id
	}
	emit('changeZlevel', state.zlevelId)
}
const initMap = (val) => {
	mapOption.value = getMapServies({
		gisState,
		mapData: mapData.value,
		resultType: false
	})
	if (gisState.center) {
		mapOption.value.geo.center = gisState.center
		mapOption.value.series[0].center = gisState.center
	}
	if (gisState.scale != 100) {
		mapOption.value.geo.zoom = gisState.mapZoom * gisState.scale / 100
		mapOption.value.series[0].zoom = gisState.mapZoom * gisState.scale / 100
		mapOption.value.series[1].data.forEach(item => {
			item.lineStyle.width = item.line_data ? (getGisStyle({ vn_kv: item.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)) * gisState.scale / 100 : getGisStyle(item, gisState.iconZoom).width * gisState.scale / 100
		})
	}
	if (val) {
		mapOption.value.series[1].data.forEach(item1 => {
			item1.lineStyle.width = item1.line_data ? (getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)) * gisState.scale / 100 : getGisStyle(item1, gisState.iconZoom).width * gisState.scale / 100
			const symbolR = getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)
			if (['boundary1', 'boundary2'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], (symbolR - getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width / 2) * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], (symbolR - getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width / 2) * gisState.geoScale)
				item1.coords = item1.line_type == 'boundary1' ? coords[0] : coords[1]
			} else if (['middle', 'bolder'].includes(item1.line_type)) {
				item1.coords = item1.coord
			} else if (['four1', 'four2'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 3 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 3 * gisState.geoScale)
				item1.coords = item1.line_type == 'four1' ? coords[0] : coords[1]
			} else if (['five1', 'five2'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 2 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 2 * gisState.geoScale)
				item1.coords = item1.line_type == 'five1' ? coords[0] : coords[1]
			} else if (['six1', 'six4'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 5 * 3 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 5 * 3 * gisState.geoScale)
				item1.coords = item1.line_type == 'six1' ? coords[0] : coords[1]
			} else if (['six2', 'six3'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 5 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 5 * gisState.geoScale)
				item1.coords = item1.line_type == 'six2' ? coords[0] : coords[1]
			}
		})
	}
	mapChart.value.setOption(mapOption.value)
	initDrag()
	mapChart.value.on('mousedown', function(params) {
		state.isDraging = false
		state.isMoving = false
	})
	mapChart.value.off('click')
	mapChart.value.on('click', async(params) => { // 点击地图
		params.event.stop()
		state.isMoving = true
		if (params.componentSubType == 'lines' && params.data.type == 'switch') {
			return
		} if (params.componentSubType == 'scatter' && params.componentIndex == 6) {
			return
		} else if (state.selectType == 'lines' && params.componentSubType == 'lines' && state.selectData.from_station == params.data.from_station && state.selectData.to_station == params.data.to_station) {
			return
		}
		if (params.componentSubType == 'lines' && state.moveLine.includes(params.data.index)) {
			return
		}
		if (params.componentSubType == 'scatter' && (state.moveStation.includes(params.data.station_id) || state.selectType && state.selectData.station_id == params.data.station_id)) {
			initDrag()
			return
		}
		if (params.componentType == 'graphic' && (state.moveStation.includes(params.info.station_id) || state.selectType && state.selectData.station_id == params.info.station_id)) {
			initDrag()
			return
		}
		state.gisInfoShow = false
		if (params.componentSubType == 'scatter') {
			await activePoint(params.data)
		} else if (params.componentType == 'graphic') {
			await activePoint(params.info)
		} else if (params.componentSubType == 'lines') {
			if (state.moveStation.length > 0) {
				mapOption.value.series[2].data.forEach(items => {
					if (state.moveStation.includes(items.station_id)) {
						items.itemStyle.borderColor = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
					}
				})
				state.waitMoveLine = []
				state.moveStation = []
				state.moveLine = []
			}
			if (state.selectType == 'point') {
				mapOption.value.series[2].data.forEach(items => {
					if (items.station_id == state.selectData.station_id) {
						items.itemStyle.borderColor = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
					}
				})
			}
			state.selectType = 'lines'
			state.selectData = params.data
			mapOption.value.series[1].data.forEach(items => {
				if (items.from_station == params.data.from_station && items.to_station == params.data.to_station) {
					items.lineStyle.color = 'purple'
				} else {
					items.lineStyle.color = state.mode == 0 ? items.color || getGisStyle(items).color : getGisStyle(items).color
				}
			})
			mapChart.value.setOption(mapOption.value)
			nextTick(() => {
				state.gisInfoShow = true
			})
		} else {
			clearSelect()
			// return
		}
		if (params.componentSubType == 'scatter') {
			initDrag()
		}
	})
	mapChart.value.off('georoam')
	mapChart.value.on('georoam', function(params) {
		state.isDraging = true
		state.isMoving = true
		const _option = mapChart.value.getOption()
		gisState.center = _option.geo[0].center
		mapOption.value.geo.center = _option.series[0].center
		mapOption.value.series[0].center = _option.series[0].center
		gisState.originCenter = _option.series[0].center
		mapChart.value.setOption(mapOption.value)
		initDrag()
	})
	mapChart.value.off('contextmenu')
	mapChart.value.on('contextmenu', (params) => {
		params.event.stop()
		if (params.componentSubType == 'map') {
			if (state.selectType) return
			if (state.mode == 0) {
				state.coords = mapChart.value.convertFromPixel('geo', [params.event.offsetX, params.event.offsetY]).concat(0)
				state.addShow = true
			} else {
				state.coords = mapChart.value.convertFromPixel('geo', [params.event.offsetX, params.event.offsetY]).concat(0)
				state.menuLeft = params.event.offsetX - 5
				state.menuTop = params.event.offsetY - 5
				state.menuShow1 = true
			}
		} else if (params.componentType == 'graphic') {
			if (state.selectData && state.selectData.station_id == params.info.station_id) {
				state.menuLeft = params.event.offsetX - 5
				state.menuTop = params.event.offsetY - 5
				state.menuShow2 = true
			} else if (params.info.expandable) {
				initNextMap(params.info)
			}
		} else if (params.componentSubType == 'lines' && state.mode == 1) {
			if (state.selectData && state.selectData.from_station == params.data.from_station && state.selectData.to_station == params.data.to_station) {
				state.menuLeft = params.event.offsetX - 5
				state.menuTop = params.event.offsetY - 5
				state.menuShow2 = true
			}
		}
	})
}
const clickMap = (event) => {
	if (state.isDraging) {
		state.isDraging = false
		return
	}
	clearSelect()
}
const wheel = (event) => {
	event.preventDefault()
	let scale
	if (event.deltaY < 0) {
		scale = gisState.scale + 10
		if (scale >= 200) {
			scale = 200
		}
	} else if (event.deltaY > 0) {
		scale = gisState.scale - 10
		if (scale <= 20) {
			scale = 20
		}
	}
	emit('changeScale', scale)
	changeScale(scale)
}
const contextmenu = (event) => {
	if (state.selectType) return
	event.preventDefault()
	state.coords = mapChart.value.convertFromPixel('geo', [event.offsetX, event.offsetY]).concat(0)
	if (state.mode == 0) {
		state.addShow = true
	} else {
		state.menuLeft = event.offsetX - 5
		state.menuTop = event.offsetY - 5
		state.menuShow1 = true
	}
}
const mousemove = (e) => {
	if (!state.isMoving) return
	state.isDraging = true
	const { clientX: x, clientY: y } = e
	const center = [
		gisState.originCenter[0] + (state.position.x - x) * gisState.geoScale / (gisState.scale / 100),
		gisState.originCenter[1] - (state.position.y - y) * gisState.geoScale / (gisState.scale / 100)
	]
	mapOption.value.series[0].center = center
	mapOption.value.geo.center = center
	mapChart.value.setOption(mapOption.value)
}
const mouseup = () => {
	if (!state.isMoving) {
		return state.isMoving = true
	}
	gisState.originCenter = mapOption.value.series[0].center
	mapChart.value.getDom().removeEventListener('mousemove', mousemove)
	initDrag()
}
const mouseleave = () => {
	if (!state.isMoving) return
	gisState.originCenter = mapOption.value.series[0].center
	mapChart.value.getDom().removeEventListener('mousemove', mousemove)
}
const mousedown = (event) => {
	if (!state.isMoving) return
	event.preventDefault()
	const { clientX: x, clientY: y } = event
	state.position = {
		x,
		y
	}
	mapChart.value.getDom().addEventListener('mousemove', mousemove)
}
const onPointDragging = async(item, pos) => {
	state.isDraging = true
	if (state.moveStation.length > 0 && state.moveStation.includes(item.station_id)) {
		const offsetLon = mapChart.value.convertFromPixel('geo', pos)[1] - item.value[1]
		const offsetLat = mapChart.value.convertFromPixel('geo', pos)[0] - item.value[0]
		mapOption.value.series[2].data.forEach(items => {
			if (state.moveStation.includes(items.station_id)) {
				items.value = [items.value[0] + offsetLat, items.value[1] + offsetLon]
				items.lon = items.value[0]
				items.lat = items.value[1]
			}
		})
		mapOption.value.series[1].data.forEach(items => {
			if (state.moveStation.includes(items.from_station) && state.moveStation.includes(items.to_station)) {
				items.coords = [[items.coords[0][0] + offsetLat, items.coords[0][1] + offsetLon], [items.coords[1][0] + offsetLat, items.coords[1][1] + offsetLon]]
				items.coord = [[items.coord[0][0] + offsetLat, items.coord[0][1] + offsetLon], [items.coord[1][0] + offsetLat, items.coord[1][1] + offsetLon]]
				if (items.type == 'switch') {
					const find = mapOption.value.series[6].data.find(item => item.from_station === items.from_station && item.to_station === items.to_station)
					if (find) {
						find.coords = items.coord
						find.symbolRotate = getRotate(items.coord[0], items.coord[1])
						find.value = centerOnLine(items.coord[0], items.coord[1])
					}
				}
			}
		})
	} else {
		const index = item.station_id
		if (state.selectType && state.selectType == 'point') {
			state.selectData.value = mapChart.value.convertFromPixel('geo', pos)
			state.selectData.lon = mapChart.value.convertFromPixel('geo', pos)[0]
			state.selectData.lat = mapChart.value.convertFromPixel('geo', pos)[1]
		}
		mapOption.value.series[2].data.forEach(items => {
			if (items.station_id == index) {
				items.value = mapChart.value.convertFromPixel('geo', pos)
				item.lon = mapChart.value.convertFromPixel('geo', pos)[0]
				item.lat = mapChart.value.convertFromPixel('geo', pos)[1]
			}
		})
	}
	mapChart.value.setOption(mapOption.value)
}
const initLine = (item1) => {
	if (item1.type == 'switch') {
		mapOption.value.series[6].data.forEach(item2 => {
			if (item2.to_station == item1.to_station && item2.from_station == item1.from_station) {
				item2.coords = item1.coord
				item2.symbolRotate = getRotate(item1.coord[0], item1.coord[1])
				item2.value = centerOnLine(item1.coord[0], item1.coord[1])
			}
		})
	}
	const symbolR = getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)
	if (['boundary1', 'boundary2'].includes(item1.line_type)) {
		const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], (symbolR - getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width / 2) * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], (symbolR - getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width / 2) * gisState.geoScale)
		item1.coords = item1.line_type == 'boundary1' ? coords[0] : coords[1]
	} else if (['middle', 'bolder'].includes(item1.line_type)) {
		item1.coords = item1.coord
	} else if (['four1', 'four2'].includes(item1.line_type)) {
		const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 3 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 3 * gisState.geoScale)
		item1.coords = item1.line_type == 'four1' ? coords[0] : coords[1]
	} else if (['five1', 'five2'].includes(item1.line_type)) {
		const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 2 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 2 * gisState.geoScale)
		item1.coords = item1.line_type == 'five1' ? coords[0] : coords[1]
	} else if (['six1', 'six4'].includes(item1.line_type)) {
		const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 5 * 3 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 5 * 3 * gisState.geoScale)
		item1.coords = item1.line_type == 'six1' ? coords[0] : coords[1]
	} else if (['six2', 'six3'].includes(item1.line_type)) {
		const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 5 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 5 * gisState.geoScale)
		item1.coords = item1.line_type == 'six2' ? coords[0] : coords[1]
	}
}
const onPointDragEnd = (item, pos) => {
	if (state.moveStation.length > 0 && state.waitMoveLine.length > 0 && state.moveStation.includes(item.station_id)) {
		mapOption.value.series[1].data.forEach(item1 => {
			if (state.waitMoveLine.includes(item1.type + item1.index)) {
				if (state.moveStation.includes(item1.from_station)) {
					item1.coord[0] = mapOption.value.series[2].data.find(item2 => item2.station_id == item1.from_station).value
				} else if (state.moveStation.includes(item1.to_station)) {
					item1.coord[1] = mapOption.value.series[2].data.find(item2 => item2.station_id == item1.to_station).value
				}
			}
			initLine(item1)
		})
		mapChart.value.setOption(mapOption.value)
		initDrag()
	} else {
		mapOption.value.series[1].data.forEach(item1 => {
			if (item1.from_station == item.station_id || item1.to_station == item.station_id) {
				if (item1.to_station == item.station_id) {
					item1.coord[1] = mapChart.value.convertFromPixel('geo', pos)
				} else {
					item1.coord[0] = mapChart.value.convertFromPixel('geo', pos)
				}
				initLine(item1)
			}
		})
		mapChart.value.setOption(mapOption.value)
	}
}
const initDrag = () => {
	const graphicData = mapOption.value.series[2].data.map(function(item) {
		return Object.assign({
			type: ['switching_station', 'station', 'converter_station'].includes(item.station_type) ? 'circle' : 'rect',
			position: mapChart.value.convertToPixel('geo', item.value),
			info: item,
			invisible: true,
			draggable: true,
			ondrag: function(e, dx, dy) {
				if (e.which == 1) {
					onPointDragging(item, [this.x, this.y])
				}
			},
			ondragend: function(e, dx, dy) {
				if (e.which == 1) {
					onPointDragEnd(item, [this.x, this.y])
				}
			},
			onclick: function() {

			},
			onmousemove: function() {
			},
			onmouseout: function() {

			},
			zlevel: 99
		},
				 ['switching_station', 'station', 'converter_station'].includes(item.station_type) ? {
			shape: {
				cx: 0,
				cy: 0,
				r: (getGisStyle(item, gisState.iconZoom).width) * mapOption.value.geo.zoom
			}
		} : {
			shape: {
				x: -(getGisStyle(item, gisState.iconZoom).width * 2) * mapOption.value.geo.zoom / 2,
				y: -(getGisStyle(item, gisState.iconZoom).width * 2) * mapOption.value.geo.zoom / 2,
				width: (getGisStyle(item, gisState.iconZoom).width * 2) * mapOption.value.geo.zoom,
				height: (getGisStyle(item, gisState.iconZoom).width * 2) * mapOption.value.geo.zoom
			}
		}
		)
	// eslint-disable-next-line no-undef
	}).concat([])
	mapChart.value.setOption({
		graphic: graphicData
	}, {
		replaceMerge: ['graphic']
	})
}
const save = (type) => {
	if (type != 'temp') storeModal.showModal()
	basicApi({
		'import_string_func': 'teapgis:update_station_pos',
		'func_arg_dict': {
			'file_path': route.query.filePath,
			'station_data': mapOption.value.series[2].data.map(item => {
				return {
					'station_id': item.station_id,
					'lat': item.lat,
					'lon': item.lon
				}
			})
			// 'center_pos': gisState.center
			// 'zoom_level': gisState.iconZoom
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			if (type == 'temp') {
				Mitt.emit('closeGisShow', state.isFileChanged)
			} else if (type == 'saveAsTg') {
				DownloadTgFile({
					tg_filename: route.query.filePath
				}).then(res => {
					storeModal.hiddenModal()
					downloadApiFile(res)
				}).catch(() => {
					storeModal.hiddenModal()
				})
				return
			} else if (type == 'saveAsTc') {
				state.saveAsShow = true
				storeModal.hiddenModal()
			} else if (type == 'saveTg') {
				message.success(res.func_result.message)
				storeModal.hiddenModal()
			} else if (type == 'saveTc') {
				basicApi({
					'import_string_func': 'teapcase:save_tc_file',
					'func_arg_dict': {
						file_name: route.query.filePath
					}
				}).then(res => {
					if (res.code == 1 && res.func_result.code == 1) {
						message.success(res.func_result.message)
						Mitt.emit('saveGisFinished')
						storeModal.hiddenModal()
					}
				}).catch(() => {
					storeModal.hiddenModal()
				})
			}
		}
	}).catch(() => {
		if (type != 'temp') storeModal.hiddenModal()
	})
}
const changeScale = (val) => {
	gisState.scale = val
	mapOption.value.geo.zoom = gisState.mapZoom * gisState.scale / 100
	mapOption.value.series[0].zoom = gisState.mapZoom * gisState.scale / 100
	mapOption.value.series[1].data.forEach(item => {
		item.lineStyle.width = item.line_data ? (getGisStyle({ vn_kv: item.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)) * gisState.scale / 100 : getGisStyle(item, gisState.iconZoom).width * gisState.scale / 100
	})
	mapChart.value.setOption(mapOption.value)
	initDrag()
}
const changeIconScale = (val) => {
	gisState.iconZoom = val
	mapData.value.line_data = mapOption.value.series[1].data
	mapData.value.point_data = mapOption.value.series[2].data
	initMap(true)
}
defineExpose({
	save, changeScale, mapOption, mapChart, changeIconScale
})
const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
}
const debouncedScreenScale = debounce(screenScale, 200)
const gisChange = () => {
	state.isFileChanged = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
}
const handleMapData = (gisData) => {
	if (gisData.station_data.length > 0) {
		mapData.value.point_data = gisData.station_data.map(item => {
			return Object.assign({ ...item }, {
				value: [item.lon, item.lat],
				itemStyle: Object.assign(state.mode == 0 ? {
					borderColor: item.color || getGisStyle(item).color,
					borderType: item.borderType || 'solid'
				} : {
					borderColor: getGisStyle(item).color
				})
			})
		})
		state.showStationData = gisData.show_station_data || []
		state.showStationList = gisData.show_station_list || []
		// gisState.iconZoom = props.gisData.zoom_level
		// gisState.center = gisData.center_pos
		mapData.value.line_data = gisData.channel_data.flatMap(item => initLineData(
			{ 'type': state.mode == 0 ? 'draw' : 'edit', 'channel': item, 'apiZoom': gisState.iconZoom, 'geoScale': gisState.geoScale }
		))
	}
	initMap()
	storeModal.hiddenModal()
}
watch(() => props.gisData, v => {
	gisState.partition = props.partition
	gisState.geoScale = props.geoScale
	// gisState.originCenter = props.originCenter
	handleMapData(v)
})
watch(() => props.showStationName, v => {
	gisState.showStationName = v
	initMap()
})
onMounted(async() => {
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	mapChart.value = markRaw(echarts.init(gis.value))
	if (route.query.gisType != 'all') {
		state.busData = props.busData
		state.baseData = state.busData.filter(item => !state.showStationList.includes(item.index))
	}
	handleMapData(props.gisData)
	// const canvas = document.createElement('canvas')
	// const ctx = canvas.getContext('2d')
	// canvas.width = mapChart.value.clientWidth
	// canvas.height = mapChart.value.clientHeight
	// const image = new Image()
	// image.src = 'https://picx.zhimg.com/v2-c916763780e020e9ce6678f906e57632_r.jpg?source=2c26e567'
	// image.onload = function() {
	// 	if (image.width / image.height > canvas.width / canvas.height) {
	// 		ctx.drawImage(image, 0, (canvas.height - image.height * (canvas.width / image.width)) / 2, canvas.width, image.height * (canvas.width / image.width))
	// 	} else {
	// 		ctx.drawImage(image, (canvas.width - image.width * (canvas.height / image.height)) / 2, 0, image.width * (canvas.height / image.height), canvas.height)
	// 	}
	// 	areaColor: {
	//         image: canvas,
	//         repeat: 'no-repeat'
	//     },
	// }
})
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
window.addEventListener('keydown', function(event) {
	// if (event.key === 'Delete' || event.keyCode === 46) {
	// 	if (state.selectData && state.selectType == 'point') {
	// 		hidePoint()
	// 	} else {
	// 		return
	// 	}
	// }
	if (event.key === 'Control' || event.keyCode === 17) {
		state.ctrlKey = true
	}
})
window.addEventListener('keyup', function(event) {
	state.ctrlKey = false
	state.option_height = undefined
	state.option_width = undefined
	state.option_left = undefined
	state.option_top = undefined
})
</script>
<style lang="scss" scoped>
    .map-container{
        height: 100%;
        width: 100%;
        position: relative;
        .gis,.gis>div:first-child{
            transform-origin: 0 0;
            height: 100%;
            width: 100%;
        }
        .gis>div:first-child>div{
            cursor: pointer!important;
        }
        .gis_kanban{
            position: absolute;
            z-index: 1;
            height: 100%;
            // overflow-y: auto;
            width: 310px;
            background: rgb(248, 248, 248);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
            >div{
                box-sizing: border-box;
                border: 1px solid rgb(211, 211, 211);
                border-radius: 4px;
                background: rgb(246, 245, 245);
                width: 290px;
                >p{
                    color: rgb(71, 71, 71);
                    // font-family: Source Han Sans SC;
                    font-size: 16px;
                    line-height: 34px;
                    font-weight: 400;
                    padding-left: 15px;
                    border-bottom: 1px solid rgb(211, 211, 211);
                    border-radius: 3px 3px 0px 0px;
                    background: rgb(233, 234, 235);
                }
                margin-bottom: 15px;
            }
            .gis_base{
                >div{
                    padding: 10px 10px;
                    p{
                        color: rgb(78, 88, 98);
                        // font-family: Source Han Sans SC;
                        font-size: 15px;
                        font-weight: 400;
                        line-height: 32px;
                        letter-spacing: 0px;
                    }
                }
            }
            .base_info{
                height: 270px;
            }
            .connect_info{
                .ant-checkbox-group{
                    column-gap: 0px;
					width: 100%;
                    .ant-checkbox-wrapper{
                        min-width: 50%;
                    }
                }
                >div{
                    padding: 5px;
                    height: 270px;
                    overflow: auto;
                }
            }
        }
        .gis_menu{
            min-width: 174px;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.45);
            background: rgb(247, 246, 246);
            z-index: 10;
            padding: 10px 10px 20px;
            p{
                color: rgb(71, 71, 71);
                // font-family: Source Han Sans SC;
                font-size: 16px;
                font-weight: 400;
                letter-spacing: 0px;
                padding: 0 10px;
                line-height: 24px;
                &:hover{
                    cursor: pointer;
                    background: rgb(219, 233, 245);
                }
            }
        }
		.gis_option{
			left: 0;
			top: 0;
			height: 100%;
			width: 100%;
			z-index: 5;
			.gis_option_item{
				border: 1px solid red;
				position: absolute;
			}
		}
        .equipment_list{
			>div{
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				>div{
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 10px;
				}
				p{
					font-size: 16px;
					line-height: 32px;
				}
				svg{
					width: 30px;
					height: 30px;
					padding: 5px;
					border: 1px solid transparent;
					box-sizing: content-box;
					&:hover{
						cursor: move;
						border: 1px solid #aecfe6;
					}
				}
				.svg_sp{
					&:hover{
						cursor: pointer;
					}
				}
			}
		}
		.add_svg{
			position: absolute;
			z-index: 99;
			width: 30px;
			height: 30px;
			&:hover{
				cursor: pointer;
			}
		}
    }
</style>
<style lang="scss">
	.gis{
		div{
			cursor: pointer!important;
		}
	}
	.gis_save_modal{
		.ant-modal{
			width: auto!important;
		}
		.gis_save_content{
			padding: 40px;
			width: 520px;
			position: relative;
			.ant-input-number{
				width: 100%;
			}
			button{
				margin-top: 20px;
				width: 100px;
				position: relative;
				left: 340px;
			}
		}
	}
</style>
