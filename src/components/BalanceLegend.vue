<template>
  <a-modal wrapClassName="modal_legend" :afterClose="closeModal" :centered="true"  v-model:open="state.visible" :footer="null" :closable="false" :maskClosable="false">
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('数据选择') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large">
        <div class="modal_content relative">
          <a-checkbox
            v-model:checked="state.checkAll"
            :indeterminate="state.indeterminate"
            @change="onCheckAllChange"
          >
            {{ $t('全选') }}
          </a-checkbox>
          <a-checkbox
            v-model:checked="state.checkDefult"
            @change="onCheckDefult"
          >
            {{ $t('全选默认') }}
          </a-checkbox>
          <div class="modal_setting">
            <a-checkbox-group v-model:value="state.checkedList" @change="changeLegendList">
              <a-row>
                <a-col :span="8" v-for="item in props.lineConfig" :key="item.lineName">
                  <a-checkbox :value="item.lineName">{{ item.name }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
          <div class="modal_btn">
            <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
            <a-button @click="handleConfirm" type="primary" :style="{color: '#fff'}">{{ $t('确认') }}</a-button>
          </div>
        </div>

      </a-spin>
    </div>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive, watch } from 'vue'
// import { useRoute } from 'vue-router'
import { useRoute } from 'vue-router'
// import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'

const route = useRoute()

const props = defineProps({
	legendList: {
		type: Array,
		default: () => []
	},
	lineConfig: {
		type: Array,
		default: () => []
	},
	selectedList: {
		type: Array,
		default: () => []
	}
})

const state = reactive({
	routePath: route.fullPath,
	visible: true,
	loading: false,
	checkAll: false,
	indeterminate: true,
	checkDefult: false,
	checkedList: props.selectedList.map(item => item.lineName)
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}

const onCheckDefult = e => {
	Object.assign(state, {
		checkedList: e.target.checked ? props.legendList : [],
		checkAll: false,
		indeterminate: true

	})
}

const onCheckAllChange = e => {
	Object.assign(state, {
		checkedList: e.target.checked ? props.lineConfig.map(item => item.lineName) : [],
		indeterminate: false,
		checkDefult: false
	})
}

watch(
	() => state.checkedList,
	val => {
		state.indeterminate = !!val.length && val.length < props.lineConfig.length
		state.checkAll = val.length === props.lineConfig.length
	}
)

// 确认新建
const handleConfirm = () => {
	if (state.routePath !== route.fullPath) return
	emit('confirm', state.checkedList)
}

onMounted(() => {

})
</script>
<style lang="scss">
  .modal_legend{
    .ant-modal{
      width: 560px!important;
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 10px 30px 90px;
            .modal_setting{
              padding: 15px;
              height: 280px;
              border: 1px dashed #cdcbcb;
              border-radius: 6px;
              overflow: auto;
            }
            .modal_upload{
              padding: 10px 0;
              height: 55px;
            }
          }

        }
      }
    }
  }
</style>

