<!-- N-故障 -->
<template>
    <a-modal wrapClassName="GisModal GisModals" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
                <div>
                    <div class="modal_top">
                        <p>{{ getTitle }}</p>
                        <close-outlined class="pointer" @click="closeModal" />
                    </div>
                    <div class="modal-content">
                        <div>
                            <div>
                                <p>{{ $t('计算文件') }}</p>
                                <div class="upload_input">
                                    <a-upload
                                        v-model:file-list="state.fileList"
                                        :beforeUpload="()=>false"
                                        :showUploadList="false"
                                        :accept="props.acceptFileType"
                                        name="file"
                                        :maxCount="1"
                                        @change="changeFile"
                                        :disabled="props.BreakdownFileName&&props.BreakdownFilePath"
                                    >
                                        <div class="upload relative">
                                            <div>
                                                 <p>{{ state.fileList[0]?state.fileList[0].name:props.BreakdownFileName?props.BreakdownFileName:'' }}</p>
                                            </div>
                                            <a-button class="upload_btn" type="primary">
                                                <template #icon><folder-open-filled /></template>
                                                {{ $t('打开') }}</a-button>
                                        </div>
                                    </a-upload>
                                </div>
                            </div>
                            <div v-if="props.type==2">
                                <p>swi</p>
                                <div class="upload_input">
                                    <a-upload
                                        v-model:file-list="state.fileLists"
                                        :beforeUpload="()=>false"
                                        :showUploadList="false"
                                        :maxCount="1"
                                        accept=".swi"
                                        name="file"
                                        @change="changeFiles"
                                    >
                                        <div class="upload relative">
                                            <div>
                                                <p>{{ state.fileLists[0]?state.fileLists[0].name:' ' }}</p>
                                            </div>
                                            <a-button class="upload_btn" type="primary">
                                                <template #icon><folder-open-filled /></template>
                                                {{ $t('打开') }}</a-button>
                                        </div>
                                    </a-upload>
                                </div>
                            </div>
                            <div v-show="state.fileType=='tr'">
                                <p>{{ $t('日期选择') }}</p>
                                <a-date-picker
                                    v-model:value="state.selectTime"
                                    format="YYYY-MM-DD HH"
                                    :show-time="{ format: 'HH' }"
                                />
                            </div>
                            <div v-if="props.type==2">
                                <p>{{ $t('电压等级') }}</p>
                                <div class="select_div">
                                    <div>
                                        <p>{{ $t('电压上限') }}:</p>
                                        <a-select
                                            v-model:value="state.max_vn_kv"
                                            :options="state.voltageOptions1"
                                            :disabled="state.fileList.length === 0"
                                            :placeholder="$t('选择电压上限')"
                                            @change="changeVoltage1"
                                        ></a-select>
                                    </div>
                                    <div>
                                        <p>{{ $t('电压下限') }}:</p>
                                        <a-select
                                            v-model:value="state.min_vn_kv"
                                            :options="state.voltageOptions2"
                                            :disabled="state.fileList.length === 0"
                                            :placeholder="$t('选择电压下限')"
                                            @change="changeVoltage2"
                                        ></a-select>
                                    </div>
                                </div>
                                <p>{{ $t('故障类型') }}</p>
                                <div class="radio_div">
                                    <a-checkbox v-model:checked="state.short_circuit_1">
                                        {{ $t('单相短路') }}
                                    </a-checkbox>
                                    <a-checkbox v-model:checked="state.short_circuit_3">
                                        {{ $t('三相短路') }}
                                    </a-checkbox>
                                </div>
                                <p>{{ $t('扫描范围') }}</p>
                                <div class="radio_div">
                                    <a-radio-group v-model:value="state.areaType" name="radioGroup">
                                        <a-radio :value="true">{{ $t('分区') }}</a-radio>
                                        <a-radio :value="false">{{ $t('所有者') }}</a-radio>
                                    </a-radio-group>
                                </div>
                                <div class="transfer_div" v-show="state.areaType">
                                    <a-transfer
                                        v-model:target-keys="state.targetKeys1"
                                        :data-source="state.transferData1"
                                        show-search
                                        :filter-option="filterOption"
                                        :render="item => item.title"
                                    />
                                </div>
                                <div class="transfer_div" v-show="!state.areaType">
                                    <a-transfer
                                        v-model:target-keys="state.targetKeys2"
                                        :data-source="state.transferData2"
                                        show-search
                                        :filter-option="filterOption"
                                        :render="item => item.title"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="modal_btn">
                            <a-button v-if="props.type==0" :disabled="!state.filePath" @click="confirm(101)" type="primary">{{ $t('确认') }}</a-button>
                            <a-button v-if="props.type==1" :disabled="!state.filePath" @click="confirm(102)" type="primary">{{ $t('N-1故障') }}</a-button>
                            <a-button v-if="props.type==1" :disabled="!state.filePath" @click="confirm(103)" type="primary">{{ $t('N-2故障') }}</a-button>
                            <a-button v-if="props.type==2" :disabled="btnDisabled" @click="confirm(104)" type="primary">{{ $t('确认') }}</a-button>
                            <a-button @click="closeModal">{{ $t('取消') }}</a-button>
                        </div>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
 </template>
<script setup>
import { onMounted, reactive, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { UploadCaseFile } from '@/api/exampleApi'
import { UploadFile, UploadBpaFile, GetBpaInfo } from '@/api/gis'
import { checkFileExtension } from '@/utils/gis'
import { t } from '@/utils/common'
import message from '@/utils/message'
import { settingStore } from '@/store/settingStore'
const storeSetting = settingStore()
const { tableShow, taskName, taskUrl, tscanShow } = storeToRefs(storeSetting)
const props = defineProps({
	type: {
		type: Number
	},
	acceptFileType: {
		type: String,
		default: '.dat'
	},
	BreakdownFileName: {
		type: String
	},
	BreakdownFilePath: {
		type: String
	}
})
const state = reactive({
	ifShow: true,
	loading: false,
	fileList: [],
	fileLists: [],
	selectTime: undefined,
	filePath: props.BreakdownFilePath,
	filePaths: undefined,
	type: undefined,
	short_circuit_1: true,
	short_circuit_3: false,
	areaType: true,
	fileType: undefined,
	options: [
		{
			label: t('三相短路'),
			value: 0
		},
		{
			label: t('单相短路'),
			value: 1
		},
		{
			label: t('母线短路'),
			value: 2
		},
		{
			label: t('直流闭锁'),
			value: 3
		}
	],
	max_vn_kv: undefined,
	min_vn_kv: undefined,
	voltageOptions: [],
	voltageOptions1: [],
	voltageOptions2: [],
	targetKeys1: [],
	targetKeys2: [],
	transferData1: [],
	transferData2: []
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}
const filterOption = (inputValue, option) => {
	return option.title.indexOf(inputValue) > -1
}
const getTitle = computed(() => {
	if (props.type == 0) {
		return t('交流潮流')
	} else if (props.type == 1) {
		return 'N-K'
	} else if (props.type == 2) {
		return t('短路故障')
	} else {
		return ''
	}
})
const changeVoltage1 = () => {
	state.voltageOptions2 = state.voltageOptions.filter(item => {
		return item.value <= state.max_vn_kv
	}).sort((a, b) => a.value - b.value)
}
const changeVoltage2 = () => {
	state.voltageOptions1 = state.voltageOptions.filter(item => {
		return item.value >= state.min_vn_kv
	}).sort((a, b) => b.value - a.value)
}
const btnDisabled = computed(() => {
	if (state.short_circuit_1 === false && state.short_circuit_3 === false) {
		return true
	} else if (state.max_vn_kv === undefined || state.min_vn_kv === undefined || state.filePaths === undefined) {
		return true
	} else if ((state.areaType && state.targetKeys1.length === 0) || (!state.areaType && state.targetKeys2.length === 0)) {
		return true
	} else {
		return false
	}
})
const changeFile = ({ file, fileList }) => {
	if (checkFileExtension(file.name, 'dat')) {
		state.fileType = 'dat'
	} else if (checkFileExtension(file.name, 'tr')) {
		state.fileType = 'tr'
	} else if (checkFileExtension(file.name, 'tc')) {
		state.fileType = 'tc'
	}
	state.loading = true
	const formData = new FormData()
	formData.append('file', file)
	UploadFile({}, formData).then(res => {
		if (res.code == 1) {
			state.filePath = res.file_path
			if (props.type == 2 && state.fileType == 'dat') {
				GetBpaInfo({
					bpa_file_path: state.filePath
				}).then(res => {
					state.transferData1 = res.zone_list.map(item => {
						return {
							title: item,
							key: item
						}
					})
					state.transferData2 = res.owner_list.map(item => {
						return {
							title: item,
							key: item
						}
					})
					state.voltageOptions = res.vn_kv_list.map(item => {
						return {
							label: item + 'kV',
							value: item
						}
					})
					state.voltageOptions1 = [...state.voltageOptions].sort((a, b) => b.value - a.value)
					state.voltageOptions2 = [...state.voltageOptions].sort((a, b) => a.value - b.value)
					state.loading = false
				}).catch(() => {
					state.loading = false
				})
			} else {
				state.loading = false
			}
		}
	}).catch(() => {
		state.loading = false
	})
}
const changeFiles = ({ file, fileList }) => {
	state.loading = true
	const formData = new FormData()
	formData.append('file', file)
	UploadFile({}, formData).then(res => {
		state.loading = false
		if (res.code == 1) {
			state.filePaths = res.file_path
		}
	}).catch(() => {
		state.loading = false
	})
}
const confirm = (val) => {
	state.loading = true
	if (state.fileType == 'tc' || props.acceptFileType == '.tc,.dat') {
		UploadCaseFile({
			tc_file_name: state.filePath,
		    job_type_id: val
		}).then(res => {
			state.loading = false
			if (res.code == 1) {
				message.success(res.message)
				if (!tableShow.value) {
					tableShow.value = true
				}
				emit('close')
			}
		}).catch(() => {
			state.loading = false
		})
	} else {
		UploadBpaFile(Object.assign({
			job_type_id: val
		},
		state.fileType == 'dat' ? {
			bpa_file_path: state.filePath
		} : {

		}, props.type == 1 ? {
		} : props.type == 2 ? {
			swi_file_path: state.filePaths,
			short_circuit_1: state.short_circuit_1,
			short_circuit_3: state.short_circuit_3,
			min_vn_kv: state.min_vn_kv,
			max_vn_kv: state.max_vn_kv,
			select_zone_list: state.areaType ? state.targetKeys1 : undefined,
			select_owner_list: !state.areaType ? state.targetKeys2 : undefined
		} : {

		})).then(res => {
			state.loading = false
			if (res.code == 1) {
				if (props.type == 2) {
					if (navigator.userAgent.includes('Electron')) {
						window.electronApi.sendToMain('bpaScan', res.task_record_id)
						emit('close')
					} else {
						tscanShow.value = true
						taskName.value = res.task_name
						taskUrl.value = 'tscan://' + '?' + window.location.host + '_' + res.task_record_id
						const iframe = document.createElement('iframe')
						iframe.style.display = 'none'
						iframe.src = taskUrl.value
						document.body.appendChild(iframe)
						setTimeout(function() {
							document.body.removeChild(iframe)
							emit('close')
						}, 500)
					}
				} else {
					message.success(res.message)
					if (!tableShow.value) {
						tableShow.value = true
					}
					emit('close')
				}
			}
		}).catch(() => {
			state.loading = false
		})
	}
}
onMounted(async() => {

})
</script>
<style lang="scss">
    .GisModal,.GisModals{
        .ant-modal {
            width: auto!important;
        }
    }
    .GisModal {
        .modal-content {
            padding: 20px 30px 70px;
            width: 550px;
            >div:first-child {
                padding: 0 0 20px;
                >div>p {
                    color: rgb(80, 79, 74);
                    font-size: 18px;
                    line-height: 35px;
                    letter-spacing: 1px;
                    font-weight: bolder;
                }
                .upload_input {
                    margin-bottom: 5px;
                    .ant-upload-wrapper {
                        display: block;
                        height: 32px;
                    }
                    .upload {
                        display: flex;
                        align-items: center;
                        padding-left: 15px;
                        height: 32px;
                        width: 490px;
                        border: 1px solid #d9d9d9;
                        border-radius: 8px;
                        .upload_btn {
                            position: absolute;
                            right: 0px;
                            width: 105px;
                        }
                        .file_p{
                            font-size: 16px;
                            span{
                                &:hover{
                                    cursor: pointer;
                                    color: red;
                                }
                            }
                        }
                    }
                }
                .ant-select {
                    width: 100%;
                }
                .ant-picker {
                    width: 100%;
                }
                .list_disabled{
                    pointer-events: none;
                    opacity: 0.5;
                }
                .list_content{
                    >div{
                        height: 300px;
                        overflow-y: auto;
                        padding: 10px;
                        border: 1px solid rgb(196, 196, 196);
                        border-radius:5px;
                        >p{
                            padding-left: 10px!important;
                            margin-bottom: 5px!important;
                            font-size: 14px;
                            line-height: 28px;
                            border: 1px solid rgb(196, 196, 196);
                            border-radius:5px;
                            &:hover{
                                cursor: pointer;
                                color: rgb(54, 120, 191);
                                border: 1px solid rgb(54, 120, 191);
                                font-weight: bolder;
                                background-color: rgba(54, 120, 191,0.2);
                            }
                        }
                        .active{
                            color: rgb(54, 120, 191);
                            border: 1px solid rgb(54, 120, 191);
                            font-weight: bolder;
                            background-color: rgba(54, 120, 191,0.2);
                        }
                    }
                }
            }
        }
    }
    .GisModals{
        .ant-modal {
            .modal-content{
                width: 650px;
            }
            .upload_input{
                .upload{
                    width: 590px!important;
                }
            }
            .ant-radio-wrapper{
                min-width: 90px;
            }
            .radio_div{
                padding: 5px 10px;
            }
            .select_div{
                padding: 5px 10px 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
                >div{
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    width: 47%;
                    p{
                        min-width: 80px;
                        font-size: 16px;
                    }
                }
            }
            .transfer_div{
                margin-top: 10px;
                .ant-transfer{
                    background-color: #fff;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    .ant-transfer-list{
                        width: 50%;
                        height: 380px;
                    }
                    .ant-transfer-operation{
                        width: 30px;
                    }
                }
            }
        }
    }
</style>
