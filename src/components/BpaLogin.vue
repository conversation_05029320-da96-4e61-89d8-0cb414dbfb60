<template>
  <a-modal
    wrapClassName="modal_bpaLogin"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="475px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('用户登录') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content">
        <div>
          <a-input-password v-model:value="state.password" :placeholder="$t('请输入密码')" />
        </div>
        <div class="editPassword" @click="state.pwdShow=true">
          {{ $t('修改密码') }}
        </div>
        <div class="modal_btns_box">
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
          <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
        </div>
      </div>
    </div>
    <bpa-password v-if="state.pwdShow" @confirm="state.pwdShow=false" @close="state.pwdShow=false"></bpa-password>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'
// import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { setStandard, bpaDelete, bpaValidPwd } from '@/api/index'
import { t } from '@/utils/common'

const props = defineProps({
	record: {
		type: Object,
		default: () => {}
	},
	actionType: {
		type: String,
		default: ''
	}
})

const emits = defineEmits(['close', 'confirm'])

const state = reactive({
	visible: true,
	password: '',
	pwdShow: false
})
// const sourceValue = ref([])

const handleOk = () => {
	bpaValidPwd({
		pwd: state.password
	}).then(res => {
		if (res.code == 1) {
			if (props.actionType == 'standard') {
				setStandard({
					'bpa_id': props.record.id,
					'is_standard': props.record.is_standard == 1 ? 2 : 1,
					'password': state.password
				}).then(res => {
					if (res.code == 1) {
						message.success(res.msg || t('设置成功') + '！')
						emits('confirm')
					} else {
						message.error(res.data.msg || t('设置失败') + '！')
					}
				})
			} else {
				bpaDelete({
					'bpa_id': props.record.id,
					'password': state.password
				}).then(res => {
					if (res.code == 1) {
						message.success(res.msg || t('删除成功') + '！')
						emits('confirm')
					} else {
						message.error(res.data.msg || t('删除失败') + '！')
					}
				})
			}
		} else {
			message.error(res.data.msg || t('密码错误') + '！')
		}
	})
}

const closeModal = () => {
	emits('close')
}

onMounted(() => {

})

</script>
<style lang="scss" scoped>
  .modal_bpaLogin{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: left;
            // .ant-input-number .ant-input-number-input {
            //   width: 100%;
            //   height: 35px;
            // }
            >div {
              margin: 10px;
            }

            .editPassword {
              color: var(--base-color);
              text-decoration: underline;
            }

          }

          .modal_btns_box{
            // margin-top: 30px;
            text-align: center;
            button{
              width: 120px;
              height: 35px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
