<template>
  <div class="balance-chart">
    <div class="time_select1">
      <div>
        <a-select
          v-show="state.sim_mode !== 'long_term' && state.countMode == 'security'"
          style="width: 160px"
          v-model:value="state.scene"
          :options="state.sceneList"
          :placeholder="$t('请选择场景')"
          @change="handleSceneChange"
        >
        </a-select>
      </div>
      <div>
        <a-select
          style="width: 160px"
          v-model:value="state.partitionValue"
          :options="state.partitionOptions"
          :placeholder="$t('请选择分区')"
          @change="handlePartitionChange"
        >
        </a-select>
      </div>
      <div>
        <a-select
          ref="select"
          style="width: 160px"
          v-model:value="state.pickerType"
          :placeholder="$t('颗粒度选择')"
          @change="changePickerType"
        >
          <a-select-option value="dates">{{ $t('自定义') }}</a-select-option>
          <a-select-option value="date">{{ $t('日') }}</a-select-option>
          <a-select-option value="week">{{ $t('周') }}</a-select-option>
          <a-select-option value="month">{{ $t('月') }}</a-select-option>
          <a-select-option value="quarter">{{ $t('季度') }}</a-select-option>
        </a-select>
      </div>
      <div>
        <a-range-picker v-if="state.pickerType == 'dates'" v-model:value="state.searchTime" @change="changeTime" :disabled="!state.pickerType" picker="date" :disabled-date="disabledDate" :valueFormat="'YYYY-MM-DD'" :allowClear="false"></a-range-picker>
        <a-date-picker v-else v-model:value="state.searchSingleTime" @change="changeSingleTime" :picker="state.pickerType" :disabled="!state.pickerType" :disabled-date="disabledDate" valueFormat="YYYY-MM-DD" :allowClear="false"/>
      </div>
      <div>
        <a-select
          style="width: 120px"
          v-model:value="state.unitValue"
          :options="state.unitOptions"
          :placeholder="$t('单位')"
          @change="changeUnit"
        >
        </a-select>
      </div>
      <a-button @click="resetEcharts">{{ $t('确认') }}</a-button>
      <a-button @click="state.confidenceShow=true" style="margin: 0 10px;">{{ $t('置信度分析') }}</a-button>
      <!-- <a-button @click="state.legendShow=true">数据选择</a-button> -->
      <a-popover placement="bottom" trigger="click">
        <template #content>
          <a-checkbox-group v-model:value="state.legendList" @change="changeLegendList" :style="{width: '620px',height: '350px',overflow: 'auto',zoom:`${state.scale}`}">
            <a-row>
              <a-col :span="8" v-for="item in state.allLineConfig" :key="item.lineName">
                <a-checkbox :value="item.lineName" :disabled="!item.checkable">{{ item.name }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </template>
        <a-button>{{ $t('数据选择') }}</a-button>
      </a-popover>
    </div>
    <div ref="midTermLine" class="midTermLine" :style="`zoom:${state.zoom};transform:scale(${state.scale});transform-origin:0 0;`"></div>
    <div class="reload" @click="reloadClick">
      {{ $t('重新生成') }}
    </div>
  </div>
  <!-- 置信度分析 -->
  <confidence-analyse
    v-if="state.confidenceShow"
    :config="state.lineConfig"
    :sideList="state.midTermlineData"
    @close="state.confidenceShow=false"
  >
  </confidence-analyse>
  <!-- BPA下载 -->
  <bpa-download :timeNo="state.timeNo" v-if="state.downLoadShow" @close="state.downLoadShow=false"></bpa-download>
</template>
<script setup>
/* eslint-disable no-unreachable */
/* eslint-disable no-unused-vars */
import { ref, reactive, inject, onMounted, markRaw, onUnmounted, nextTick, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GetSimulationTaskResult, regenerateBalanceReports } from '@/api/index'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import { exportExcelTable, getMidLineSeries } from '@/utils/teap.js'
// import dayjs from 'dayjs'
import { debounce } from '@/utils/gis'
import ConfidenceAnalyse from './ConfidenceAnalyse.vue'
import { t } from '@/utils/common'
// const dateFormat = 'YYYY第wo'
const route = useRoute()
const store = settingStore()
// eslint-disable-next-line no-unused-vars
const { add_watermark, watermark_text, isChromeHigh } = storeToRefs(store)
const echarts = inject('ec')
const midTermLine = ref()
const midLineChart = ref()
const emit = defineEmits(['hideLoading', 'refresh', 'showLoading'])

const state = reactive({
	sim_mode: '',
	midTermlineData: {},
	scale: 1,
	zoom: 1,
	lineConfig: {},
	allLineConfig: [],
	lineConfig_selected: [],
	legendSelected: {},
	searchTime: undefined,
	searchSingleTime: undefined,
	isScale: false,
	routePath: route.fullPath,
	filePath: route.query.filePath,
	pickerType: 'dates',
	partitionValue: '',
	partitionOptions: [],
	partitionOptions1: [],
	partitionOptions2: [],
	unitValue: undefined,
	unitName: t('万千瓦'),
	unitOptions: [],
	startIndex: 0,
	endIndex: 0,
	startZoom: 0,
	endZoom: undefined,
	timeData: [],
	startDate: '',
	endDate: '',
	year: '',
	scene: 2,
	sceneList: [
		{
			value: 2,
			label: t('保供模式')
		}
	],
	countMode: '',
	confidenceShow: false,
	legendShow: false,
	legendList: [],
	downLoadShow: false
})
const changeUnit = (val) => {
	state.unitName = state.unitOptions.find(item => item.value == val).label
}
const changePickerType = (val) => {
	state.startIndex = 0
	state.endIndex = 0
	state.pickerType == 'dates' ? changeTime() : changeSingleTime()
}
const disabledDate = (current) => {
	return current < new Date(state.startDate) || current > new Date(state.endDate)
}
const changeTime = () => {
	state.searchSingleTime = state.searchTime[0]
	state.startIndex = state.timeData.findIndex(item => item == state.searchTime[0].slice(0, 10) + ' 00:00:00')
	state.endIndex = state.timeData.findIndex(item => item == state.searchTime[1].slice(0, 10) + ' 23:00:00')
}

const changeSingleTime = () => {
	if (state.pickerType == 'date') {
		state.startIndex = state.timeData.findIndex(item => item == state.searchSingleTime + ' 00:00:00')
		state.endIndex = state.timeData.findIndex(item => item == state.searchSingleTime + ' 23:00:00')
	} else if (state.pickerType == 'week') {
		const startOfWeek = new Date(state.searchSingleTime).getDay() == 0 ? 7 : new Date(state.searchSingleTime).getDay()
		const endOfWeek = new Date(state.searchSingleTime).getDay() == 0 ? 7 : new Date(state.searchSingleTime).getDay()
		state.startIndex = (state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') - 24 * (startOfWeek - 1)) < 0 ? 0 : state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') - 24 * (startOfWeek - 1)
		state.endIndex = (state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') + 24 * (7 - endOfWeek) + 23) > state.timeData.length - 1 ? state.timeData.length - 1 : state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 10) + ' 00:00:00') + 24 * (7 - endOfWeek) + 23
	} else if (state.pickerType == 'month') {
		state.startIndex = state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 7) + '-01 00:00:00')
		const month = +state.searchSingleTime.slice(5, 7)
		const day = [1, 3, 5, 7, 8, 10, 12].includes(month) ? 31 : [4, 6, 9, 11].includes(month) ? 30 : state.year % 4 === 0 ? 29 : 28
		// state.endIndex = state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month >= 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		const lastIndex = state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month >= 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		if (state.startIndex >= 0 && lastIndex < 0) {
			state.endIndex = state.timeData.length - 1
		} else {
			state.endIndex = lastIndex
		}
	} else if (state.pickerType == 'quarter') {
		state.startIndex = state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 7) + '-01 00:00:00')
		const month = +state.searchSingleTime.slice(5, 7) + 2
		const day = [1, 3, 5, 7, 8, 10, 12].includes(month) ? 31 : 30
		// state.endIndex = state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month > 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		const lastIndex = state.timeData.findIndex(item => item == state.searchSingleTime.slice(0, 5) + (month > 10 ? month : '0' + month) + '-' + day + ' 23:00:00')
		if (state.startIndex >= 0 && lastIndex < 0) {
			state.endIndex = state.timeData.length - 1
		} else {
			state.endIndex = lastIndex
		}
	}
}

const changeLegendList = (data) => {
	state.legendShow = false
	state.lineConfig_selected = state.allLineConfig.filter(item => data.includes(item.lineName))
	initMidTermEcharts()
}

const resetEcharts = () => {
	if (!state.pickerType) return
	if (!state.searchTime) return

	initMidTermEcharts()
}
const initMidTermEcharts = () => {
	const flag = state.sim_mode == 'short_term'
	const option = getMidLineSeries(state.midTermlineData, state.lineConfig_selected, state.legendSelected, midLineChart.value, add_watermark.value, watermark_text.value, download_table, changeLegendSelected, state.startIndex, state.endIndex, state.startZoom, state.endZoom, state.unitValue, state.unitName, flag)
	midLineChart.value.setOption(option, true)
	midLineChart.value.on('datazoom', (event) => {
		state.startZoom = event.start
		state.endZoom = event.end
	})
	// 监听图例选择状态变化事件
	midLineChart.value.on('legendselectchanged', function(params) {
		state.legendSelected = params.selected
	})
}
const download_table = (legend) => {
	const legendData = state.lineConfig.map(item => item.name) // 下载全部数据
	const fileName1 = route.query.name.slice(5, -5)
	const fileName2 = route.query.name.slice(-4)
	exportExcelTable(state.midTermlineData, legend, legendData, state.lineConfig, t('电力电量平衡') + `(${state.unitName})` + '.xlsx', `${fileName1}-${fileName2}(${state.unitName})-${state.partitionValue ? state.partitionValue : t('全系统')}-${t('电力电量平衡')}.xlsx`, state.unitValue)
}

const downloadBPI = (val) => {
	state.downLoadShow = true
	if (Array.isArray(val)) {
		state.timeNo = val
	} else {
		state.timeNo = [val, val]
	}
}
window.downloadBPI = downloadBPI

const changeLegendSelected = (legend) => {
	state.legendSelected = legend
}

const handleSceneChange = (val) => {
	emit('showLoading')
	state.partitionValue = ''
	state.partitionOptions = val == 1 ? state.partitionOptions1 : state.partitionOptions2
	getMidTermResult('init')
}
const handlePartitionChange = (val) => {
	emit('showLoading')
	if (!val) return getMidTermResult('all')
	const tempQuery = {
		'group': state.scene == 1 ? `_result.balance_df.zone_power.${val}` : `_result.scenario_balance_df.zone_power.${val}`,
		result_file_path: state.filePath,
		'to_list': true
	}
	handleEchartsData('partition', tempQuery)
}
const handleEchartsData = (type, tempQuery) => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	GetSimulationTaskResult(url, tempQuery).then(res => {
		if (res.code == 1) {
			const { structure, data } = res

			state.midTermlineData = data
			state.midTermlineData.time = data.Time
			state.timeData = data.Time
			state.lineConfig = Object.keys(structure.columns).filter(items => structure.columns[items].name && data[items] && structure.columns[items].color).map(item => Object.assign({ lineName: item }, structure.columns[item])).sort((a, b) => a.sequence - b.sequence)
			state.allLineConfig = Object.keys(structure.columns).filter(items => structure.columns[items].name && structure.columns[items].color).map(item => Object.assign({ lineName: item },
				{ checkable: Object.keys(state.midTermlineData).includes(item) },
				structure.columns[item])).sort((a, b) => a.sequence - b.sequence)
			state.unitOptions = Object.keys(structure.unit).map(item => {
				return {
					label: item,
					value: structure.unit[item]
				}
			})
			state.unitValue = state.unitOptions[0].value

			if (type.includes('init') || type == 'systermChartChange') { // 初始化才更新，切换分区不更新
				state.year = +state.timeData[0].slice(0, 4)
				state.startDate = state.timeData[0].slice(0, 10) + ' 00:00:00'
				state.endDate = state.timeData[state.timeData.length - 1].slice(0, 10)
				state.searchTime = state.searchTime ? state.searchTime : [state.timeData[0].split(' ')[0], state.timeData[state.timeData.length - 1].split(' ')[0]]

				state.lineConfig_selected = state.allLineConfig.filter(item => item.default_show || item.default_show == undefined)
				state.legendList = state.allLineConfig.filter(item => item.default_show || item.default_show == undefined).map(item => item.lineName)

				if (!state.searchSingleTime) {
					state.searchSingleTime = state.timeData[0].split(' ')[0]
				}

				state.endIndex = state.endIndex ? state.endIndex : state.timeData.length
				if (type == 'systermChartChange') {
					if (sessionStorage.getItem(state.filePath + 'lineConfig_selected')) {
						state.lineConfig_selected = JSON.parse(sessionStorage.getItem(state.filePath + 'lineConfig_selected'))
						sessionStorage.removeItem(state.filePath + 'lineConfig_selected')
					}

					state.lineConfig_selected.forEach(item => {
						item.color = state.allLineConfig.find(item1 => item1.lineName == item.lineName).color
						item.sequence = state.allLineConfig.find(item1 => item1.lineName == item.lineName).sequence
					})
					sessionStorage.setItem(state.filePath + 'lineConfig_selected', JSON.stringify(state.lineConfig_selected))
				}
				screenScale(1)
			} else {
				initMidTermEcharts()
			}
		}
		emit('hideLoading')
	}).catch(() => {
		emit('hideLoading')
	})
}
const getMidTermResult = (type) => {
	const tempPartition = sessionStorage.getItem(state.filePath + 'partitionValue2')
	let tempQuery
	if (sessionStorage.getItem(state.filePath + 'partitionValue2')) {
		tempQuery = {
			'group': state.scene == 1 ? `_result.balance_df.zone_power.${tempPartition}` : `_result.scenario_balance_df.zone_power.${tempPartition}`,
			result_file_path: state.filePath,
			'to_list': true
		}
	} else {
		tempQuery = {
			'group': state.scene == 1 ? '_result.balance_df' : '_result.scenario_balance_df',
			result_file_path: state.filePath
		}
	}
	handleEchartsData(type, tempQuery)
}

const getScene = async() => {
	if (state.routePath !== route.fullPath) return
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result`,
		result_file_path: state.filePath
	}
	GetSimulationTaskResult(url, tempQuery).then(res => {
		state.sim_mode = res.sim_mode
		if (res.sim_mode == 'mid_term' && res.security_run) {
			res.accommodation_run ? state.sceneList = [
				{
					value: 2,
					label: t('保供模式')
				},
				{
					value: 1,
					label: t('促消纳模式')
				}

			] : state.sceneList = [
				{
					value: 2,
					label: t('保供模式')
				}
			]
			state.countMode = 'security'
			state.scene = 2
			Promise.all([GetSimulationTaskResult(url, {
				result_file_path: state.filePath,
				group: '_result.balance_df.zone_power'
			}), GetSimulationTaskResult(url, {
				result_file_path: state.filePath,
				group: '_result.scenario_balance_df.zone_power'
			})]).then(([res1, res2]) => {
				state.partitionOptions1 = [{
					value: '',
					label: t('全系统')
				}].concat(res1.data.map(item => {
					return {
						value: item,
						label: item
					}
				}))
				state.partitionOptions2 = [{
					value: '',
					label: t('全系统')
				}].concat(res2.data.map(item => {
					return {
						value: item,
						label: item
					}
				}))
				state.partitionOptions = state.partitionOptions2
				getMidTermResult('init')
			})
		} else {
			state.sceneList = [
				{
					value: 1,
					label: t('促消纳模式')
				}
			]
			state.scene = 1
			state.countMode = 'accommodation'
			GetSimulationTaskResult(url, {
				result_file_path: state.filePath,
				group: '_result.balance_df.zone_power'
			}).then(res => {
				state.partitionOptions = [{
					value: '',
					label: t('全系统')
				}].concat(res.data.map(item => {
					return {
						value: item,
						label: item
					}
				}))
				getMidTermResult('init')
			})
		}
	})
}

const reloadClick = () => {
	if (state.routePath !== route.fullPath) return
	emit('showLoading')
	regenerateBalanceReports({
		result_file_path: route.query.filePath
	}).then(res => {
		emit('hideLoading')
		if (res.code == 1) {
			getScene()
		}
	}).catch(() => {
		emit('hideLoading')
	})
}

const screenScale = (val) => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
	} else {
		root = document.body.style.zoom
	}
	state.zoom = 1 / root
	state.scale = root
	if (val != 1) {
		if (state.routePath == route.fullPath) {
			emit('refresh', 2)
		} else {
			state.isScale = true
		}
	} else {
		if (sessionStorage.getItem(state.filePath + 'scene2')) {
			state.scene = +sessionStorage.getItem(state.filePath + 'scene2')
			sessionStorage.removeItem(state.filePath + 'scene2')
		}
		if (sessionStorage.getItem(state.filePath + 'partitionValue2')) {
			state.partitionValue = sessionStorage.getItem(state.filePath + 'partitionValue2')
			sessionStorage.removeItem(state.filePath + 'partitionValue2')
		}
		if (sessionStorage.getItem(state.filePath + 'unitValue')) {
			state.unitValue = Number(sessionStorage.getItem(state.filePath + 'unitValue'))
			sessionStorage.removeItem(state.filePath + 'unitValue')
		}
		if (sessionStorage.getItem(state.filePath + 'startIndex') || sessionStorage.getItem(state.filePath + 'endIndex')) {
			state.startIndex = +sessionStorage.getItem(state.filePath + 'startIndex')
			state.endIndex = +sessionStorage.getItem(state.filePath + 'endIndex')
			sessionStorage.removeItem(state.filePath + 'startIndex')
			sessionStorage.removeItem(state.filePath + 'endIndex')
		}
		if (sessionStorage.getItem(state.filePath + 'searchSingleTime')) {
			state.searchSingleTime = sessionStorage.getItem(state.filePath + 'searchSingleTime')
			sessionStorage.removeItem(state.filePath + 'searchSingleTime')
		}
		if (sessionStorage.getItem(state.filePath + 'pickerType')) {
			state.pickerType = sessionStorage.getItem(state.filePath + 'pickerType')
			sessionStorage.removeItem(state.filePath + 'pickerType')
		}
		if (sessionStorage.getItem(state.filePath + 'searchTime')) {
			state.searchTime = JSON.parse(sessionStorage.getItem(state.filePath + 'searchTime'))
			sessionStorage.removeItem(state.filePath + 'searchTime')
		}
		if (sessionStorage.getItem(state.filePath + 'lineConfig_selected')) {
			state.lineConfig_selected = JSON.parse(sessionStorage.getItem(state.filePath + 'lineConfig_selected'))
			sessionStorage.removeItem(state.filePath + 'lineConfig_selected')
		}
		if (sessionStorage.getItem(state.filePath + 'legendList')) {
			state.legendList = JSON.parse(sessionStorage.getItem(state.filePath + 'legendList'))
			sessionStorage.removeItem(state.filePath + 'legendList')
		}
		if (sessionStorage.getItem(state.filePath + 'legendSelected')) {
			state.legendSelected = JSON.parse(sessionStorage.getItem(state.filePath + 'legendSelected'))
			sessionStorage.removeItem(state.filePath + 'legendSelected')
		}
		if (sessionStorage.getItem(state.filePath + 'startZoom2')) {
			state.startZoom = +sessionStorage.getItem(state.filePath + 'startZoom2')
			sessionStorage.removeItem(state.filePath + 'startZoom2')
		}
		if (sessionStorage.getItem(state.filePath + 'endZoom2')) {
			state.endZoom = +sessionStorage.getItem(state.filePath + 'endZoom2')
			sessionStorage.removeItem(state.filePath + 'endZoom2')
		}
		initMidTermEcharts()
	}
}

defineExpose({ getMidTermResult })
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	sessionStorage.setItem(state.filePath + 'scene2', state.scene)
	if (state.partitionValue) sessionStorage.setItem(state.filePath + 'partitionValue2', state.partitionValue)
	sessionStorage.setItem(state.filePath + 'unitValue', state.unitValue)
	sessionStorage.setItem(state.filePath + 'startIndex', state.startIndex)
	sessionStorage.setItem(state.filePath + 'endIndex', state.endIndex)
	sessionStorage.setItem(state.filePath + 'searchSingleTime', state.searchSingleTime)
	sessionStorage.setItem(state.filePath + 'pickerType', state.pickerType)
	if (state.searchTime) sessionStorage.setItem(state.filePath + 'searchTime', JSON.stringify(state.searchTime))
	sessionStorage.setItem(state.filePath + 'lineConfig_selected', JSON.stringify(state.lineConfig_selected))
	sessionStorage.setItem(state.filePath + 'legendList', JSON.stringify(state.legendList))
	sessionStorage.setItem(state.filePath + 'legendSelected', JSON.stringify(state.legendSelected))
	sessionStorage.setItem(state.filePath + 'startZoom2', JSON.stringify(state.startZoom))
	sessionStorage.setItem(state.filePath + 'endZoom2', JSON.stringify(state.endZoom))
	window.removeEventListener('resize', debouncedScreenScale)
})
onActivated(() => {
	if (state.isScale) {
		emit('refresh', 2)
		state.isScale = false
	}
})
onMounted(() => {
	emit('showLoading')
	window.addEventListener('resize', debouncedScreenScale)
	midLineChart.value = markRaw(echarts.init(midTermLine.value))

	getScene()
})
</script>
<style lang="scss" scoped>
.balance-chart {
  position: relative;
  width: 100%;
  height: 100%;
  .reload {
    position: absolute;
    top: 0px;
    left: 10px;
    z-index: 30;
    width: 98px;
    height: 28px;
    border: 1px solid #789bb7;
    border-radius: 12px;
    line-height: 28px;
    text-align: center;
    font-size: 14px;
    color: #789bb7;
    cursor: pointer;
  }
}
.midTermLine {
  height: calc(100% - 16px);
  width: 100%;
  transform-origin: 0 0;
}
.time_select1{
//   position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
//   width: 100%;
//   z-index: 9;
  >div{
    margin: 0 20px;
  }
}
</style>
