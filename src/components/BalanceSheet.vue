<template>
  <div class="balance-body">
    <div class="balance_left">
      <div class="partitionBox">
        <a-select
          size="small"
          :allowClear="true"
          :bordered="false"
          :disabled="state.balance_type == 'power_monthly' || state.balance_type == 'power'"
          :placeholder="$t('请选择分区')"
          v-model:value="state.partitionValue"
          :options="state.partitionOptions"
          style="width: 100%"
          @change="handlePartitionChange"
        ></a-select>
      </div>

      <div class="balance_type" v-if="state.sim_mode == 'capacity_balance'">
        <div :class="state.balance_type == 'balance_power_v2' ? 'active': ''" @click="changeBalanceTable('balance_power_v2', '电力盈亏平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('电力盈亏平衡表') }}
        </div>
        <div :class="state.balance_type == 'p_rate_v2' ? 'active': ''" @click="changeBalanceTable('p_rate_v2', '电源出力系数表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('电源出力系数表') }}
        </div>
      </div>
      <div class="balance_type" v-else-if="state.sim_mode == 'long_term' || state.sim_mode == 'short_term' || state.countMode == 'accommodation'">
        <div :class="state.balance_type == 'neps_power' ? 'active': ''" @click="changeBalanceTable('neps_power', '月度电力平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('月度电力平衡表') }}
        </div>
        <div :class="state.balance_type == 'neps_electricity_monthly' ? 'active': ''" @click="changeBalanceTable('neps_electricity_monthly', '月度电量平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('月度电量平衡表') }}
        </div>
      </div>
      <div class="balance_type" v-else>
        <div v-show="state.countMode !== 'accommodation'" :class="state.balance_type == 'neps_esp_pwbalance_monthly' ? 'active': ''" @click="changeBalanceTable('neps_esp_pwbalance_monthly', '保供模式月度电力平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('保供模式月度电力平衡表') }}
        </div>
        <div v-show="state.countMode !== 'accommodation'" :class="state.balance_type == 'neps_esp_electric_monthly' ? 'active': ''" @click="changeBalanceTable('neps_esp_electric_monthly', '保供模式月度电量平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('保供模式月度电量平衡表') }}
        </div>
        <div v-show="state.countMode !== 'security'" :class="state.balance_type == 'neps_power' ? 'active': ''" @click="changeBalanceTable('neps_power', '促消纳模式月度电力平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('促消纳模式月度电力平衡表') }}
        </div>
        <div v-show="state.countMode !== 'security'" :class="state.balance_type == 'neps_electricity_monthly' ? 'active': ''" @click="changeBalanceTable('neps_electricity_monthly', '促消纳模式月度电量平衡表')">
          <FileTextOutlined style="margin-right: 5px;"/>{{ $t('促消纳模式月度电量平衡表') }}
        </div>
      </div>
    </div>
    <div class="balance_content">
      <div class="downLoad">
        <a-tooltip>
          <template #title>{{ state.balance_title }}</template>
          <DownloadOutlined  @click="downLoadClick" style="margin-right: 15px;"/>
        </a-tooltip>
        <a-spin :spinning="state.spinning" size="small">
          <a-tooltip>
            <template #title>{{ $t('全时序电力平衡表') }}</template>
            <CloudDownloadOutlined v-if="state.sim_mode !== 'capacity_balance'" @click="downLoadAllClick" style="font-size: 18px;"/>
          </a-tooltip>
        </a-spin>
      </div>
      <div class="reload" v-if="state.sim_mode !== 'capacity_balance'" @click="reloadClick">
        {{ $t('重新生成') }}
      </div>
      <div class="balance_title">
        {{ state.balance_title }}
        <div class="unitBox">
          <a-select
            ref="select"
            size="small"
            :bordered="false"
            v-model:value="state.unitValue"
            style="width: 100px"
            :options="state.unitOptions"
            @change="handleUnitChange"
          ></a-select>
        </div>
      </div>
      <div class="time_select" @click="state.balanceTimeVisible = true" v-if="state.isBalancePowerTimeAll && state.balance_type !== 'neps_electricity_monthly' && state.balance_type !== 'neps_esp_electric_monthly'">
        {{ $t('自定义盈亏控制时刻') }}
      </div>

      <div class="neps_table">
        <AgGrid ref="agGridRef" :isEdit="'balanceSheet'" :istree="state.balance_type == 'neps_power' || state.balance_type == 'neps_electricity_monthly' || state.balance_type == 'neps_esp_pwbalance_monthly' || state.balance_type == 'neps_esp_electric_monthly' || state.balance_type == 'balance_power_v2' || state.balance_type == 'p_rate_v2' ? true : false"></AgGrid>
      </div>

    </div>
  </div>
  <!-- 时序详情 -->
  <balance-time v-if="state.balanceTimeVisible"
    ref="balanceTimeRef"
    @cancel="state.balanceTimeVisible = false"
  />
</template>
<script setup>
// import message from '@/utils/message'
// import Mitt from '@/utils/mitt.js'
import { ref, reactive, onMounted, onUnmounted, defineEmits, nextTick } from 'vue'
import { useRoute } from 'vue-router'
// import { settingStore } from '@/store/settingStore'
// import { storeToRefs } from 'pinia'
// import { GetMidTermTaskResult } from '@/api/exampleApi'
import { GetSimulationTaskResult, GetMidTermTaskResult, regenerateBalanceReports } from '@/api/index'
import { downloadApiFile } from '@/utils/common.js'
import { t } from '@/utils/common'
// const storeSetting = settingStore()
// const { balanceData } = storeToRefs(storeSetting)

const route = useRoute()

const emits = defineEmits(['hideLoading', 'showLoading'])

// AG-Grid表格
const agGridRef = ref(null)
const balanceTimeRef = ref(null)

const state = reactive({
	routePath: route.fullPath,
	balanceTimeVisible: false,
	isBalancePowerTimeAll: false,
	sim_mode: '',
	countMode: '',
	filePath: route.query.filePath,
	balance_type: '',
	balance_title: '',
	balanceData: {},
	balanceStructure: {},
	partitionOptions: [],
	partitionValue: null,
	unitOptions: [],
	unitValue: undefined,
	spinning: false,
	typeObj: {
		'neps_power': 'neps_zone_power',
		'neps_electricity_monthly': 'neps_zone_electricity_monthly',
		'neps_esp_pwbalance_monthly': 'neps_zone_esp_pwbalance_monthly',
		'neps_esp_electric_monthly': 'neps_zone_esp_electric_monthly',
		'balance_power_v2': 'balance_power_v2',
		'p_rate_v2': 'p_rate_v2'

	}
})

// defineExpose({ handleTabChange })

const downLoadClick = () => {
	const fileName1 = route.query.name.slice(5, -5)
	const fileName2 = route.query.name.slice(-4)
	agGridRef.value.onBtExport(`${fileName1}-${fileName2}${state.balance_title}-${state.partitionValue ? state.partitionValue : t('全系统')}-${t('盈亏时段电力平衡表')}.xlsx`)
}

const downLoadAllClick = () => {
	// const fileName1 = route.query.name.slice(5, -5)
	// const fileName2 = route.query.name.slice(-4)
	state.spinning = true
	GetMidTermTaskResult({
		'group': `_result.balance_df.neps_alltime_power.${state.partitionValue ? state.partitionValue : 'all'}`,
		'result_file_path': route.query.filePath,
		'to_csv_format': true
	}).then(res => {
		state.spinning = false
		downloadApiFile(res)
	}).catch(() => {
		state.spinning = false
	})
	// agGridRef.value.onBtExport(`${fileName1}-${fileName2}${state.balance_title}-${state.partitionValue ? state.partitionValue : '全系统'}-盈亏时段电力平衡表.xlsx`)
}

const handleUnitChange = (val) => {
	const tableData = JSON.parse(JSON.stringify(state.balanceData))
	tableData.data.forEach(element => {
		for (const key in element) {
			if (typeof element[key] === 'number') {
				element[key] = element[key] * val
			}
		}
	})
	nextTick(() => {
		agGridRef.value.setBlanceData(tableData)
	})
}

const handlePartitionChange = (val) => {
	getMidTermTaskResult(state.balance_type)
}

const changeBalanceTable = (val, name) => {
	if (val == 'power_monthly' || val == 'power') {
		state.partitionValue = undefined
	}
	emits('showLoading')
	state.unitOptions = []
	state.unitValue = undefined
	state.balance_type = val
	state.balance_title = t(name)
	getMidTermTaskResult(val)
}

const reloadClick = () => {
	if (state.routePath !== route.fullPath) return
	emits('showLoading')
	agGridRef.value.setBlanceData({
		columns: [],
		data: []
	})
	regenerateBalanceReports({
		result_file_path: route.query.filePath
	}).then(res => {
		emits('hideLoading')
		if (res.code == 1) {
			getMidTermTaskResult(state.balance_type)
		}
	}).catch(() => {
		emits('hideLoading')
	})
}

const getMidTermTaskResult = (val) => {
	if (state.routePath !== route.fullPath) return

	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	let tempStr = ''
	if (!state.partitionValue) {
		tempStr = `${val}`
	} else {
		tempStr = `${state.typeObj[val] ? state.typeObj[val] : val}.${state.partitionValue}`
	}

	const tempQuery = {
		'group': `_result.balance_df.${tempStr}`
	}
	tempQuery.result_file_path = route.query.filePath
	GetSimulationTaskResult(url, tempQuery).then(res => {
		emits('hideLoading')
		if (res.code == 1) {
			const { columns, data, structure } = res
			if (val != 'p_rate_v2') {
				state.unitOptions = Object.keys(structure.unit).map(item => {
					return {
						value: structure.unit[item],
						label: item
					}
				})
				state.unitValue = state.unitOptions[0].value
			} else {
				state.unitValue = undefined
			}

			const tableData = {
				columns,
				data
			}

			tableData.columns.forEach((item, index) => {
				item.width = 115
			})

			state.balanceData = tableData
			nextTick(() => {
				agGridRef.value.setBlanceData(tableData)
			})
		}
	}).catch(() => {
		emits('hideLoading')
	})
}

// 获取计算类型、计算模式等信息
const getScene = async() => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result`,
		result_file_path: route.query.filePath
	}
	GetSimulationTaskResult(url, tempQuery).then(res => {
		state.sim_mode = res.sim_mode
		state.isBalancePowerTimeAll = res.data.some(item => item == '_result.balance_df.neps_alltime_power.all')
		if (res.sim_mode == 'mid_term') {
			if (res.security_run) {
				// console.log(88888)

				res.accommodation_run ? state.countMode = 'all' : state.countMode = 'security'
				state.balance_type = 'neps_esp_pwbalance_monthly'
				state.balance_title = t('保供模式月度电力平衡表')
				// getMidTermTaskResult('neps_esp_pwbalance_monthly')
			} else {
				state.countMode = 'accommodation'
				state.balance_type = 'neps_power'
				state.balance_title = t('月度电力平衡表')
				// getMidTermTaskResult('neps_power')
			}
		} else if (res.sim_mode == 'capacity_balance') {
			state.countMode = ''
			state.balance_type = 'balance_power_v2'
			state.balance_title = t('电力盈亏平衡表')
			// getMidTermTaskResult(`balance_power_v2`)
		} else {
			state.countMode = ''
			state.balance_type = 'neps_power'
			state.balance_title = t('月度电力平衡表')
			// getMidTermTaskResult('neps_power')
		}
		screenScale()
	})
}

// 获取分区
const getPartition = () => {
	if (state.routePath !== route.fullPath) return
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result.balance_df.zone_power`
	}
	tempQuery.result_file_path = route.query.filePath
	GetSimulationTaskResult(url, tempQuery).then(res => {
		if (res.code == 1) {
			state.partitionOptions = [{
				value: '',
				label: t('全系统')
			}].concat(res.data.map(item => {
				return {
					value: item,
					label: item
				}
			}))
		}
	}).catch(() => {
		// emits('cancel')
	})
}

const screenScale = () => {
	if (sessionStorage.getItem(state.filePath + 'partitionValue1')) {
		state.partitionValue = sessionStorage.getItem(state.filePath + 'partitionValue1')
		sessionStorage.removeItem(state.filePath + 'partitionValue1')
	}
	if (sessionStorage.getItem(state.filePath + 'balance_type')) {
		state.balance_type = sessionStorage.getItem(state.filePath + 'balance_type')
		sessionStorage.removeItem(state.filePath + 'balance_type')
	}
	if (sessionStorage.getItem(state.filePath + 'countMode') || sessionStorage.getItem(state.filePath + 'countMode') == '') {
		state.countMode = sessionStorage.getItem(state.filePath + 'countMode')
		sessionStorage.removeItem(state.filePath + 'countMode')
	}
	if (sessionStorage.getItem(state.filePath + 'balance_title')) {
		state.balance_title = sessionStorage.getItem(state.filePath + 'balance_title')
		sessionStorage.removeItem(state.filePath + 'balance_title')
	}
	getMidTermTaskResult(state.balance_type)
}

onUnmounted(() => {
	if (state.partitionValue) sessionStorage.setItem(state.filePath + 'partitionValue1', state.partitionValue)
	sessionStorage.setItem(state.filePath + 'balance_type', state.balance_type)
	sessionStorage.setItem(state.filePath + 'countMode', state.countMode)
	sessionStorage.setItem(state.filePath + 'balance_title', state.balance_title)
})

onMounted(() => {
	emits('showLoading')
	getScene()
	getPartition()
})

// const formatCloud = computed(() => {
// 	return function(index) {
// 		if (typeof index === 'number') {
// 			return parseFloat(index * parseFloat(state.unitValue)).toFixed(2)
// 		} else {
// 			return index
// 		}
// 	}
// })

</script>
<style lang="scss" scoped>
.balance-body {
  width: 100%;
  height: 100%;
  display: flex;
  .balance_left {
    width: 218px;
    margin-right: 5px;

    .partitionBox {
      height: 40px;
      border: 1px solid #999;
      border-radius: 4px;
      padding: 8px 0;
      margin-bottom: 10px;
    }
    .balance_type {
      width: 100%;
      border: 1px solid #999;
      border-radius: 4px;
      padding: 10px 0;
      >div {
        width: 100%;
        height: 40px;
        line-height: 36px;
        padding-left: 2px;
        cursor: pointer;
      }
    }
    .active {
      background-color: #e8f1ff;
    }
  }
  .balance_content{
    width: 100%;
    height: 100%;
    position: relative;
    .time_select {
      position: absolute;
      top: 5px;
      left: 180px;
      z-index: 30;
      width: 178px;
      height: 28px;
      border: 1px solid #789bb7;
      border-radius: 4px;
      line-height: 28px;
      text-align: center;
      font-size: 14px;
      color: #789bb7;
      cursor: pointer;
    }
    .downLoad {
      position: absolute;
      top: 20px;
      right: 280px;
      z-index: 30;
      font-size: 18px;
      // color: #1f7fed;
      display: flex;
    }
    .reload {
      position: absolute;
      top: 5px;
      left: 10px;
      z-index: 30;
      width: 98px;
      height: 28px;
      border: 1px solid #789bb7;
      border-radius: 12px;
      line-height: 28px;
      text-align: center;
      font-size: 14px;
      color: #789bb7;
      cursor: pointer;
    }
    .balance_title {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, 0);
      z-index: 30;
      width: 40%;
      height: 40px;
      border: 1px solid #999;
      border-radius: 4px;
      line-height: 40px;
      text-align: center;
      margin: 0 auto;
      // position: relative;
    }
    .unitBox {
      position: absolute;
      top: 0;
      right: 0;
    }
    .neps_table {
      margin-top: 10px;
      width: 100%;
      height: 100%;
    }

  }
}
</style>
