<template>
  <a-modal
    wrapClassName="modal_balanceGoals"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="visible"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t('透视图修改') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <div class="btn_box">
          <div>
            <p>{{ $t('一级类别') }}</p>
            <a-select v-model:value="state.type1" :style="{width: '120px'}" @change="changeType1">
              <a-select-option value="area">{{ $t('所属区域') }}</a-select-option>
              <a-select-option value="zone">{{ $t('所属分区') }}</a-select-option>
              <a-select-option value="owner">{{ $t('所有者') }}</a-select-option>
            </a-select>
          </div>
          <div>
            <p>{{ $t('二级类别') }}</p>
            <a-select
              v-model:value="state.type2"
              :options="state.options1"
              :style="{width: '120px'}"
              :disabled="state.type1 ? false : true"
              :allowClear="true"
              @change="changeType2">
            </a-select>
          </div>
          <div>
            <p>{{ $t('三级类别') }}</p>
            <a-select
              v-model:value="state.type3"
              :options="state.options2"
              :style="{width: '120px'}"
              :allowClear="true"
              :disabled="state.type2 ? false : true">
            </a-select>
          </div>
          <div>
            <p>{{ $t('统计参数') }}</p>
            <a-select
              v-model:value="state.valueType"
              :options="state.floatOptions"
              :style="{width: '120px'}">
            </a-select>
          </div>
          <div>
            <p>{{ $t('处理方式') }}</p>
            <a-select
              v-model:value="state.method"
              :style="{width: '120px'}"
            >
              <a-select-option value="sum">{{ $t('修改总和') }}</a-select-option>
              <a-select-option value="ave">{{ $t('统一赋值') }}</a-select-option>
            </a-select>
          </div>
          <a-button
            type="primary"
            :style="{width: '80px',color:'#fff',marginLeft: '15px',marginTop: '22px'}"
            @click="handleSearch"
          >{{ $t('查询') }}</a-button>

        </div>
        <div class="agClass">
          <AgGrid ref="agGridRef" :isEdit="'itemized'" :istree="state.istree"></AgGrid>
        </div>
        <div class="save_btn_box">
          <a-button @click="handleOk" type="primary" :style="{width: '80px',color:'#fff',marginLeft: '15px'}">{{ $t('修改') }}</a-button>
          <a-button @click="closeModal" :style="{width: '80px',marginLeft: '15px'}">{{ $t('取消') }}</a-button>
        </div>
      </div>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { ref, reactive, toRef, defineProps, defineEmits, nextTick } from 'vue'
import message from '@/utils/message'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import { t } from '@/utils/common.js'

const props = defineProps(['visible', 'sourceType'])
const visible = toRef(props, 'visible')
const emits = defineEmits(['cancel', 'confirm'])

const route = useRoute()

// AG-Grid表格
const agGridRef = ref(null)

const state = reactive({
	type1: '',
	type2: '',
	type3: '',
	valueType: '',
	method: '',
	options1: [],
	options2: [],
	groupOptions: [],
	floatOptions: [],
	groupColumns: [],
	treeNode: '',
	istree: true,
	id: '',
	routeId: '',
	timeseriesName: '',
	timeseriesType: '',
	timeseriesScene: '',
	timeseriesDataType: '',
	timeseriesDisplay: 'day', // 时序显示形式
	mod_24_col_data: [],
	mod_single_col_data: []
})

const changeType1 = (val) => {
	state.options1 = state.groupOptions.filter(item => {
		return item.value !== val
	})
	state.options2 = state.groupOptions.filter(item => {
		return item.value !== val && item.value !== state.type2
	})
	if (val == state.type2) {
		state.type2 = ''
		state.type3 = ''
	} else if (val == state.type3) {
		state.type3 = ''
	}
}

const changeType2 = (val) => {
	state.options2 = state.groupOptions.filter(item => {
		return item.value !== val && item.value !== state.type1
	})
	if (val == state.type3 || !val) {
		state.type3 = ''
	}
}

const handleSearch = () => {
	if (!state.type1 || !state.method || !state.valueType) {
		return message.warning(t('请选择查询条件') + '!')
	}
	agGridRef.value.setBlanceData({
		columns: [],
		data: []
	})
	state.groupColumns = state.type3 ? [state.type1, state.type2, state.type3] : state.type2 ? [state.type1, state.type2] : [state.type1]
	state.istree = !!(state.type3 || state.type2)
	basicApi({
		'import_string_func': 'teapcase:group_relation_bus_data',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': state.treeNode,
			'group_columns': state.groupColumns,
			'group_type': state.method,
			'target_column': state.valueType
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			const { columns, data } = res.func_result
			columns.push({ cellDataType: 'number', field: 'edit_col', headerName: t('目标值') })
			data.forEach(item => {
				item.edit_col = item[state.valueType]
			})
			const tableData = {
				columns,
				data
			}

			nextTick(() => {
				agGridRef.value.setBlanceData(tableData)
			})
		}
	})
}

const closeModal = () => {
	emits('cancel')
}

const refreshBalanceGoals = () => {
	emits('confirm')
}
Mitt.on('refreshBalanceGoals', refreshBalanceGoals)
const getBalanceGoalsData = (treeNode) => {
	state.treeNode = treeNode
	basicApi({
		'import_string_func': 'teapcase:get_relation_bus_column_info',
		'func_arg_dict': {
			// 'file_name': route.query.filePath,
			'sheet_name': treeNode
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			const { data } = res.func_result
			state.groupOptions = data.group_columns
			state.floatOptions = data.float_columns
		}
	})
}

const handleOk = () => {
	agGridRef.value.saveBalanceGoals(state.treeNode, state.groupColumns, state.method, state.valueType)
}
defineExpose({ getBalanceGoalsData })
</script>
<style lang="scss">
  .modal_balanceGoals{
    .ant-modal{
      width: 80%!important;
      .ant-modal-body{
        >div{
          .modal_content{
            // height: 540px;
            padding: 0px 30px 20px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            .btn_box {
              position: absolute;
              top: 3px;
              left: 30px;
              z-index: 33;
              text-align: left;
              display: flex;
              >div {
                margin-left: 15px
              }
              >div:first-child {
                margin-left: 0
              }
            }
          }
          .agClass {
            width: 100%;
            height: 480px;
            margin-top: 30px;
          }
          .save_btn_box {
            margin-top: 15px;
            text-align: right;
          }
        }
      }
    }
  }
</style>
