<template>
  <a-modal wrapClassName="DialogBox" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <a-spin :spinning="state.spinning">
      <div class="user-select">
          <div class="modal_top">
              <p>{{ props.title }}</p>
              <close-outlined v-if="props.type==1" class="pointer" @click="emit('close')" />
          </div>
          <div class="DialogBox_content relative">
              <ExclamationCircleTwoTone />
              <p>{{ props.text}}</p>
              <div v-if="props.type==2||props.type==4">
                <div v-html="props.logStr"></div>
              </div>
          </div>
          <div class="modal_btn" v-if="props.type==1">
            <a-button @click="confirm(false)">{{$t('创建副本')}}</a-button>
            <a-button @click="confirm(true)" type="primary">{{$t('覆盖')}}</a-button>
            <a-button @click="confirm('skip')">{{$t('跳过')}}</a-button>
            <a-button @click="emit('close')">{{$t('取消')}}</a-button>
          </div>
          <div class="modal_btn" v-if="props.type==2">
            <a-button @click="confirm(true)">{{$t('下载更新')}}</a-button>
            <a-button @click="confirm(false)" type="primary">{{$t('忽略此版本')}}</a-button>
            <a-button @click="emit('close')">{{$t('稍后更新')}}</a-button>
          </div>
          <div class="modal_btn" v-if="props.type==3">
            <a-button @click="confirm">{{$t('安装更新')}}</a-button>
            <a-button @click="emit('close')">{{$t('稍后安装')}}</a-button>
          </div>
          <div class="modal_btn" v-if="props.type==4">
            <a-button @click="emit('close')">{{$t('确定')}}</a-button>
          </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive } from 'vue'
import { CloseOutlined, ExclamationCircleTwoTone } from '@ant-design/icons-vue'
import { InsertTsFile } from '@/api/index'
import message from '@/utils/message'
const state = reactive({
	ifShow: true,
	spinning: false
})
const props = defineProps({
	data: {
		type: Object,
		default: () => {}
	},
	logStr: {
		type: String,
		default: ''
	},
	text: {
		type: String,
		default: ''
	},
	type: {
		type: Number,
		default: 1
	},
	title: {
		type: String,
		default: ''
	}
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}
const confirm = (val) => {
	state.spinning = true
	if (props.type == 1) {
		InsertTsFile({
			case_file_name: props.data.case_file_name,
			index_list: props.data.selectedRowKeys,
			file_path: props.data.file_path,
			overwrite_flag: val == 'skip' ? null : val,
			skip_flag: val == 'skip' ? true : null
		}).then(res => {
			if (res.code == 1) {
				message.success(res.message)
				emit('close', true)
			}
			state.spinning = false
		}).catch(() => {
			state.spinning = false
		})
	} else {
		emit('confirm', val)
	}
}
onMounted(() => {

})
</script>
<style lang="scss">
  .DialogBox{
    .ant-modal{
      width: 600px!important;
      .ant-modal-body{
        >div{
            .modal_top {
                height: 50px;
            }
            .DialogBox_content{
                display: flex;
                align-items: center;
                flex-direction: column;
                >span{
                    font-size: 100px;
                    margin: 50px 0;
                }
                >div{
                  width: 90%;
                  margin-top: 20px;
                  padding: 0px 10px 0px 15px;
                  border: 1px solid #C1E0FC;
                  height: 400px;
                  overflow-y: overlay;
                  >div{
                    font-size: 16px;
                    line-height: 28px;
                    white-space:  pre-line;
                  }
                }
                >p{
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing:2px;
                }
            }
            .modal_btn{
                position: relative;
                width: 100%;
                justify-content: space-around;
                display: flex;
                right: unset;
                bottom: unset;
                padding: 30px 10px 20px;
                button{
                  margin-left: 0px;
                }
                button:first-child{
                    border-color: var(--base-color);
                    color: var(--base-color);
                    background-color: #C1E0FC;
                }
            }
        }
      }
    }
  }
</style>

