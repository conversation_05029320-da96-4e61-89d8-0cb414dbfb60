<!-- 协同规划 -->
<template>
    <div class="global-main-plan">
        <div class="main-left">
            <p>{{ $t('参数设置') }}</p>
            <div :class="['parameterSet']">
                <div>
                    <p>{{ $t('旋转备用率') }}</p>
                    <a-input-number v-model:value="stateBase.load_resv_up_coe" :min="0" addon-after="%"></a-input-number>
                </div>
                <div>
                    <p>{{ $t('停机备用率') }}</p>
                    <a-input-number v-model:value="stateBase.emg_resv_up_coe" :min="0" addon-after="%"></a-input-number>
                </div>
            </div>
            <p>{{ $t('仿真设置') }}</p>
            <div :class="['solutionMethod']">
                <div class="solutionMethod-top">
                    <div>
                        <p>{{ $t('仿真时间') }}</p>
                        <a-range-picker v-model:value="state.simulation_long_time" valueFormat="YYYY-MM-DD" @change="longTimechange"/>
                    </div>
                    <div>
                        <p>{{ $t('仿真步长') }}</p>
                        <a-select
                        ref="select"
                        v-model:value="stateMore.simulation.sim_freq"
                        :style="{width: '100%'}"
                        >
                            <a-select-option value="M" disabled>{{ $t('分钟') }}</a-select-option>
                            <a-select-option value="H">{{ $t('小时') }}</a-select-option>
                            <a-select-option value="D" disabled>{{ $t('天') }}</a-select-option>
                        </a-select>
                    </div>
                </div>
                <div  class="solutionMethod-bottom">
                    <div>
                        <p>{{ $t('场景选择') }}</p>
                        <a-select
                        v-model:value="stateCase.scenario_selected"
                        :style="{width: '100%'}"
                        :options="stateBase.scenariOptions"
                        @change="handleChange"
                        ></a-select>
                    </div>
                    <div>
                        <p>{{ $t('水电场景') }}</p>
                        <a-select
                            v-model:value="stateCase.hydropower_scenario"
                            style="width: 100%"
                            :options="state.hydropowerOptions"
                            :disabled="state.hydropowerOptions.length <= 0"
                        >
                        </a-select>
                    </div>
                </div>
            </div>
            <p>{{ $t('时序削减方式') }}</p>
            <div :class="['setContent-balance-reduce']">
                <div class="setContent-balance-way">
                    <div>
                        <p>{{ $t('求解方法') }}</p>
                        <a-select
                            ref="select"
                            v-model:value="state.solution_method_long"
                            style="width: 92%"
                        >
                        <a-select-option value="partial">{{ $t('负荷持续时间曲线分段法') }}</a-select-option>
                        <a-select-option disabled value="fitted">{{ $t('变步长时序聚合法') }}</a-select-option>
                        </a-select>
                    </div>
                    <div v-show="state.solution_method_long == 'partial'">
                        <p>{{ $t('是否分区平衡') }}</p>
                        <a-radio-group v-model:value="stateMore.simulation.separate_zone" button-style="solid">
                        <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                        <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                        </a-radio-group>
                    </div>
                    <div v-show="state.solution_method_long == 'fitted'">
                        <p>{{ $t('是否分区平衡') }}</p>
                        <a-radio-group v-model:value="stateMore.fitted.separate_zone" button-style="solid">
                        <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                        <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                        </a-radio-group>
                    </div>
                </div>
                <div class="setContent-balance-content">
                    <div v-show="state.solution_method_long == 'partial'">
                        <p>{{ $t('周期颗粒度') }}</p>
                        <a-select
                            ref="select"
                            v-model:value="stateMore.partial.sample_period"
                            style="width: 92%"
                        >
                        <a-select-option value="W">{{ $t('周度') }}</a-select-option>
                        <a-select-option value="M">{{ $t('月度') }}</a-select-option>
                        <a-select-option value="S">{{ $t('季度') }}</a-select-option>
                        <a-select-option value="Y">{{ $t('年度') }}</a-select-option>
                        </a-select>
                    </div>
                    <div v-show="state.solution_method_long == 'partial'">
                        <p>{{ $t('周期内分段数') }}</p>
                        <a-input v-model:value="stateMore.partial.block_num" style="width: 92%" :placeholder="$t('请输入')" />
                    </div>
                    <div v-show="state.solution_method_long == 'partial'">
                        <p>{{ $t('计算前自动安排检修') }}</p>
                        <a-radio-group v-model:value="stateMore.simulation.arrange_maintenance" button-style="solid">
                        <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                        <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                        </a-radio-group>
                    </div>
                    <div v-show="state.solution_method_long == 'fitted'">
                        <p>{{ $t('近似分段数量') }}</p>
                        <a-input v-model:value="stateMore.fitted.block_num" style="width: 92%" :placeholder="$t('请输入')" />
                    </div>
                    <div v-show="state.solution_method_long == 'fitted'">
                        <p>{{ $t('计算时间上限') }}（s）</p>
                        <a-input v-model:value="stateMore.fitted.max_solving_time" style="width: 92%" :placeholder="$t('请输入')" />
                    </div>
                </div>
            </div>
            <p>{{ $t('案例备注') }}</p>
            <div :class="['remarkBox1']">
                <a-textarea v-model:value="stateCase.description" :placeholder="$t('请输入')+'...'" :rows="4" />
            </div>
        </div>
        <div class="main-right">
            <p @click="state.longTermShow = !state.longTermShow">{{ $t('专业参数设置') }}<CaretRightOutlined v-show="!state.longTermShow"/><CaretDownOutlined v-show="state.longTermShow"/></p>
            <div class="setContent-set-balance-long">
                <div v-show="state.longTermShow"
                :class="['setContent-set-balance']"
                >
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('负荷松弛惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.load_relax_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.load_relax_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}
                                <a-input-number
                                    :disabled="stateAuto.load_relax_cost_auto"
                                    :min="0"
                                    v-model:value="stateBase.load_relax_cost"
                                    :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                    :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                                />
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('最大缺电功率惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.load_dec_max_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.load_dec_max_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.load_dec_max_cost_auto" :min="0" v-model:value="stateBase.load_dec_max_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('备用松弛惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.reserve_relax_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.reserve_relax_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.reserve_relax_cost_auto" :min="0" v-model:value="stateBase.reserve_relax_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('最大备用松弛惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.reserve_dec_max_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.reserve_dec_max_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.reserve_dec_max_cost_auto" :min="0" v-model:value="stateBase.reserve_dec_max_cost" :formatter="value => value=='AUTO'?'':`${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('弃风惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.wind_curtailment_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.wind_curtailment_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.wind_curtailment_cost_auto" :min="0" v-model:value="stateBase.wind_curtailment_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('弃光惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.solar_curtailment_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.solar_curtailment_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.solar_curtailment_cost_auto" :min="0" v-model:value="stateBase.solar_curtailment_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('弃水惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.water_curtailment_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.water_curtailment_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.water_curtailment_cost_auto" :min="0" v-model:value="stateBase.water_curtailment_cost" :formatter="value =>value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('支路过载惩罚(元/MWh)') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.branch_relax_cost).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.branch_relax_cost_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}<a-input-number :disabled="stateAuto.branch_relax_cost_auto" :min="0" v-model:value="stateBase.branch_relax_cost" :formatter="value =>  value=='AUTO'?'': `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('停备松弛惩罚为旋备的倍数') }}</p>
                            <div class="defaultVal">{{ parseFloat(stateDefault.general_parameters.coe_of_emerg_up_resv_relax_pen).toLocaleString() }}</div>
                            <a-radio-group v-model:value="stateAuto.coe_of_emerg_up_resv_relax_pen_auto">
                                <a-radio :value="true">{{ $t('自动设置') }}</a-radio>
                                <a-radio :value="false">{{ $t('指定值') }}
                                <a-input-number :disabled="stateAuto.coe_of_emerg_up_resv_relax_pen_auto" :min="0" v-model:value="stateBase.coe_of_emerg_up_resv_relax_pen" :formatter="value => value=='AUTO'?'':`${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" :parser="value => value.replace(/\$\s?|(,*)/g, '')" />
                                </a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('储能充放状态二次优化') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.reoptimize_es_state" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('电池充放电状态统一') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.same_battery_state" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('抽蓄抽水出力状态统一') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.same_pump_state" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('抽蓄、电池周期首末时刻SOC一致') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.same_soc_period_ini_end_pump_battery" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div v-show="state.solution_method_long == 'partial'">
                            <p>{{ $t('储能聚合') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.group_energy_storage" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div v-show="state.solution_method_long == 'fitted'">
                            <p>{{ $t('储能聚合') }}</p>
                            <a-radio-group v-model:value="stateMore.fitted.group_energy_storage" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('需求侧响应是否影响备用需求') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.reserve_needed_impacted_by_demand_response" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div>
                            <p>{{ $t('储能按容量百分比投运') }}</p>
                            <a-radio-group v-model:value="stateMore.simulation.plan_storage_by_capacity_percentage" button-style="solid">
                                <a-radio-button :value="true">{{ $t('是') }}</a-radio-button>
                                <a-radio-button :value="false">{{ $t('否') }}</a-radio-button>
                            </a-radio-group>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('待规划设备最短投产时间') }}</p>
                            <a-select v-model:value="stateMore.simulation.plan_min_install_duration" style="width: 88%">
                                <a-select-option value="period">{{ $t('周期颗粒度') }}</a-select-option>
                                <a-select-option value="quarter">{{ $t('季度') }}</a-select-option>
                                <a-select-option value="halfyear">{{ $t('半年') }}</a-select-option>
                                <a-select-option value="year">{{ $t('年') }}</a-select-option>
                            </a-select>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('计算时间上限') }}（s）</p>
                            <a-input v-model:value="stateMore.simulation.max_solving_time" style="width: 92%" :placeholder="$t('请输入')" />
                        </div>
                        <div>
                            <p>{{ $t('新能源最低消纳率(<=1)') }}</p>
                            <a-input-number
                                v-model:value="stateMore.simulation.renewable_accommodation_rate"
                                :min="0"
                                :max="1"
                                :step="0.1"
                            />
                            </div>
                        <div>
                        <p>Partial MIP Gap(%)</p>
                        <a-select
                            v-model:value="stateMore.simulation.mipgap"
                            :placeholder="$t('请选择')"
                            style="width: 92%"
                            :options="state.gap_long_options"
                        >
                            <template #dropdownRender="{ menuNode: menu }">
                            <v-nodes :vnodes="menu" />
                            <a-divider style="margin: 4px 0" />
                            <a-space style="padding: 2px 4px">
                                <a-input ref="inputLongRef" v-model:value="state.gapLongValue" :placeholder="$t('请输入')" />
                                <a-button type="text" @click="addLongGapItem">
                                <template #icon>
                                    <plus-outlined />
                                </template>
                                {{ $t('添加') }}
                                </a-button>
                            </a-space>
                            </template>
                        </a-select>
                        </div>
                        <div>
                        <p>Full MIP Gap(%)</p>
                        <a-select
                            v-model:value="stateMore.simulation.adjust_mipgap"
                            :placeholder="$t('请选择')"
                            style="width: 92%"
                            :options="state.gap_long_adjust_options"
                        >
                            <template #dropdownRender="{ menuNode: menu }">
                            <v-nodes :vnodes="menu" />
                            <a-divider style="margin: 4px 0" />
                            <a-space style="padding: 2px 4px">
                                <a-input ref="inputLongAdjustRef" v-model:value="state.gapLongAdjustValue" :placeholder="$t('请输入')" />
                                <a-button type="text" @click="addLongAdjustGapItem">
                                <template #icon>
                                    <plus-outlined />
                                </template>
                                {{ $t('添加') }}
                                </a-button>
                            </a-space>
                            </template>
                        </a-select>
                        </div>
                    </div>
                    <div class="advancedSet_line">
                        <div class="setting_bottom_input">
                            <p>{{ $t('储能提供备用的模式') }}</p>
                            <a-select v-model:value="stateMore.simulation.storage_reserve_mode" style="width: 98%">
                                <a-select-option value="no_reserve">{{ $t('储能不提供备用') }}</a-select-option>
                                <a-select-option value="upper_limit_soc">{{ $t('以时刻SOC为上限提供备用') }}</a-select-option>
                                <a-select-option value="upper_limit_min_weekly_soc">{{ $t('以周内最低SOC为上限提供备用') }}</a-select-option>
                            </a-select>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('水电提供备用模式') }}</p>
                            <a-select v-model:value="stateMore.simulation.hydro_curt_as_reserve" style="width: 98%">
                                <a-select-option :value="true">{{ $t('水电弃水时提供备用') }}</a-select-option>
                                <a-select-option :value="false">{{ $t('水电开机时即可提供备用') }}</a-select-option>
                            </a-select>
                        </div>
                        <div class="setting_bottom_input">
                            <p>{{ $t('停备需求计算模式') }}</p>
                            <a-select v-model:value="stateMore.simulation.emer_reserve_mode" style="width: 98%">
                                <a-select-option value="period_max_load">{{ $t('基于周期内最大负荷') }}</a-select-option>
                                <a-select-option value="current_load">{{ $t('基于当前时刻负荷') }}</a-select-option>
                            </a-select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="main-btn">
                <a-button style="width: 96px" @click="handleReset">{{ $t('重置') }}</a-button>
                <a-button :style="{'width': '96px', 'backgroundColor': '#dbe9f4'}" @click="confirm(false)">{{ $t('仅保存') }}</a-button>
                <a-button type="primary" style="width: 96px" @click="confirm(true)">{{ $t('计算') }}</a-button>
            </div>
        </div>
    </div>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { onMounted, reactive, ref, watch, defineComponent } from 'vue'
import message from '@/utils/message'
import { t } from '@/utils/common.js'
const props = defineProps({
	stateDefault: {
		type: Object
	},
	stateSetting: {
		type: Object
	},
	stateAuto: {
		type: Object
	},
	stateBase: {
		type: Object
	},
	state: {
		type: Object
	},
	isReady: {
		type: Boolean
	}
})
const VNodes = defineComponent({
	props: {
		vnodes: {
			type: Object,
			required: true
		}
	},
	render() {
		return this.vnodes
	}
})
const state = reactive({
	longTermShow: true,
	simulation_long_time: props.state.simulation_long_time,
	hydropowerOptions: props.state.hydropowerOptions,
	solution_method_long: props.state.solution_method_long,
	gapLongAdjustValue: null,
	gapLongValue: null,
	gap_long_options: [
		{ label: t('快速粗略') + '（10%）', value: '10' },
		{ label: t('一般居中') + '（5%）', value: '5' },
		{ label: t('慢速精确') + '（0.5%）', value: '0.5' }
	],
	gap_long_adjust_options: [
		{ label: t('快速粗略') + '（10%）', value: '10' },
		{ label: t('一般居中') + '（5%）', value: '5' },
		{ label: t('慢速精确') + '（0.5%）', value: '0.5' }
	]
})
const stateDefault = reactive({
	general_parameters: {

	}
})
const stateCase = ref({

})
const stateBase = ref({

})
const stateAuto = ref({

})
const stateMore = ref({
	simulation: {

	},
	fitted: {

	},
	partial: {

	}
})
const inputLongRef = ref()
const inputLongAdjustRef = ref()
const emit = defineEmits(['reset', 'saveSetting'])
const handleChange = val => {
	Mitt.emit('changeTree', val)
}
// 规划协同 仿真时间
const longTimechange = (val) => {
	stateMore.value.simulation.start_datetime = `${val[0]} 00:00:00`
	stateMore.value.simulation.end_datetime = `${val[1]} 23:00:00`
}
const addLongGapItem = e => {
	e.preventDefault()
	if (isNaN(state.gapLongValue - parseFloat(state.gapLongValue))) return message.warning(t('请输入数字'))
	state.gap_long_options.push({ label: state.gapLongValue, value: state.gapLongValue })
	state.gapLongValue = ''
	setTimeout(() => {
		inputLongRef.value?.focus()
	}, 0)
}

const addLongAdjustGapItem = e => {
	e.preventDefault()
	if (isNaN(state.gapLongAdjustValue - parseFloat(state.gapLongAdjustValue))) return message.warning(t('请输入数字'))
	state.gap_long_adjust_options.push({ label: state.gapLongAdjustValue, value: state.gapLongAdjustValue })
	state.gapLongAdjustValue = ''
	setTimeout(() => {
		inputLongAdjustRef.value?.focus()
	}, 0)
}
onMounted(() => {

})
const handleReset = () => {
	emit('reset')
}
const confirm = (val) => {
	emit('saveSetting', val ? 5 : false, 'long_term', {
		stateCase: stateCase.value,
		stateBase: stateBase.value,
		stateAuto: stateAuto.value,
		stateMore: stateMore.value
	})
}
watch(() => props.isReady, (val) => {
	stateAuto.value = props.stateAuto
	stateBase.value = props.stateBase
	stateCase.value = props.stateSetting.case_info
	stateDefault.general_parameters = props.stateDefault.general_parameters
	stateMore.value = props.stateSetting.long_term
	state.simulation_long_time = props.state.simulation_long_time
	state.hydropowerOptions = props.state.hydropowerOptions
	state.solution_method_long = props.state.solution_method_long
}, { immediate: true })
</script>
<style lang="scss">

</style>
