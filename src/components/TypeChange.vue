<!-- 类型转换 -->
 <template>
    <a-modal wrapClassName="modal_type_change" :afterClose="closeModal" :centered="true"  v-model:open="state.visible" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('类型转换') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <a-spin :spinning="state.loading" size="large">
                <div class="modal_content">
                    <div>
                        <a-select v-model:value="state.type" :options="state.options" style="width: 100%" :placeholder="$t('请选择转换类型')">

                        </a-select>
                    </div>
                    <div class="modal_btn">
                        <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
                        <a-button :disabled="!state.type" @click="confirm" type="primary">{{ $t('确认') }}</a-button>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { CloseOutlined } from '@ant-design/icons-vue'
const route = useRoute()
const props = defineProps({
	data: {
		type: Array,
		default: () => []
	}
})
const state = reactive({
	routePath: route.fullPath,
	visible: true,
	loading: false,
	type: undefined,
	options: props.data
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}
const confirm = () => {
	emit('confirm', state.type)
}
onMounted(() => {

})
</script>
<style lang="scss">
    .modal_type_change{
        .ant-modal{
            width: auto!important;
            .ant-modal-body{
                >div{
                    .modal_content{
                        padding: 30px 30px 90px;
                        width: 500px;
                    }
                    .modal_btn{
                        button:last-child{
                            color: #fff;
                        }
                        .ant-btn[disabled]{
                            color: #000;
                        }
                    }
                }
            }
        }
  }
</style>

