<!-- 拓扑匹配 -->
<template>
    <a-modal wrapClassName="GisModal" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
                <div>
                    <div class="modal_top">
                        <p>{{ $t('拓扑匹配') }}</p>
                        <close-outlined class="pointer" @click="closeModal" />
                    </div>
                    <div class="modal-content">
                        <div>
                            <div>
                                <p>{{ $t('文件选择') }}</p>
                                <div class="upload_input">
                                    <a-upload
                                        v-model:file-list="state.fileList"
                                        :beforeUpload="()=>false"
                                        :showUploadList="false"
                                        accept=".tg"
                                        name="file"
                                        :maxCount="1"
                                        @change="changeFile"
                                    >
                                        <div class="upload relative">
                                            <div class="file_p">
                                                <p>{{ state.fileList[0]?state.fileList[0].name:' ' }}<DeleteOutlined v-if="state.fileList.length!=0" @click.stop="deleteFile" /></p>
                                            </div>
                                            <a-button class="upload_btn" type="primary">
                                                <template #icon><folder-open-filled /></template>
                                                {{ $t('打开') }}</a-button>
                                        </div>
                                    </a-upload>
                                </div>
                            </div>
                            <div class="list_content">
                                <p>{{ $t('历史选择') }}</p>
                                <div :class="state.filePath?'list_disabled':''">
                                    <p @click="selectTg(item)" :class="state.selectTg==item.file_path?'active':''" v-for="(item) in state.historyList" :key="item.file_path">{{ item.file_name }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal_btn">
                            <a-button :disabled="!state.filePath&&!state.selectTg" @click="confirm" type="primary">{{ $t('确认') }}</a-button>
                            <a-button @click="closeModal">{{ $t('取消') }}</a-button>
                        </div>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
 </template>
<script setup>
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { } from '@/api/index'
import { UploadFile, GetTrRelationTgFiles } from '@/api/gis'
import { parseFilePath } from '@/utils/gis'
const route = useRoute()
const state = reactive({
	ifShow: true,
	loading: false,
	fileList: [],
	historyList: [],
	filePath: undefined,
	selectTg: undefined
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}
const changeFile = ({ file, fileList }) => {
	state.loading = true
	const formData = new FormData()
	formData.append('file', file)
	UploadFile({}, formData).then(res => {
		state.loading = false
		if (res.code == 1) {
			state.filePath = res.file_path
		}
	}).catch(() => {
		state.loading = false
	})
}
const deleteFile = () => {
	state.fileList = []
	state.filePath = undefined
}
const selectTg = (item) => {
	if (state.selectTg == item.file_path) {
		state.selectTg = undefined
	} else {
		state.selectTg = item.file_path
	}
}
const confirm = () => {
	emit('confirm', state.filePath || state.selectTg)
}
onMounted(async() => {
	GetTrRelationTgFiles({
		'result_file_path': route.query.filePath
	}).then(res => {
		state.historyList = res.data.map(item => {
			return {
				file_name: parseFilePath(item).file_name,
				file_path: item
			}
		})
	})
})
</script>
<style lang="scss">

</style>
