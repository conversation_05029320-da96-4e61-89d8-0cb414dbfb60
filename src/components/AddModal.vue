<template>
    <a-modal wrapClassName="AddSimpleModal" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
            <div>
                <div class="modal_top">
                    <p>{{(props.isAdd?$t('新建'):$t('修改'))+(props.type=='bus'?$t('节点'):props.type=='ac_line'?$t('线路'):$t('变压器'))}}</p>
                    <close-outlined class="pointer" @click="emit('close')" />
                </div>
                <div class="modal-content">
                    <div class="form_content">
                        <a-form
                            ref="formRef"
                            :model="formState"
                        >
                            <div class="grid">
                                <a-form-item
                                    v-for="(item) in props.columns"
                                    :key="item.field"
                                    :rules="[{ required:true, message: $t('请输入')+item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,'') }]"
                                    :label="item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,'')"
                                    :name="item.field"
                                >
                                    <a-input-number :disabled="item.field=='length_km'" v-if="item.cellDataType=='number'&&!item.col_source" :min="0" :controls="false"  v-model:value="formState[item.field]" >
                                        <template #addonAfter v-if="!(item.headerName.includes($t('率'))&&!item.headerName.includes($t('功率')))">
                                            <span>{{ getUnit(item.headerName) }}</span>
                                        </template>
                                    </a-input-number>
                                    <a-radio-group v-else-if="item.cellDataType=='boolean'" v-model:value="formState[item.field]" name="radioGroup">
                                        <a-radio :value="true">{{$t('是')}}</a-radio>
                                        <a-radio :value="false">{{$t('否')}}</a-radio>
                                    </a-radio-group>
                                    <a-select v-else-if="item.field=='from_bus'" v-model:value="formState[item.field]" :options="item.options">
                                    </a-select>
                                    <a-select  v-else-if="item.field=='to_bus'" v-model:value="formState[item.field]" :options="item.options">
                                    </a-select>
                                    <a-select v-else-if="item.field=='hv_bus'" show-search :filter-option="filterOption" v-model:value="formState[item.field]" :options="handleOption(state.busOption1,'high')" @change="change_bus('hv_bus')">
                                    </a-select>
                                    <a-select v-else-if="item.field=='lv_bus'" show-search :filter-option="filterOption" v-model:value="formState[item.field]" :options="handleOption(state.busOption2,'low')" @change="change_bus('lv_bus')">
                                    </a-select>
                                    <div v-else-if="item.col_source">
                                        <a-select  v-model:value="formState[item.field]" :options="state.relationOption[item.col_source]||[]">
                                        </a-select>
                                        <a-button v-if="item.field!='timeseries'" type="primary" class="add_btn" shape="circle" :icon="h(PlusOutlined)" @click="addRelation(item.col_source,item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,''))" />
                                    </div>
                                    <a-input disabled v-else-if="item.field=='type'" v-model:value="state.typeName"/>
                                    <a-input v-else-if="item.cellDataType=='text'" v-model:value="formState[item.field]"/>
                                </a-form-item>
                            </div>
                            <a-form-item
                                :label="$t('线路组成')"
                                v-if="props.type=='ac_line'"
                            >
                            <div class="line_type_data">
                                <div v-for="(item, index) in formState.line_data_detail" :key="index">
                                    <div>
                                        <p>{{$t('线路型号')}}</p>
                                        <a-select :placeholder="$t('请选择线路型号')" v-model:value="item.line_model_id" :options="state.lineTypeOptions">

                                        </a-select>
                                    </div>
                                    <div>
                                        <p>{{$t('线路长度')}}</p>
                                        <a-input-number :min="0" :controls="false"  v-model:value="item.length_km" >
                                            <template #addonAfter>
                                                <span>km</span>
                                            </template>
                                        </a-input-number>
                                    </div>
                                    <MinusCircleFilled v-if="formState.line_data_detail.length>1" class="pointer" @click="deleteLines(index)" />
                                </div>
                            </div>
                            <div class="flex">
                                <span class="add_lines" @click="addLines"><PlusCircleFilled />{{$t('添加型号')}}</span>
                                <span class="add_lines" @click="state.lineTypeShow=true"><MenuOutlined />{{$t('型号管理')}}</span>
                            </div>
                            </a-form-item>
                            <div>
                                <p class="text_remark">{{$t('注：其它数据请前往编辑器或生成器完善')}}</p>
                            </div>
                        </a-form>
                    </div>
                    <div class="modal_btn">
                        <a-button @click="confirm" type="primary">{{$t('确认')}}</a-button>
                        <a-button @click="emit('close')">{{$t('取消')}}</a-button>
                    </div>
                </div>
            </div>
            </a-spin>
        </screen-scale>
        <line-manage v-if="state.lineTypeShow" @close="reloadLineData"></line-manage>
        <add-simple-modal @confirm="confirmAddRelation" v-if="state.addModalShow" @close="state.addModalShow=false" :type="state.addModalType" :title="state.addModalTitle" :columns="state.addModalColumn"></add-simple-modal>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive, ref, h, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
import { deepClone, getUnit } from '@/utils/gis'
import { t } from '@/utils/common'
const props = defineProps({
	columns: {
		type: Array
	},
	data: {
		type: Object
	},
	relationColumn: {
		type: Object
	},
	relationOption: {
		type: Object
	},
	busOption: {
		type: Array
	},
	bus_data: {
		type: Array
	},
	type: {
		type: String
	},
	isAdd: {
		type: Boolean,
		default: true
	}
})
const route = useRoute()
const state = reactive({
	ifShow: true,
	loading: false,
	formData: [],
	relationColumn: props.relationColumn,
	relationOption: props.relationOption,
	lineTypeShow: false,
	addModalShow: false,
	addModalType: undefined,
	addModalTitle: undefined,
	addModalColumn: undefined,
	busOption1: props.busOption.map(item => {
		return {
			label: item.name,
			value: item.index,
			vn_kv: item.vn_kv
		}
	}),
	busOption2: props.busOption.map(item => {
		return {
			label: item.name,
			value: item.index,
			vn_kv: item.vn_kv
		}
	}),
	lineTypeOptions: [

	]
})
const formRef = ref()
const formState = reactive({

})
const emit = defineEmits(['close', 'confirm'])
const handleOption = (option, type) => {
	return option.map(item => {
		return {
			label: item.vn_kv + 'kV ' + item.label,
			value: item.value,
			vn_kv: item.vn_kv
		}
	}).sort((a, b) => type === 'low' ? (a.vn_kv - b.vn_kv) : (b.vn_kv - a.vn_kv))
}
const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const addRelation = (type, title) => {
	state.addModalType = type
	state.addModalTitle = title
	state.addModalColumn = state.relationColumn[type].filter(item => item.required)
	state.addModalShow = true
}
const confirmAddRelation = (item) => {
	state.addModalShow = false
	state.relationOption[state.addModalType].push(item)
}
const change_bus = (type) => {
	const isHvBus = type === 'hv_bus'
	let option
	const vn_kv = props.busOption.find(item => {
		if (isHvBus) {
			return item.index === formState.hv_bus
		} else {
			return item.index === formState.lv_bus
		}
	}).vn_kv
	if (props.bus_data.find(item => {
		if (isHvBus) {
			return item.index === formState.hv_bus
		} else {
			return item.index === formState.lv_bus
		}
	})) {
		option = props.busOption.filter(item => {
			if (isHvBus) {
				return item.vn_kv <= vn_kv && item.index !== formState.hv_bus
			} else {
				return item.vn_kv >= vn_kv && item.index !== formState.lv_bus
			}
		}).map(items => {
			return {
				label: items.name,
				value: items.index,
				vn_kv: items.vn_kv
			}
		})
	} else {
		option = props.bus_data.filter(item => {
			if (isHvBus) {
				return item.vn_kv <= vn_kv
			} else {
				return item.vn_kv >= vn_kv
			}
		}).map(items => {
			return {
				label: items.name,
				value: items.index,
				vn_kv: items.vn_kv
			}
		})
	}
	if (isHvBus) {
		if (!option.find(item => item.value === formState.lv_bus)) {
			formState.lv_bus = undefined
		}
		state.busOption2 = option
	} else {
		if (!option.find(item => item.value === formState.hv_bus)) {
			formState.hv_bus = undefined
		}
		state.busOption1 = option
	}
}
const confirm = () => {
	state.loading = true
	formRef.value.validate()
		.then(() => {
			if (props.type == 'bus' || props.type == 'trafo') {
				state.loading = false
			    emit('confirm', formState)
			} else if (props.type == 'ac_line') {
				if (formState.line_data_detail.find(item => item.line_model_id == undefined || item.length_km == 0)) {
					state.loading = false
					message.warning(t('请选择线路型号或者输入线路长度'))
					return
				}
				state.loading = false
				formState.from_bus_name = props.columns.find(item => item.field == 'from_bus').options.find(item => item.value == formState.from_bus).label
				formState.to_bus_name = props.columns.find(item => item.field == 'to_bus').options.find(item => item.value == formState.to_bus).label
				emit('confirm', formState)
			}
		})
		.catch(error => {
			state.loading = false
			console.log('error', error)
		})
}
const addLines = () => {
	formState.line_data_detail.push({
		length_km: 0,
		line_model_id: undefined
	})
}
const deleteLines = (index) => {
	formState.line_data_detail.splice(index, 1)
}
const closeModal = () => {
	emit('close')
}
const getLineData = (type) => {
	basicApi({
		'import_string_func': 'teapgis:list_all_line_model',
		'func_arg_dict': {
			'tc_file_path': route.query.filePath
		}
	}).then(res => {
		if (type == 'init') state.loading = false
		if (res.code == 1 && res.func_result.code == 1) {
			state.lineTypeOptions = Object.keys(res.func_result.data).map(item => {
				return {
					value: item,
					label: res.func_result.data[item].model_name
				}
			})
			if (type == 'edit') {
				formState.line_data_detail.forEach(item => {
					if (!state.lineTypeOptions.find(items => items.value == item.line_model_id)) {
						item.line_model_id = undefined
					}
				})
			}
		}
	}).catch(() => {
		if (type == 'init') state.loading = false
	})
}
const reloadLineData = () => {
	state.lineTypeShow = false
	getLineData('edit')
}
onMounted(async() => {
	props.columns.forEach(item => {
		formState[item.field] = item.cellDataType == 'boolean' ? true : undefined
	})
	Object.keys(props.data).forEach(key => {
		formState[key] = key == 'line_data_detail' ? deepClone(props.data[key]) : props.data[key]
	})
	if (props.type == 'ac_line') {
		state.loading = true
		getLineData('init')
	}
	if (props.isAdd && props.type == 'ac_line') {
		formState.line_data_detail = [
			{
				length_km: 0,
				line_model_id: undefined
			}
		]
	}
})
watch(() => formState.line_data_detail, (val) => {
	if (formState.line_data_detail != undefined)formState.length_km = formState.line_data_detail.reduce((pre, cur) => pre + cur.length_km, 0)
}, { deep: true })
</script>
<style lang="scss">
    .AddSimpleModal {
        .ant-modal{
            width: auto!important;
            .modal-content{
                min-width: 800px;
                padding: 0 0 70px;
                .form_content{
                    padding: 30px 80px 0;
                    .grid{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        grid-column-gap: 40px;
                    }
                    .display{
                        display: flex;
                    }
                    .ant-input{
                        width: 200px;
                    }
                    .ant-input-number-wrapper{
                        width: 200px;
                        .ant-input-number-group-addon{
                            width: 40px;
                        }
                    }
                    .ant-form-item-control-input-content>.ant-input-number{
                        width: 200px;
                    }
                    .ant-select{
                        width: 200px;
                    }
                    .ant-form-item-label{
                        // text-align: left;
                        width: 200px;
                        label{
                            font-size: 18px;
                        }
                    }
                    .line_type_data{
                        padding:0 20px;
                        .ant-input-number-wrapper{
                            width: 160px;
                        }
                        .ant-select{
                            width: 160px;
                        }
                        >div{
                            display: grid;
                            position: relative;
                            grid-template-columns: 1fr 1fr;
                            grid-gap: 20px;
                            padding:0 0 20px;
                            >div{
                                display: flex;
                                align-items: center;
                                >p{
                                    margin-right: 10px!important;
                                }
                            }
                            .pointer{
                                font-size: 20px;
                                position: absolute;
                                right: -20px;
                                top: 5px;
                                opacity: 0.8;
                                &:hover{
                                    opacity: 1;
                                }
                                color: rgb(228, 51, 51);
                            }
                        }
                    }
                    .add_lines{
                        width: 90px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 20px;
                        color: rgb(71, 71, 71);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        height: 28px;
                        line-height: 28px;
                        text-align: center;
                        opacity: 0.8;
                        >span{
                            color: rgb(35, 137, 230);
                        }
                        &:hover{
                            cursor: pointer;
                            opacity: 1;
                        }
                        >span{
                            font-size: 16px;
                            margin-right: 5px;
                        }
                    }
                    .text_remark{
                        color: rgb(163, 0, 20);
                        font-weight: bolder;
                        font-size: 18px;
                    }
                }
            }
        }
    }
</style>
