<template>
  <a-modal
    wrapClassName="modal_confidence_analyse"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.ifShow"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <div class="user-select" :style="{zoom: state.zoom}">
      <div class="modal_top">
        <p>{{ $t('置信度曲线') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
    </div>
    <a-spin :spinning="state.loading" size="large" :tip="$t('接口请求中')">
      <div :class="['modal_content','relative',(!props.index && props.index != 0) ? 'gird_box' : '']">
        <div class="left" v-if="!props.index && props.index != 0">
          <div
            v-for="item in props.config"
            :key="item.lineName"
            @click="handleSelect(item)"
            :class="state.active_key==item.lineName?'active':''"
          >{{ item.name }}</div>
        </div>
        <div class="line" ref="line" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;height:${state.height}px;`"></div>
      </div>
    </a-spin>
    </div>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive, ref, inject, markRaw, onUnmounted } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getConfidenceSeries } from '@/utils/teap'
import { getBaseDataApi } from '@/api/exampleApi'
import { useRoute } from 'vue-router'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const route = useRoute()
const props = defineProps({
	index: {
		type: Number
	},
	config: {
		type: Array,
		default: () => []
	},
	sideList: {
		type: Object,
		default: () => {}
	}
})
const echarts = inject('ec')
const lineChart = ref()
const state = reactive({
	ifShow: true,
	loading: false,
	zoom: 1,
	zooms: 1,
	scales: 1,
	height: 450,
	lineData: []
})
const line = ref()
const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}

const handleSelect = (val) => {
	state.active_key = val.lineName
	getInitData({
		'import_string_func': 'teapcase:get_cdf_by_input',
		'func_arg_dict': {
			'value': props.sideList[val.lineName]
		}
	})
}

const initLine = (data) => {
	const option = getConfidenceSeries(data)
	lineChart.value.setOption(option)
}

const getInitData = (tempQuery) => {
	getBaseDataApi(tempQuery).then(res => {
		state.loading = false
		if (res.code == 1 && res.func_result.code == 1) {
			initLine(res.func_result.data)
		}
	}).catch(() => {
		state.loading = false
	})
}

const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		state.zoom = root
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	state.height = 450 * root
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(() => {
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	lineChart.value = markRaw(echarts.init(line.value))
	state.loading = true
	let tempQuery = {}

	if (props.index || props.index == 0) {
		tempQuery = {
			'import_string_func': 'teapcase:get_cdf_by_ts_from_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'row_id': props.index
			}
		}
	} else {
		state.active_key = props.config[0].lineName
		tempQuery = {
			'import_string_func': 'teapcase:get_cdf_by_input',
			'func_arg_dict': {
				'value': props.sideList[props.config[0].lineName]
			}
		}
	}
	getInitData(tempQuery)
})
</script>
<style lang="scss">
.modal_confidence_analyse{
  .ant-modal{
    width: auto!important;
    // height: 80vh;
    .ant-modal-body{
      >div{
        .gird_box {
          display: grid;
          grid-template-columns: 1fr 3fr;
          grid-column-gap: 20px;
          padding: 10px 15px 15px 0;
        }
        .modal_content{
          height: 480px;
          padding: 10px 15px;
          width: 1200px;
          .left {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            overflow: auto;
            div{
              width: 100%;
              text-align: center;
              // border-bottom: 1px solid #ccc;
              margin-bottom: 10px;
              padding: 5px 0;
            }
            .active {
              background: #c1d3ed;
              border-radius: 4px;
            }
          }
          .line{
            transform-origin: 0 0;
          }
        }
      }
    }
  }
}
</style>

