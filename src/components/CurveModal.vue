<template>
  <a-modal wrapClassName="modal_curve" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <div class="user-select" :style="{zoom: state.zoom}">
      <close-outlined class="pointer absolute" @click="emit('close')" />
      <a-spin :spinning="state.loading" size="large" :tip="$t('接口请求中')">
        <div class="modal_content relative">
          <div>
            <!-- <img src="" alt=""> -->
            <CheckCircleOutlined />
            <p>{{props.type=='solar'?'光伏':props.type=='wind'?$t('风电'):props.type=='windSolar'?$t('风光'):$t('负荷')}}{{ $t('预测曲线生成') }}</p>
          </div>
          <div class="line_check" v-if="props.type=='load'">
            <a-select
              style="width: 140px"
              v-model:value="state.area"
              @change="handleAreaChange"
              :options="state.areaList">
            </a-select>
            <a-radio-group v-if="props.type=='load'" v-model:value="state.type" @change="changeLineType">
              <a-radio :value="1">{{ $t('负荷') }}</a-radio>
              <a-radio :value="2">{{ $t('用电量') }}</a-radio>
              <a-radio :value="3">{{ $t('预测成因分析') }}</a-radio>
            </a-radio-group>
            <p v-if="state.type==2">{{ $t('全年总用电量') }} {{ (state.total_power ).toFixed(2)}} {{ $t('亿千瓦时') }}</p>
            <p v-if="props.type=='load'&&state.type==1">{{ $t('最高气温') }} {{ (state.max_temp ).toFixed(2)}}℃&nbsp;&nbsp;&nbsp;{{ $t('最低气温') }} {{ (state.min_temp ).toFixed(2)}}℃ </p>
            <p class="dateSelect" v-if="state.type==3"><span :class="state.dateType=='workday'?'active':''" @click="changeDateType('workday')">{{ $t('工作日') }}</span><span :class="state.dateType=='holiday'?'active':''" @click="changeDateType('holiday')">{{ $t('节假日') }}</span></p>
          </div>
          <div v-show="state.type==1" class="line" ref="line" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;height:${state.height}px;`">

          </div>
          <div v-show="state.type==2" class="bar" ref="bar" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;height:${state.height}px;`">

          </div>
          <div v-show="state.type==3" class="bar" ref="bars" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;height:${state.height}px;`">

          </div>
          <div class="icon_list absolute" v-if="props.type!='load'">
            <p :class="state.MAX?'active':''" @click="selectLine('MAX')">MAX</p>
            <p :class="state.AVG?'active':''" @click="selectLine('AVG')">AVG</p>
            <p :class="state.MIN?'active':''" @click="selectLine('MIN')">MIN</p>
          </div>
        </div>
        <div class="modal_btn">
          <a-button v-if="props.state.isEdit" @click="confirm(0)" type="primary">{{ $t('保存至时序表') }}</a-button>
          <a-button @click="confirm(1)" type="primary">{{ $t('保存至本地') }}</a-button>
          <a-button @click="emit('close')">{{ $t('返回') }}</a-button>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import { onMounted, reactive, ref, inject, markRaw, onUnmounted } from 'vue'
import { TmyInsert, TmyWindAndSolarInsert, LoadForecastInsert, DownloadTempFile } from '@/api/index'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getLineOption, openModal, getBarOption } from '@/utils/teap'
import message from '@/utils/message'
import { fileBlobFun } from '@/utils/common.js'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const props = defineProps({
	type: {
		type: String
	},
	state: {
		type: Object,
		default: () => {}
	}
})
const echarts = inject('ec')
const lineChart = ref()
const barChart = ref()
const barCharts = ref()
const lineOption = ref({})
const state = reactive({
	areaList: [],
	area: undefined,
	ifShow: true,
	loading: false,
	type: 1,
	dateType: 'workday',
	zoom: 1,
	zooms: 1,
	scales: 1,
	height: 480,
	MAX: false,
	MIN: false,
	AVG: false,
	total_power: 0,
	max_temp: 0,
	min_temp: 0
})
const line = ref()
const bar = ref()
const bars = ref()
const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}
const handleAreaChange = (val) => {
	initLine()
	state.max_temp = props.state.loadData[val].max_temp
	state.min_temp = props.state.loadData[val].min_temp
	state.total_power = props.state.loadData[val].total_power
}
const initLine = () => {
	lineOption.value = props.type == 'load'
		? getLineOption(props.type, props.state.loadData[state.area].data_list, props.state.caseYear, state.MAX, state.AVG, state.MIN, props.state.loadData[state.area].pred_second_load, props.state.loadData[state.area].pred_third_load)
		: getLineOption(props.type, props.state.lineData, props.state.caseYear, state.MAX, state.AVG, state.MIN)
	lineChart.value.setOption(lineOption.value)

	if (props.type == 'load') {
		const { option1, option2 } = getBarOption(props.state.loadData[state.area], props.state.caseYear, state.dateType)
		barChart.value.setOption(option1)
		barCharts.value.setOption(option2)
	}
}
const selectLine = (type) => {
	state[type] = !state[type]
	initLine()
}
const changeLineType = () => {

}
const changeDateType = (val) => {
	if (val == state.dateType) return
	state.dateType = val
	const { option2 } = getBarOption(props.state.loadData[state.area], props.state.caseYear, state.dateType)
	barCharts.value.setOption(option2)
}
const confirm = (type) => {
	if (type == 1) {
		state.loading = true

		let downloadiFleName
		props.type == 'load' ? downloadiFleName = props.state.loadData[state.area].download_file_name : downloadiFleName = props.state.download_file_name
		DownloadTempFile({
			'file_name': downloadiFleName
		}).then(res => {
			fileBlobFun(res.data, downloadiFleName)
		})
		state.loading = false
	} else {
		state.loading = true
		if (props.type != 'load') {
			let tempParams = {}
			let insertPost
			if (props.type != 'windSolar') {
				insertPost = TmyInsert
				tempParams = {
					trg_hour: props.state.trg_hour,
					index_list: props.state.index_list.filter(item => !item.includes('_plan')).map(item => item.split('-')[1]),
					index_list_for_plan: props.state.index_list.filter(item => item.includes('_plan')).map(item => item.split('-')[1])
				}
			} else {
				insertPost = TmyWindAndSolarInsert
				tempParams = {
					extreme_ratio: props.state.extreme_ratio,
					wind_index_list: props.state.wind_index_list.filter(item => !item.includes('_plan')).map(item => item.split('-')[1]),
					wind_plan_index_list: props.state.wind_index_list.filter(item => item.includes('_plan')).map(item => item.split('-')[1]),
					solar_index_list: props.state.solar_index_list.filter(item => !item.includes('_plan')).map(item => item.split('-')[1]),
					solar_plan_index_list: props.state.solar_index_list.filter(item => item.includes('_plan')).map(item => item.split('-')[1])
				}
			}
			insertPost(Object.assign({
				tc_filename: props.state.tc_filename,
				job_type: props.type,
				year: props.state.year

			},
			tempParams,
			props.state.type == 1 ? {
				province: props.state.province,
				city: props.state.city
			} : {
				lat: props.state.lat,
				lon: props.state.lon
			}
			)).then(res => {
				state.loading = false
				if (res.code == 1) {
					message.success(res.message)
					emit('close', true)
				} else {
					openModal(res.message).then(res => {
						state.loading = true
						insertPost(Object.assign({
							tc_filename: props.state.tc_filename,
							job_type: props.type,
							year: props.state.year,

							confirm: true
						},
						tempParams,
						props.state.type == 1 ? {
							province: props.state.province,
							city: props.state.city
						} : {
							lat: props.state.lat,
							lon: props.state.lon
						}
						)).then(res => {
							state.loading = false
							emit('close', true)
						}).catch(() => {
							state.loading = false
						})
					})
					//
				}
			}).catch(() => {
				state.loading = false
			})
		} else {
			LoadForecastInsert(Object.assign({
				tc_filename: props.state.tc_filename,
				pred_year: props.state.year,
				hist_year: props.state.hist_year,
				area: props.state.area,
				extreme_ratio: props.state.extreme_ratio,
				index_list: props.state.area.length <= 1 ? props.state.index_list : [],
				max_power_limit: props.state.max_power_limit
			},
			props.state.powerType == 1 ? {
				pred_power_consumption: props.state.pred_power_consumption
			} : {
				pred_sec_power: props.state.pred_sec_power,
				pred_tri_power: props.state.pred_tri_power,
				pred_city_power: props.state.pred_city_power
			}
			)).then(res => {
				state.loading = false
				if (res.code == 1) {
					message.success(res.message)
					emit('close', true)
				}
			}).catch(() => {
				state.loading = false
			})
		}
	}
}
const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		state.zoom = root
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	state.height = 500 * root
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(() => {
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	lineChart.value = markRaw(echarts.init(line.value))
	if (props.type == 'load') {
		state.areaList = props.state.area.map(item => {
			return {
				value: item,
				label: item
			}
		})
		state.area = props.state.area[0]
		state.max_temp = props.state.loadData[state.area].max_temp
		state.min_temp = props.state.loadData[state.area].min_temp
		state.total_power = props.state.loadData[state.area].total_power
		barChart.value = markRaw(echarts.init(bar.value))
		barCharts.value = markRaw(echarts.init(bars.value))
	}
	initLine()
})
</script>
<style lang="scss">
  .modal_curve{
    .ant-modal{
      width: 52%!important;
      .ant-modal-body{
        >div{
          .pointer{
            font-size: 20px;
            background-color: rgb(244, 244, 244);
            padding: 5px;
            border-radius: 5px;
            right: 5px;
            z-index: 9;
          }
          .modal_content{
            height: 780px;
            padding: 30px 30px 90px;
            >div:first-child{
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
                img{
                    height: 100px;
                    width: 100px;
                }
                >span{
                    font-size: 100px;
                    background-color: #E8F8F0;
                    color: #49CB8A;
                    border-radius: 100%;
                }
                p{
                    font-size: 20px;
                    line-height: 60px;
                }
            }
            .line,.bar{
                width: 940px;
                transform-origin: 0 0;
            }
            .line_check{
              display: flex;
			  align-items: center;
			  position: relative;
			  .ant-select{
				margin-right: 20px
			  }
			  .dateSelect{
				display: flex;
				border-radius: 5px;
				overflow: hidden;
				border: 1px solid var(--base-color);
			  }
			  >p{
				position: absolute;
				z-index: 1;
				left:2px;
				top: 35px;
				span{
					padding:0 5px;
					&:hover{
						cursor: pointer;
					}
				}
				.active{
					background-color: var(--base-color);
					color:#fff
				}
			  }
            }
            .icon_list{
              right: 30px;
              top: 350px;
              p{
                border-radius: 100%;
                width: 40px;
                line-height: 40px;
                background-color: rgb(18, 150, 219);
                text-align: center;
                color: #fff;
                margin-bottom: 20px!important;
                opacity: 0.7;
                &:hover{
                  cursor: pointer;
                  opacity: 1;
                }
              }
              .active{
                opacity: 1;
              }
            }
          }
          .modal_btn{
            // right: unset;
            // width: 100%;
            // display: flex;
            // justify-content: center;
          }
        }
      }
    }
  }
</style>

