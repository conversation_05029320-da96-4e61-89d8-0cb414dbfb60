<template>
    <div class="plan_result_box">
        <div class="cost_box_content">
            <div class="date_box">
                <a-date-picker v-model:value="state.dateValue" :disabled-date="disabledDate" valueFormat="YYYY-MM-DD" @change="dateChange" :allowClear="false" />
            </div>
            <div class="plan_left">
                <div class="ag-grid-box">
                    <div class="ag-title">{{ $t('规划装机') }}</div>
                    <AgGrid ref="agGridRef" :isEdit="'costSheet'" :istree="true"></AgGrid>
                </div>
                <div class="ag-grid-box">
                    <div class="ag-title">{{ $t('线路规划容量') }}</div>
                    <AgGrid ref="agGridPlanRef" :isEdit="'costSheet'"></AgGrid>
                </div>
            </div>
            <div class="plan_right">
                <div class="chart-box">
                    <div class="unit">
                        <span>{{ $t('分区') }}：</span>
                        <a-select
                            ref="select"
                            size="small"
                            :bordered="false"
                            v-model:value="state.zoneValue"
                            style="width: 100px"
                            :options="state.zoneOptions"
                            @change="changeZone"
                        ></a-select>
                    </div>
                    <div ref="bar" class="pie" :style="{transform:`scale(${state.scale})`,zoom:`${state.zoom}`,width:`${state.width}`,height:`${state.height}`}"></div>
                </div>
                <div class="chart-box">
                    <div class="unit1">
                        <span>{{ $t('设备类型') }}：</span>
                        <a-select
                            ref="select"
                            size="small"
                            :bordered="false"
                            v-model:value="state.typeValue"
                            style="width: 180px;"
                            :options="state.typeOptions"
                            @change="changeType"
                        ></a-select>
                    </div>
                    <div ref="line" class="pie" :style="{transform:`scale(${state.scale})`,zoom:`${state.zoom}`,width:`${state.width}`,height:`${state.height}`}"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted, onUnmounted, toRefs, markRaw, inject, nextTick, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { GetMidTermTaskResult } from '@/api/exampleApi'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import { debounce } from '@/utils/gis'
const store = settingStore()
const { isChromeHigh } = storeToRefs(store)
import { get_plan_bar, get_plan_line } from '@/utils/resultEchartsOptions.js'
const route = useRoute()
const emit = defineEmits(['hideLoading', 'refresh', 'showLoading'])
// AG-Grid表格
const agGridRef = ref(null)
const agGridPlanRef = ref(null)
const echarts = inject('ec')
const state = reactive({
	scale: 1,
	zoom: 1,
	width: undefined,
	height: undefined,
	isScale: false,
	trueTableData: [],
	routePath: route.fullPath,
	maxData: [],
	lineData: {},
	zoneValue: null,
	zoneOptions: [],
	typeValue: null,
	typeOptions: [],
	configObj: {},
	startDate: '',
	endDate: '',
	dateValue: null,
	snap_idx: -1,
	timeData: []
})
const disabledDate = (current) => {
	return current < new Date(state.startDate) || current > new Date(state.endDate)
}
const dateChange = (date) => {
	state.snap_idx = date ? state.timeData.findIndex(item => item == date + ' 23:00:00') : -1
	getMidTermResult(true)
}
const allEcharts = reactive({
	barChart: undefined,
	bar: undefined,
	lineChart: undefined,
	line: undefined
})
const { bar, line } = toRefs(allEcharts)
const initEcharts = () => {
	const option1 = get_plan_bar(state.trueTableData, state.maxData, state.zoneValue, state.configObj, state.zoom)
	allEcharts.barChart.setOption(option1, true)
	const option2 = get_plan_line(state.lineData, state.zoom)
	allEcharts.lineChart.setOption(option2, true)
}
const getMidTermResult = (val) => {
	if (val) emit('showLoading')
	GetMidTermTaskResult({
		'group': '_result.plan_install_cap_table',
		'result_file_path': decodeURIComponent(route.query.filePath),
		'extra_param': {
			'snap_idx': state.snap_idx,
			'ts_zone': state.zoneValue,
			'ts_type': state.typeValue
		}
	}).then((res) => {
		if (res.code == 1) {
			const { 实际投运容量, 待规划线路投运容量, 投运时间曲线分区列表, 投运时间曲线类型列表, 投运时间曲线, 设备类型中英文映射字典,
				 选定分区, 选定类型, 时间戳起始, 时间戳截止, 柱状图实际投运容量, 柱状图最大可投运容量, 时间戳列表
			} = res.data

			state.startDate = 时间戳起始
			state.endDate = 时间戳截止
			state.zoneValue = 选定分区
			state.typeValue = 选定类型
			state.timeData = 时间戳列表
			if (!val) state.dateValue = res.data['时间戳截止']
			// state.dateValue = res.data['时间戳起始']
			// trueTableData.columns.forEach((item, index) => {
			// 	if (item.field == '类型' || item.field == '类别') {
			// 		item.width = 110
			// 	} else {
			// 		item.width = 132
			// 	}
			// })
			const tempTrueData = 实际投运容量
			const tempPlanData = 待规划线路投运容量
			state.configObj = 设备类型中英文映射字典
			state.lineData = 投运时间曲线
			state.maxData = 柱状图最大可投运容量
			state.trueTableData = 柱状图实际投运容量
			state.zoneOptions = 投运时间曲线分区列表.map(item => {
				return {
					label: item,
					value: item
				}
			})
			state.typeOptions = 投运时间曲线类型列表.map(item => {
				return {
					label: state.configObj[item],
					value: item
				}
			})
			nextTick(() => {
				agGridRef.value.setBlanceData(tempTrueData)
				agGridPlanRef.value.setBlanceData(tempPlanData)
				allEcharts.barChart = markRaw(echarts.init(allEcharts.bar))
				allEcharts.lineChart = markRaw(echarts.init(allEcharts.line))
				initEcharts()
			})
		}
		emit('hideLoading')
	}).catch(() => {
		emit('hideLoading')
	})
}
const changeZone = (val) => {
	// initEcharts()
	getMidTermResult(true)
}
const changeType = (val) => {
	getMidTermResult(true)
}
// 分区成本单位切换
// const changeCostUnit = (val) => {
// 	const tableData = JSON.parse(JSON.stringify(state.costData))
// 	tableData.data.forEach(element => {
// 		for (const key in element) {
// 			if (key !== 'index' && element[key] !== '') {
// 				if (!isNaN(element[key])) {
// 					element[key] = parseFloat(element[key] / val).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
// 				}
// 			}
// 		}
// 	})
// 	nextTick(() => {
// 		agGridRef.value.setBlanceData(tableData)
// 	})
// }

// 联络线成本单位切换
// const changeLineUnit = (val) => {
// 	const trueTableData = JSON.parse(JSON.stringify(state.costLineData))
// 	trueTableData.data.forEach(element => {
// 		for (const key in element) {
// 			if (key !== 'index' && element[key] !== '' && element[key] !== null) {
// 				if (!isNaN(element[key])) {
// 					element[key] = parseFloat(element[key] / val).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
// 				}
// 			}
// 		}
// 	})
// 	nextTick(() => {
// 		agGridPlanRef.value.setBlanceData(trueTableData)
// 	})
// }

// const formatCloud = computed(() => {
// 	return function(index) {
// 		// return parseFloat(index / state.unitValue).toFixed(2).toLocaleString() // 使用toFixed后toLocaleString不生效
// 		if (index == null) {
// 			return '-'
// 		} else {
// 			return parseFloat(index / state.unitValue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
// 		}
// 	}
// })

const screenScale = (val) => {
	if (val != 1) {
		if (state.routePath == route.fullPath) {
			emit('refresh', 7)
		} else {
			state.isScale = true
		}
	} else {
		let root
		if (isChromeHigh.value) {
			root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		} else {
			root = document.body.style.zoom
		}
		state.zoom = 1 / root
		state.scale = root
		state.width = bar.value.parentElement.offsetWidth - 30 + 'px'
		state.height = bar.value.parentElement.offsetHeight - 30 + 'px'
	}
}
const debouncedScreenScale = debounce(screenScale, 200)
onActivated(() => {
	if (state.isScale) {
		emit('refresh', 7)
		state.isScale = false
	}
})
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(() => {
	emit('showLoading')
	window.addEventListener('resize', debouncedScreenScale)
	screenScale(1)
	getMidTermResult()
})
</script>
<style lang="scss" scoped>
.plan_result_box {
    width: 100%;
    height: calc(100%);
    box-sizing: border-box;
    overflow: auto;
    user-select: text;
    .cost_box_content {
        width: 100%;
        height: calc(100% - 5px);
        text-align: center;
        box-sizing: border-box;
        // display: grid;
        // grid-template-columns: 1fr 1fr;
        // grid-gap: 10px;

        display: flex;
        justify-content: space-between;
        position: relative;
        .date_box {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 30;
        }
        .plan_left,.plan_right {
            height: 100%;
            width: calc(50% - 5px);
        }
        .ag-grid-box{
            height: 60%;
            position: relative;
            .ag-title {
                position: absolute;
                top: 20px;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #49699c;
                font-weight: 500;
            }
        }
        .ag-grid-box:last-child  {
            height: 40%;
        }
        .chart-box {
            height: 50%;
            padding: 15px;
            overflow: hidden;
            font-weight: 700;
            position: relative;
            .unit {
                position: absolute;
                top: 15px;
                left: 30px;
                z-index: 30;
                font-weight: 400;
                color: #91939e;
            }
            .unit1 {
                position: absolute;
                top: 10px;
                left: 30px;
                z-index: 30;
                // transform: translate(-50%, -50%);

                font-weight: 400;
                color: #91939e;
            }
        }
        .pie {
            width: 100%;
            height: 100%;
            transform-origin: 0 0;
        }
    }
}
</style>
