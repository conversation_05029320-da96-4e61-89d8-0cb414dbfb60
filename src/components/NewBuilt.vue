<template>
    <a-modal wrapClassName="modal_newBuilt" :afterClose="closeModal" :centered="true"  v-model:open="state.visible" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('保存算例') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <a-spin :spinning="state.loading" size="large">
                <div class="modal_content relative">
                    <p>{{ $t('文件名称') }}</p>
                    <div class="modal_setting">
                        <a-input v-model:value="state.fileName" style="width: 360px" :placeholder="$t('请输入')" />
                    </div>
                    <div class="modal_btn">
                        <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
                        <a-button @click="handleConfirm" type="primary" :style="{color: '#fff'}">{{ $t('确认') }}</a-button>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { onMounted, reactive } from 'vue'
// import { useRoute } from 'vue-router'
import { useRoute } from 'vue-router'
// import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'
import { saveBaseDataApi } from '@/api/exampleApi'

const route = useRoute()

const props = defineProps({
	caseName: {
		type: String,
		default: ''
	},
	type: {
		type: String,
		default: ''
	}
})

const state = reactive({
	routePath: route.fullPath,
	visible: true,
	loading: false,
	fileName: props.caseName + Date.now()
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}

// 确认新建
const handleConfirm = () => {
	if (state.routePath !== route.fullPath) return
	if (state.fileName == '') return
	// Mitt.emit('handleSaveAgtable', props.type)
	Mitt.emit('toDetailSave', props.type)
}

const saveNewFile = (val) => {
	if (state.routePath !== route.fullPath) return
	state.loading = true
	saveBaseDataApi({
		'import_string_func': 'teapcase:rename_tc_file',
		'func_arg_dict': {
			'file_name': route.query.filePath, // # 要带扩展名.h5
			'new_file_name': state.fileName // # 此参数可以不传，也可以为空列表，两种情况都会返回所有表
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			if (val == 'newBuilt') { // 新建保存
				emit('confirm', res.func_result.tc_file_name, res.func_result.tc_file_path)
			} else { // 新建算例 保存并计算
				Mitt.emit('handleUploadCase', res.func_result.tc_file_path)
				setTimeout(() => {
					emit('confirm', res.func_result.tc_file_name, res.func_result.tc_file_path)
				}, 1000)
			}
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}
Mitt.on('saveNewFile', saveNewFile)

onMounted(() => {

})
</script>
<style lang="scss">
.modal_newBuilt{
    .ant-modal{
        width: auto!important;
        .ant-modal-body{
            >div{
                .modal_content{
                    padding: 10px 30px 90px;
                    >p{
                        font-size: 15px;
                    }
                    .modal_setting{
                        padding: 10px 0;
                        height: 55px;
                    }
                    .modal_upload{
                        padding: 10px 0;
                        height: 55px;
                    }
                }
            }
        }
    }
}
</style>

