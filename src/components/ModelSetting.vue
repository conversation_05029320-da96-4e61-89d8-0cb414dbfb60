<template>
  <a-modal wrapClassName="modal_model" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
        <div class="modal_top">
            <p>{{ $t('模型参数管理') }}</p>
            <close-outlined class="pointer" @click="emit('close')" />
        </div>
        <a-spin :spinning="state.loading" size="large">
            <div class="modal_content relative">
                <p>{{ $t('模型可调参数') }}</p>
                <div>
                    <div>
                        <div class="setting_input">
                            <div class="setting_tip">
                                <p>{{ $t('树的数量') }}</p>
                                <a-tooltip placement="bottomLeft">
                                    <template #title>
                                    <span>{{ $t('集成树的数量') }}</span>
                                    </template>
                                    <exclamation-circle-filled />
                                </a-tooltip>
                            </div>
                            <a-input-number :min="0" v-model:value="state.model_param.n_estimators"></a-input-number>
                        </div>
                        <div class="setting_input">
                            <div class="setting_tip">
                                <p>{{ $t('树的最大深度') }}</p>
                                <a-tooltip placement="bottomLeft">
                                    <template #title>
                                    <span>{{ $t('控制决策树的最大深度') }}</span>
                                    </template>
                                    <exclamation-circle-filled />
                                </a-tooltip>
                            </div>
                            <a-input-number :min="0" v-model:value="state.model_param.max_depth"></a-input-number>
                        </div>
                        <div class="setting_input">
                            <div class="setting_tip">
                                <p>{{ $t('分裂节点所需最小样本数') }}</p>
                                <a-tooltip placement="bottomLeft">
                                    <template #title>
                                    <span>{{ $t('控制当前节点分裂所需的最小样本数')}}</span>
                                    </template>
                                    <exclamation-circle-filled />
                                </a-tooltip>
                            </div>
                            <a-input-number :min="0" v-model:value="state.model_param.min_samples_split"></a-input-number>
                        </div>
                    </div>
                    <div>
                        <div class="setting_input">
                            <div class="setting_tip">
                                <p>{{ $t('叶节点所需最小样本数') }}</p>
                                <a-tooltip placement="bottomLeft">
                                    <template #title>
                                    <span>{{ $t('任何深度的分割点只有在左右分支中至少留下所填数量的训练样本时才会被考虑') }}</span>
                                    </template>
                                    <exclamation-circle-filled />
                                </a-tooltip>
                            </div>
                            <a-input-number :min="0" v-model:value="state.model_param.min_samples_leaf"></a-input-number>
                        </div>
                        <div class="setting_input">
                            <div class="setting_tip">
                                <p>{{ $t('并行数') }}</p>
                                <a-tooltip placement="bottomLeft">
                                    <template #title>
                                    <span>{{ $t('并行线程的数量，默认1代表不并行') }}</span>
                                    </template>
                                    <exclamation-circle-filled />
                                </a-tooltip>
                            </div>
                            <a-input-number :min="0" v-model:value="state.model_param.n_jobs"></a-input-number>
                        </div>
                        <div class="setting_input">
                            <div class="setting_tip">
                                <p>{{ $t('热启动开关') }}</p>
                                <a-tooltip placement="bottomLeft">
                                    <template #title>
                                    <span>{{ $t('当设置为True,重新使用之前的结构去拟合样例并且加入更多的估计器(随机树)到集成树中')}}</span>
                                    </template>
                                    <exclamation-circle-filled />
                                </a-tooltip>
                            </div>
                            <a-radio-group v-model:value="state.model_param.warm_start">
                                <a-radio :value="true">{{$t('开')}}</a-radio>
                                <a-radio :value="false">{{$t('关')}}</a-radio>
                            </a-radio-group>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal_btn">
            <a-button @click="confirm" type="primary">{{$t('确定')}}</a-button>
            <a-button @click="reset">{{$t('重置')}}</a-button>
            <a-button @click="emit('close')">{{$t('取消')}}</a-button>
            </div>
        </a-spin>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { LoadForecastModelParamQueryView, LoadForecastModelParamUpdateView } from '@/api/index'
import message from '@/utils/message'
import { t } from '@/utils/common'
const state = reactive({
	ifShow: true,
	loading: true,
	default_model_param: {
		n_estimators: undefined,
		max_depth: undefined,
		min_samples_split: undefined,
		min_samples_leaf: undefined,
		n_jobs: undefined,
		warm_start: undefined
	},
	model_param: {
		n_estimators: undefined,
		max_depth: undefined,
		min_samples_split: undefined,
		min_samples_leaf: undefined,
		n_jobs: undefined,
		warm_start: undefined
	}
})
const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}
const confirm = () => {
	if (state.model_param.n_estimators == undefined || state.model_param.max_depth == undefined || state.model_param.min_samples_split == undefined || state.model_param.min_samples_leaf == undefined || state.model_param.n_jobs == undefined) {
		message.warning(t('请填写完整参数'))
		return
	}
	state.loading = true
	LoadForecastModelParamUpdateView({
		n_estimators: state.model_param.n_estimators,
		max_depth: state.model_param.max_depth,
		min_samples_split: state.model_param.min_samples_split,
		min_samples_leaf: state.model_param.min_samples_leaf,
		n_jobs: state.model_param.n_jobs,
		warm_start: state.model_param.warm_start
	}).then(res => {
		state.loading = false
		if (res.code == 1) {
			message.success(res.message)
		}
	}).catch(() => { state.loading = false })
}
const reset = () => {
	state.model_param.n_estimators = state.default_model_param.n_estimators
	state.model_param.max_depth = state.default_model_param.max_depth
	state.model_param.min_samples_split = state.default_model_param.min_samples_split
	state.model_param.min_samples_leaf = state.default_model_param.min_samples_leaf
	state.model_param.n_jobs = state.default_model_param.n_jobs
	state.model_param.warm_start = state.default_model_param.warm_start
}
onMounted(() => {
	LoadForecastModelParamQueryView().then(res => {
		state.loading = false
		if (res.code == 1) {
			state.default_model_param = res.default_model_param
			state.model_param = res.model_param
		} else {
			message.error(res.message)
			emit('close')
		}
	}).catch(() => {
		state.loading = false
		emit('close')
	})
})
</script>
<style lang="scss">
  .modal_model{
    .ant-modal{
      width: auto!important;
      .ant-modal-body{
        >div{
          .modal_content{
            width: 880px;
            padding: 20px 30px 90px;
             >p{
                  font-size: 18px;
                  font-weight: bolder;
                  line-height: 40px;
              }
              >div{
                    background: rgb(248, 248, 248);
                    >div{
                        display: grid;
                        padding: 16px 32px;
                        grid-template-columns: 1fr 1fr 1fr;
                    }
                    .setting_input{
                        margin-bottom: 0px;
                        .calculate_text{
                            margin-left: 15px;
                            font-weight: normal;
                            color: var(--base-color);
                        }
                        .ant-input-number{
                            width: 140px;
                        }
                    }
                    .setting_tip{
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;
                        >p{
                            font-size: 16px;
                            font-weight: bolder;
                            // line-height: 30px;
                        }
                        span{
                            margin-left: 3px;
                            font-size: 14px;
                            color: var(--base-color);
                        }
                    }
              }
          }
          .modal_btn{
            button{
                letter-spacing: 0;
            }
          }
        }
      }
    }
  }
</style>

