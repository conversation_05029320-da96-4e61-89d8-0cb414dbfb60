<template>
    <a-modal wrapClassName="GisCreate" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
                <div>
                    <div class="modal_top">
                        <p>{{ $t('新建') }}</p>
                        <close-outlined class="pointer" @click="closeModal" />
                    </div>
                    <div class="modal-content">
                        <div>
                            <div>
                                <p>{{ $t('文件名称') }}</p>
                                <a-input v-model:value="formState.name" :placeholder="$t('请输入文件名称')" />
                                <div class="upload_div">
                                    <p>{{ $t('数据文件') }}</p>
                                    <a-upload
                                        v-if="state.tcFileList.length == 0"
                                        v-model:file-list="state.tcFileList"
                                        name="file"
                                        @change="changeTc"
                                        accept=".tc,.tr"
                                        :multiple="false"
                                        :beforeUpload="()=>false"
                                        :maxCount="1"
                                    >
                                        <p class="upload_p">
                                            <upload-outlined></upload-outlined>
                                            {{ $t('上传文件') }}
                                        </p>
                                    </a-upload>
                                    <p class="file_p" v-else>
                                        {{ state.tcFileList[0].name }}
                                        <DeleteOutlined @click="deleteTcFile" />
                                    </p>
                                </div>
                                <div :class="state.tcDisabled?'list list_disabled':'list'">
                                    <div>
                                        <p :class="state.type==0?'active':''" @click="changeType(0)">{{ $t('编辑器') }}</p>
                                        <p :class="state.type==1?'active':''" @click="changeType(1)">{{ $t('结果列表') }}</p>
                                    </div>
                                    <div class="list_content" v-show="state.type==1">
                                        <p :class="state.resultTcFile==item.result_file_path?'active':''" @click="selectResult(item)" v-for="(item,index) in state.resultList" :key="index">{{ item.case_file_name }}</p>
                                    </div>
                                    <div class="list_content" v-show="state.type==0">
                                        <p :class="state.editTcFile==item.filePath?'active':''" @click="selectEdit(item)" v-for="(item,index) in state.editList" :key="index">{{ item.title }}</p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="upload_div">
                                    <p>{{ $t('gis底图') }}</p>
                                    <a-upload
                                        v-if="state.gisFileList.length == 0"
                                        v-model:file-list="state.gisFileList"
                                        name="file"
                                        @change="changeMapJson"
                                        accept=".json,.geojson"
                                        :multiple="false"
                                        :showUploadList="false"
                                        :beforeUpload="()=>false"
                                        :maxCount="1"
                                    >
                                        <p class="upload_p">
                                            <upload-outlined></upload-outlined>
                                            {{ $t('上传自定义地图') }}
                                        </p>
                                    </a-upload>
                                    <p class="file_p" v-else>
                                        {{ state.gisFileList[0].name }}
                                        <DeleteOutlined @click="deleteGisFile" />
                                    </p>
                                </div>
                                <a-select
                                    v-model:value="formState.geojson_name"
                                    :options="state.gisOption"
                                    :disabled ="state.gisDisabled"
                                    :filter-option="filterOption"
                                    show-search
                                    allowClear
                                    :placeholder="$t('请选择gis底图')"
                                ></a-select>
                                <p>{{ $t('文件备注') }}</p>
                                <a-textarea v-model:value="formState.note" />
                            </div>
                        </div>
                        <div class="modal_btn">
                            <a-button :disabled="btnDisabled" @click="confirmAdd" type="primary">{{ $t('确认') }}</a-button>
                            <a-button @click="closeModal">{{ $t('取消') }}</a-button>
                        </div>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { basicApi } from '@/api/exampleApi'
import { getTaskTableApi, UploadTempFile } from '@/api/index'
import { t } from '@/utils/common'
import message from '@/utils/message'
import { checkGeoJson } from '@/utils/gis'
import { routeStore } from '@/store/routeStore'
const storeRoute = routeStore()
const { routeTabs } = storeToRefs(storeRoute)
const state = reactive({
	ifShow: true,
	type: 0,
	loading: false,
	gisFileList: [],
	tcFileList: [],
	gisOption: [],
	resultList: [],
	editList: [],
	mapJsonText: undefined,
	tcDisabled: false,
	gisDisabled: false,
	editTcFile: undefined,
	resultTcFile: undefined,
	tcFilePath: undefined
})
const emit = defineEmits(['close', 'confirm'])
const formState = reactive({
	name: undefined,
	note: '',
	geojson_name: undefined
})
const filterOption = (input, option) => {
	return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const btnDisabled = computed(() => {
	if (state.tcFilePath) return false
	if (state.type == 1 && state.resultTcFile == undefined) return true
	if (state.type == 0 && state.editTcFile == undefined) return true
	return false
})
const closeModal = () => {
	emit('close')
}
const selectEdit = (item) => {
	state.editTcFile = item.filePath
}
const selectResult = (item) => {
	state.resultTcFile = item.result_file_path
}
const deleteGisFile = () => {
	state.gisFileList = []
	formState.geojson_name = undefined
	state.gisDisabled = false
}
const deleteTcFile = () => {
	state.tcFileList = []
	state.tcFilePath = undefined
	state.tcDisabled = false
}
const changeType = (val) => {
	if (state.type == val) return
	state.type = val
	if (state.type == 1) {
		initResult()
	} else {
		state.editList = routeTabs.value.filter(item => {
			return item.type == 'isEditor' || item.type == 'firstSave'
		})
	}
}
const changeMapJson = async({ file, fileList }) => {
	if (fileList.length == 0) {
		return
	}
	if (state.gisOption.find(item => item.value == file.name.split('.')[0])) {
		message.error(t('该地理json文件已存在'))
		setTimeout(() => {
			state.gisFileList = []
		}, 1000)
		return
	}
	const reader = new FileReader()
	reader.readAsText(file)
	state.mapJsonText = await new Promise((resolve, reject) => {
		reader.onload = (e) => {
			try {
				resolve(e.target.result)
			} catch (error) {
				reject(error)
			}
		}
	})
	try {
		const jsonData = JSON.parse(state.mapJsonText)
		if (checkGeoJson(jsonData)) {
			state.gisDisabled = true
			formState.geojson_name = file.name.split('.')[0]
		}
	} catch (error) {
		message.error(t('请上传正确的地理json文件'))
		setTimeout(() => {
			state.gisFileList = []
		}, 1000)
	}
}
const changeTc = async({ file, fileList }) => {
	if (fileList.length == 0) return
	const formdata = new FormData()
	formdata.append('file', file)
	state.loading = true
	UploadTempFile({}, formdata).then(res => {
		state.loading = false
		if (res.code == 1) {
			state.tcFilePath = res.file_path
		}
	})
	state.tcDisabled = true
}
const getFilePath = () => {
	if (state.tcFileList.length == 0) {
		if (state.type == 1) {
			return state.resultTcFile
		} else {
			return state.editTcFile
		}
	} else {
		return state.tcFilePath
	}
}
const confirmAdd = () => {
	if (formState.name == undefined) {
		return message.error(t('请输入文件名称') + '!')
	}
	basicApi({
		'import_string_func': 'teapgis:create_gis_file',
		'func_arg_dict': {
			'tg_file_name': formState.name,
			'tc_file_path': getFilePath(),
			'note': formState.note,
			'geojson_name': formState.geojson_name ? formState.geojson_name : '',
			'geojson_data': state.mapJsonText ? JSON.parse(state.mapJsonText) : {}
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			emit('confirm', res.func_result, 'create')
		}
	})
}
const initResult = () => {
	getTaskTableApi({}).then(res => {
		if (res.code == 1) {
			state.resultList = res.finished_data_list.filter(item => ![101, 102, 103, 104].includes(item.job_type_id))
		}
	})
}
onMounted(async() => {
	// initResult()
	state.editList = routeTabs.value.filter(item => {
		return item.type == 'isEditor' || item.type == 'firstSave'
	})
	basicApi({
		'import_string_func': 'teapgis:list_all_geojson_file',
		'func_arg_dict': {
			'tg_file_path': ''
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			state.gisOption = res.func_result.data.map(item => {
				return {
					label: item.name,
					value: item.name
				}
			})
		}
	})
})
</script>
<style lang="scss">
    .GisCreate{
        .ant-modal{
            width: auto!important;
        }
        .modal-content{
            padding: 20px 30px 70px;
            >div:first-child{
                display: flex;
                margin-bottom: 20px;
                >div{
                    width: 350px;
                }
                >div>p,.upload_div>p:first-child{
                    color: rgb(80, 79, 74);
                    font-size: 18px;
                    font-weight: 500;
                    line-height: 35px;
                    letter-spacing: 1px;
                }
                >div:first-child{
                    margin-right: 20px;
                }
                .ant-select{
                    width: 100%;
                }
                textarea{
                    height: 300px;
                    resize: none;
                }
                .upload_div{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .upload_p{
                        font-size: 16px;
                        color: rgb(54, 120, 191);
                        &:hover{
                            cursor: pointer;
                        }
                    }
                }
                .list{
                    >div:first-child{
                        display: flex;
                        border-radius: 12px 12px 0px 0px;
                        background: rgb(217, 217, 217);
                        height: 35px;
                        >p{
                            font-size: 16px;
                            line-height: 32px;
                            width: 50%;
                            text-align: center;
                            margin: 4px 4px 0!important;
                            border-radius: 12px 12px 0px 0px;
                            &:hover{
                                cursor: pointer;
                                color: rgb(54, 120, 191);
                                background: #fff;
                            }
                        }
                        .active{
                            color: rgb(54, 120, 191);
                            background: #fff;
                        }
                    }
                    .list_content{
                        height: calc(300px - 35px);
                        padding: 10px;
                        overflow-y: auto;
                        border: 1px solid rgb(196, 196, 196);
                        border-top: none;
                        border-radius: 0px 0px 12px 12px;
                       >p{
                            &:hover{
                                cursor: pointer;
                                color: rgb(54, 120, 191);
                            }
                       }
                       .active{
                            color: rgb(54, 120, 191);
                       }
                    }
                }
                .list_disabled{
                    pointer-events: none;
                    opacity: 0.5;
                }
                .file_p{
                    font-size: 16px;
                    span{
                        &:hover{
                            cursor: pointer;
                            color: red;
                        }
                    }
                }
            }
        }
    }
</style>
