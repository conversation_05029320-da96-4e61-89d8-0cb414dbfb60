<template>
    <a-modal wrapClassName="AddSimpleModal" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
            <div>
                <div class="modal_top">
                    <p>{{$t('新建')}}{{ props.title }}</p>
                    <close-outlined class="pointer" @click="emit('close')" />
                </div>
                <div class="modal-content">
                    <div class="form_content">
                        <a-form
                            ref="formRef"
                            :model="formState"
                        >
                            <div class="grid">
                                <a-form-item
                                    v-for="(item,index) in props.columns"
                                    :key="index"
                                    :rules="[{ required:true, message: $t('请输入')+item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,'') }]"
                                    :label="item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,'')"
                                    :name="item.field"
                                >
                                    <a-input-number v-if="item.cellDataType=='number'&&!item.col_source" :min="0" :controls="false"  v-model:value="formState[item.field]" >
                                        <template #addonAfter v-if="!(item.headerName.includes($t('率'))&&!item.headerName.includes($t('功率')))">
                                            <span>{{ getUnit(item.headerName) }}</span>
                                        </template>
                                    </a-input-number>
                                    <a-radio-group v-else-if="item.cellDataType=='boolean'" v-model:value="formState[item.field]" name="radioGroup">
                                        <a-radio :value="true">{{$t('是')}}</a-radio>
                                        <a-radio :value="false">{{$t('否')}}</a-radio>
                                    </a-radio-group>
                                    <div v-else-if="item.col_source">
                                        <a-select  v-model:value="formState[item.field]" :options="state.relationOption[item.col_source]||[]">

                                        </a-select>
                                    </div>
                                    <a-input disabled v-else-if="item.field=='type'" v-model:value="state.typeName"/>
                                    <a-input v-else-if="item.cellDataType=='text'" v-model:value="formState[item.field]"/>
                                </a-form-item>
                            </div>
                            <div>
                                <p class="text_remark">{{$t('注：其它数据请前往编辑器或生成器完善')}}</p>
                            </div>
                        </a-form>
                    </div>
                    <div class="modal_btn">
                        <a-button @click="confirm" type="primary">{{$t('确认')}}</a-button>
                        <a-button @click="emit('close')">{{$t('取消')}}</a-button>
                    </div>
                </div>
            </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
import { getUnit } from '@/utils/gis'
const props = defineProps({
	columns: {
		type: Array
	},
	type: {
		type: String
	},
	typeName: {
		type: String
	},
	types: {
		type: String
	},
	title: {
		type: String,
		default: ''
	}
})
const route = useRoute()
const state = reactive({
	ifShow: true,
	typeName: props.typeName,
	loading: false,
	relationOption: {}
})
const formRef = ref()
const formState = reactive({

})
const emit = defineEmits(['close', 'confirm'])
const confirm = () => {
	state.loading = true
	formRef.value.validate()
		.then(() => {
			basicApi({
				'import_string_func': 'teapgis:add_one_row_tc_data',
				'func_arg_dict': {
					'tc_file_path': route.query.filePath,
					'ele_name': props.type,
					'data': formState
				}
			}).then(res => {
				if (res.code == 1 && res.func_result.code == 1) {
					state.loading = false
					message.success(res.func_result.message)
					emit('confirm', {
						label: res.func_result.name,
						value: res.func_result.index
					})
				}
			}).catch(() => {
				state.loading = false
			})
		})
		.catch(error => {
			state.loading = false
			console.log('error', error)
		})
}
const closeModal = () => {
	emit('close')
}
onMounted(async() => {
	props.columns.forEach(item => {
		formState[item.field] = item.cellDataType == 'boolean' ? true : undefined
		if (item.field == 'type') {
			formState[item.field] = props.types
		}
	})
	basicApi({
		'import_string_func': 'teapgis:get_relation_sheet_index',
		'func_arg_dict': {
			'file_path': route.query.filePath,
			'ele_name': props.type
		}
	}).then(res => {
		state.relationOption = res.func_result.relation_sheet_index
	})
})
</script>
<style lang="scss">
    .AddSimpleModal {
        .ant-modal{
            width: auto!important;
            .modal-content{
                min-width: 800px;
                padding: 0 0 70px;
                .form_content{
                    padding: 30px 80px 0;
                    .grid{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        grid-column-gap: 40px;
                    }
                    .display{
                        display: flex;
                    }
                    .ant-input{
                        width: 200px;
                    }
                    .ant-input-number-wrapper{
                        width: 200px;
                        .ant-input-number-group-addon{
                            width: 40px;
                        }
                    }
                    .ant-form-item-control-input-content>.ant-input-number{
                        width: 200px;
                    }
                    .ant-select{
                        width: 200px;
                    }
                    .ant-form-item-label{
                        // text-align: left;
                        width: 200px;
                        label{
                            font-size: 18px;
                        }
                    }
                    .line_type_data{
                        padding:0 20px;
                        .ant-input-number-wrapper{
                            width: 160px;
                        }
                        .ant-select{
                            width: 160px;
                        }
                        >div{
                            display: grid;
                            position: relative;
                            grid-template-columns: 1fr 1fr;
                            grid-gap: 20px;
                            padding:0 0 20px;
                            >div{
                                display: flex;
                                align-items: center;
                                >p{
                                    margin-right: 10px!important;
                                }
                            }
                            .pointer{
                                font-size: 20px;
                                position: absolute;
                                right: -20px;
                                top: 5px;
                                opacity: 0.8;
                                &:hover{
                                    opacity: 1;
                                }
                                color: rgb(228, 51, 51);
                            }
                        }
                    }
                    .add_lines{
                        width: 90px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 20px;
                        color: rgb(71, 71, 71);
                        font-family: 'SiYuan Medium',Serif;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        height: 28px;
                        line-height: 28px;
                        text-align: center;
                        opacity: 0.8;
                        >span{
                            color: rgb(35, 137, 230);
                        }
                        &:hover{
                            cursor: pointer;
                            opacity: 1;
                        }
                        >span{
                            font-size: 16px;
                            margin-right: 5px;
                        }
                    }
                    .text_remark{
                        color: rgb(163, 0, 20);
                        font-weight: bolder;
                        font-size: 18px;
                    }
                }
            }
        }
    }
</style>
