import { defineStore } from 'pinia'
// import { select_func_option } from '@/config/setting.config.js'
export const settingStore = defineStore({
	id: 'settingState',
	state: () => ({
		fontSize: localStorage.getItem('fontSize') ? localStorage.getItem('fontSize') : '0',
		agFontSize: localStorage.getItem('agFontSize') ? localStorage.getItem('agFontSize') : '14px',
		color: localStorage.getItem('color') ? localStorage.getItem('color') : '#3678BF',
		colorRGB: localStorage.getItem('colorRGB') ? localStorage.getItem('colorRGB') : '54, 120, 191',
		app_title: '',
		appTheme: '',
		solverType: 'Tj_Solver',
		tableType: 'task',
		startingCase: '',
		waitingCase: '',
		waitingCaseList: [],
		jobExecPercent: null,
		add_watermark: false,
		tableShow: false,
		watermark_text: '',
		isNotify: false,
		taskName: '',
		tscanShow: false,
		isChromeHigh: true,
		taskUrl: '',
		recommended_parallel_number: 0,
		sim_job_config: {},
		systemInfo: {
			cpu_vendor_info: undefined,
			app_version: undefined,
			os_version_info: undefined,
			virtual_memory: undefined,
			physical_cpu_count: undefined,
			logical_cpu_count: undefined
		},
		wsAcceptType: {
			show_error_log: true,
			show_info_log: true,
			show_warning_log: true
		},
		permissionList: [], // 权限列表
		provinceList: [],
		cityObj: [],
		isDebug: false // 是否是debug模式
	}),
	actions: {
		changeWatermark(add_watermark, watermark_text) {
			this.add_watermark = add_watermark
			this.watermark_text = watermark_text
		},
		changeAreaList(data) {
			this.provinceList = data
			const obj = {}
			data.forEach((item) => {
				obj[item.label] = item.children ? item.children : []
			})
			this.cityObj = obj
		},
		changeWsAcceptType(error, info, warning) {
			this.wsAcceptType.show_error_log = error
			this.wsAcceptType.show_info_log = info
			this.wsAcceptType.show_warning_log = warning
		},
		changeNotify(val) {
			this.isNotify = val
		},
		changeSystem(cpu_vendor_info, app_version, os_version_info, virtual_memory, physical_cpu_count, logical_cpu_count) {
			this.systemInfo = {
				cpu_vendor_info, app_version, os_version_info, virtual_memory, physical_cpu_count, logical_cpu_count
			}
		},
		changeAgFontSize(val) {
			this.agFontSize = val
		},
		changeDebugMode(val) {
			this.isDebug = val
		},
		changeAppTitle(val) {
			this.app_title = val
		},
		changeAppTheme(val) {
			this.appTheme = val
		},
		getPermissionList(data) {
			this.permissionList = data
		},
		getTaskFile(data) {
			this.startingCase = data.calculating_task_name
			this.waitingCase = data.latest_waiting_task_count
			this.waitingCaseList = data.latest_waiting_task_names
			this.jobExecPercent = data.calculating_task_exec_percent
		},
		changeRecommendedParallel(val) {
			this.recommended_parallel_number = val
		},
		getSimJobConfig(data) {
			this.sim_job_config = data
		}
	}
})
