import { defineStore } from 'pinia'
export const routeStore = defineStore({
	id: 'routeStore',
	state: () => ({
		routeCache: localStorage.getItem('routeCache') ? JSON.parse(localStorage.getItem('routeCache')) : [],
		routeTabs: localStorage.getItem('routeTabs') ? JSON.parse(localStorage.getItem('routeTabs')) : [],
		activeKey: localStorage.getItem('activeKey') ? localStorage.getItem('activeKey') : '',
		routerRefresh: true
	}),
	actions: {
		addTabs(val) {
			if (!this.routeCache.find(item => item == val.name)) {
				this.routeCache.push(val.name)
			}
			this.routeTabs.push(val)
			this.activeKey = val.key
		},
		setActive(val) {
			this.activeKey = val
		},
		setTabs(targetKey, bool) {
			this.routeTabs.forEach(item => {
				if (item.key == targetKey) {
					item.isUnsaved = bool
				}
			})
		},
		setSaveTabs(targetKey, bool) {
			this.routeTabs.forEach(item => {
				if (item.key == targetKey) {
					item.isSaved = bool
				}
			})
		},
		setTabsModalVisible(targetKey, bool) {
			this.routeTabs.forEach(item => {
				if (item.key == targetKey) {
					item.isModalVisible = bool
				}
			})
		},
		setTreeNode(targetKey, tree) {
			this.routeTabs.forEach(item => {
				if (item.key == targetKey) {
					item.treeNode = tree
				}
			})
		},
		draggleSortTabs(val) {
			this.routeTabs = val
		}
	}
})
