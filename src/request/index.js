import axios from 'axios'
import message from '@/utils/message'
import network from '@/config/teap.config'
import { storeToRefs } from 'pinia'
import { loadingStore } from '@/store/loadingStore'
/* eslint-disable no-unused-vars */
import { routeStore } from '@/store/routeStore'
import router from '@/router/index'
import { basicApi } from '@/api/exampleApi'
import { throttle } from '@/utils/gis'
import { t } from '@/utils/common'
const cancelToken = axios.CancelToken
// 请求拦截器
const request = axios.create({
	baseURL: network.baseURL,
	// timeout: network.requestTimeout,
	headers: {
		'Content-Type': network.contentType
	}
})
const handleLostFile = (url, response) => {
	url == '/backend/teap_api/' ? message.error(response.data.message || t('请求出错，请重试'), 0) : message.error(response.data.message || t('请求出错，请重试'))
	const storeRoute = routeStore()
	const { routeTabs, activeKey, routeCache } = storeToRefs(storeRoute)
	basicApi({
		'import_string_func': 'teapcase:check_file_is_exist',
		'func_arg_dict': {
			'file_names': routeTabs.value.map(item => item.filePath)
		}
	}).then(res => {
		if (res.code === 1) {
			routeTabs.value = routeTabs.value.filter(item => res.func_result.includes(item.filePath))
			if (routeTabs.value.length === 0) {
				router.push('/')
				routeCache.value = []
				activeKey.value = ''
			} else {
				activeKey.value = routeTabs.value[0].key
				router.push(activeKey.value)
			}
		}
	})
}
const throttledHandleLostFile = throttle(handleLostFile, 2000)
request.interceptors.request.use(
	(config) => {
		const storeLoading = loadingStore()
		const { loadingTimeData, loadingTime } = storeToRefs(storeLoading)
		config.metadata = { startTime: new Date() }
		if (loadingTimeData.value[config.url] && config.isShowLoading) {
			loadingTime.value = loadingTimeData.value[config.url]
		}
		if (config.isShowLoading) {
			storeLoading.showModal()
		}
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)
// 响应拦截器
request.interceptors.response.use(
	(response) => {
		const storeLoading = loadingStore()
		const { loadingTimeData } = storeToRefs(storeLoading)
		response.config.metadata.endTime = new Date()
		loadingTimeData.value[response.config.url] = (response.config.metadata.endTime - response.config.metadata.startTime) / 1000
		const config = response.config
		const url = config.url

		// 特殊接口处理
		if (url.indexOf('topo_graph') > -1) return response
		if (url.indexOf('get_task_detail_result') > -1) return response.data // 功率曲线接口，接口调通后需要删除次行
		if (network.successCode.includes(response.data.code)) {
			return response.data
		} else if (network.errorCode.includes(response.data.code)) {
			if (response.data.error_code && response.data.error_code === network.lostFileCode) {
				throttledHandleLostFile(url, response)
			} else {
				url == '/backend/teap_api/' ? message.error(response.data.message || t('请求出错，请重试'), 0) : message.error(response.data.message || t('请求出错，请重试'))
			}
			return Promise.reject(new Error(response.data.message || 'Error'))
		} else {
			return response
		}
	},
	(error) => {
		// if (error.message.includes('timeout')) {
		// 	message.error('请求超时')
		// } else {
		// 	// console.log(response.data.message)
		// }
		if (error.code == 'ERR_CANCELED') {
			return
		}
		message.error(t('请求出错，请重试'))
		return Promise.reject(error)
	}
)
export { request, cancelToken }
