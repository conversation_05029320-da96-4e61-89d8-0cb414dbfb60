.ant-modal {
  max-width: 100000px;
  overflow: hidden !important;

  .ant-modal-content {
    padding: 0px !important;
  }
}

.ant-modal-confirm {
  .ant-modal-content {
    padding: 15px !important;
  }
}

.ant-table {
  .ant-table-body {
    overflow-y: overlay !important;
    overflow-x: auto !important;
  }

  .ant-table-placeholder {
    td {
      overflow: hidden;
    }
  }
}

.ant-spin {
  max-height: 1080px !important;
}

.error-modal .ant-modal-body {
  padding: 20px 20px 20px !important;
}

.confirm-modal .ant-modal-body {
  padding: 20px !important;
}

.gis-delete-modal {
  width: 448px !important;
  height: 210px;

  .ant-modal-confirm-body {
    display: flex;
    flex-direction: column;
    justify-content: center;


    >span:first-child {
      font-size: 36px;
      margin-inline-end: 0;

      >svg path:first-child {
        fill: rgb(253, 115, 132);
      }

      >svg path:nth-child(2) {
        fill: rgb(253, 115, 132);
      }

      >svg path:last-child {
        fill: #fff;
      }
    }

    .ant-modal-confirm-title {
      color: rgb(71, 71, 71);
      font-size: 16px;
      font-weight: bolder;
      letter-spacing: 0px;
      line-height: 50px;
    }

    .ant-modal-confirm-content {
      margin-inline-start: 0 !important;
      margin-block-start: 0 !important;

      >div {
        color: rgb(71, 71, 71);

        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
      }
    }
  }

  .ant-modal-confirm-btns {
    text-align: center;

    button {
      width: 92px;
    }

    >button:last-child {
      background-color: rgb(227, 46, 46);
    }
  }
}

.ant-btn[disabled],
.ant-btn[disabled]:hover,
.ant-btn[disabled]:focus,
.ant-btn[disabled]:active {
  // background: #f5f5f5 !important;
  // color: rgba(0, 0, 0, 0.25) !important;
  opacity: 0.5;
}

.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-nested-loading .ant-spin-container {
  height: 100%;
}