body {
  margin: 0;
  transform-origin: 0 0;
}

*,
*:before,
*:after {
  box-sizing: border-box
}

html,
body {
  height: 100%;
  //  background-color: #b12791;
}

#app {
  height: 100%;
  overflow-y: overlay;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: skyblue;
  border-radius: 10px;
  background-image: -webkit-linear-gradient(45deg,
      rgba(255, 255, 255, 0.2) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      transparent 75%,
      transparent);
}

::-webkit-scrollbar-track {
  background-color: #ccc;
  border-radius: 10px;
}

p {
  margin: 0 !important;
}

.select_switch {
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 100%;
  height: 32px;
  box-sizing: border-box;
  border: 1px solid #007bff;
  border-radius: 5px;
  overflow: hidden;
  // background: #000000;  
  -moz-user-select: none;
  /*火狐*/
  -webkit-user-select: none;
  /*webkit浏览器*/
  -ms-user-select: none;
  /*IE10*/
  user-select: none;

  /*选中文字时避免出现蓝色背景*/
  >div {
    color: #6c757d;
    text-align: center;
    line-height: 32px;
    font-weight: bolder;

    &:hover {
      cursor: pointer;
    }
  }

  .active {
    background: #007bff;
    color: #fff;
  }

  .a_left {
    animation: move 0.6s;

    @keyframes move {
      from {
        transform: translatex(100%);
      }

      to {
        transform: translatex(0);
      }
    }
  }

  .a_right {
    animation: moves 0.6s;

    @keyframes moves {
      from {
        transform: translatex(-100%);
      }

      to {
        transform: translatex(0);
      }
    }
  }
}



.grayscale {
  filter: grayscale(1);
  pointer-events: none;
}

.border-transparent {
  border-color: transparent !important;
}

.bolder {
  font-weight: bolder;
}

.grid {
  display: grid;
}

.flex {
  display: flex;
}

.scroll {
  overflow-y: auto;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
}

.space-between {
  display: flex;
  justify-content: space-between;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.justify-content-center {
  display: flex;
  justify-content: center;
}

.align-items-center {
  display: flex;
  align-items: center;
}

.wrap {
  display: flex;
  flex-wrap: wrap;
}

.flex-direction {
  display: flex;
  flex-direction: column;
}

.space-around {
  display: flex;
  justify-content: space-around;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute !important;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis; // 显示省略符号来代表被修剪的文本。
  white-space: nowrap; // white-space 
  text-align: left;
}

.ellipsis-2 {
  text-align: center;
  line-height: 24px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.pointer {
  &:hover {
    cursor: pointer;
  }
}

.not-allowed {
  &:hover {
    cursor: not-allowed;
  }
}

.underline {
  &:hover {
    cursor: pointer;
    color: #007bff;
    text-decoration: underline;
  }
}

.user-select {
  -moz-user-select: none;
  /*火狐*/
  -webkit-user-select: none;
  /*webkit浏览器*/
  -ms-user-select: none;
  /*IE10*/
  user-select: none;
  /*选中文字时避免出现蓝色背景*/
}

.auto {
  overflow-y: auto;
}

.modal_top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px 0 20px;
  border-bottom: 1px solid var(--base-color);

  >p {
    color: var(--base-color);
    font-size: 20px;
    line-height: 50px;
  }

  >span {
    font-size: 20px;
    background-color: rgb(244, 244, 244);
    padding: 5px;
    border-radius: 5px;
  }
}

.modal_btn {
  position: absolute;
  z-index: 3;
  right: 30px;
  bottom: 28px;

  button {
    min-width: 130px;
    margin-left: 30px;
    height: 38px;
    font-size: 16px;
    letter-spacing: 0px;
  }

  button:last-child {
    border-color: var(--base-color);
    color: var(--base-color);
  }
}

.echarts-btn {
  color: #fff;
  background-color: var(--base-color);
  box-shadow: 0 2px 0 rgba(5, 165, 255, 0.1);
  font-size: 14px;
  height: 30px;
  padding: 0px 15px;
  border-radius: 6px;
  font-weight: 400;
  text-align: center;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  margin-top: 5px;
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0
  }

  to {
    background-position: 0 0
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0
  }

  to {
    background-position: 0 0
  }
}

.progress_active_name {
  height: 20px;
  width: 50px;
  line-height: 20px;
  border-radius: 5px;
  background-color: #27b148;

  span {
    color: #fff;
  }

  animation: progress-bar-stripes 2s linear infinite;
  background-size: 1rem 1rem;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}

.TEAP-preview-main {
  position: absolute;
  background-color: #f2f7fd;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  overflow: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  &.nohead {
    padding-top: 10px;
  }
}

.TEAP-preview-gis {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  overflow: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  &.nohead {
    padding-top: 10px;
  }
}

.gis-result-tooltip-two,
.gis-result-tooltip-three {
  >p {
    font-size: 14px;
    text-align: center;
    font-weight: bolder;
  }
}

.gis-result-tooltip-one {
  padding: 0px;

  >p {
    font-size: 14px;
    line-height: 24px;
    font-weight: bolder;
  }

  >div {
    p {
      font-size: 12px;
      line-height: 18px;
      display: flex;
      justify-content: space-between;
    }

  }
}

.gis-result-tooltip-two {
  >div {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 1fr;

    p {
      text-align: center;
      font-size: 12px;
      line-height: 16px;
    }
  }

  >div:first-child>p {
    line-height: 18px;
    font-weight: bolder;
  }
}

.gis-result-tooltip-three {
  >div {
    width: 200px;

    p {
      display: flex;
      justify-content: space-between;
    }
  }
}

@-webkit-keyframes shakeX {

  0%,
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0)
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0)
  }
}

@keyframes shakeX {

  0%,
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0)
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0)
  }
}

.animate__shakeX {
  -webkit-animation-name: shakeX;
  animation-name: shakeX
}