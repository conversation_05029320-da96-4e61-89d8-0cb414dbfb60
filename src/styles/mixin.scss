
@mixin bg($src, $size: cover,$position:center){
    background-image: url($src);
    background-size: $size;
    background-position: $position;
    background-repeat: no-repeat;
}

//字体大小（原始，增量）
@mixin add-size($val, $size) {
  font-size: $val+$size !important;
  [data-size="0"] & {
    font-size: $val+$size !important; //小号，增量0
  }
  [data-size="1"] & {
    font-size: $val+$size-2 !important; //中号，增量2
  }
  [data-size="2"] & {
    font-size: $val+$size-4 !important; //大号，增量4
  }
}

// AG-Grid 字体大小（原始，增量）
@mixin add-ag-size($val, $size) {
  --ag-font-size: $val+$size;
  [data-size="0"] & {
    --ag-font-size: $val+$size; //小号，增量0
  }
  [data-size="1"] & {
    --ag-font-size: $val+$size-2; //中号，增量2
  }
  [data-size="2"] & {
    --ag-font-size: $val+$size-4; //大号，增量4
  }
}
