// import { createApp } from 'vue'

import elScrollLoadmore from './loadmore'

const install = function(Vue) {
	Vue.directive('el-scroll-loadmore', elScrollLoadmore)
}

if (window.Vue) {
	window['el-scroll-loadmore'] = elScrollLoadmore
  Vue.use(install); // eslint-disable-line
}

elScrollLoadmore.install = install
export default elScrollLoadmore

// app.directive('position', (el, binding) => {
// 	el.style[binding.arg] = binding.value + 'px'
// })
