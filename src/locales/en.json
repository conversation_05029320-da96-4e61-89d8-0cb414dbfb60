{"TEAP新型电力系统规划仿真平台": "Power System Simulation Platform", "文件": "File", "工具": "Tools", "视图": "Views", "仿真计算场景": "Simulations", "仿真设置": "Settings", "应用场景": "Applications", "开始": "Start", "仿真计算": "Simulation", "应用": "Application", "其他": "Others", "结果查看": "ResultView", "新建": "New", "新建tc文件": "tc file", "新建tg文件": "tg file", "未命名": "Unnamed", "打开": "Open", "tg导入": "tg file", "tc导入": "tc file", "Excel导入": "Excel", "BPA导入": "BPA", "保存": "Save", "另存为tc文件": "as tc", "另存为Excel文件": "as Excel", "另存为yml文件": "as yml", "另存为v2版本xlsx": "as v2 xlsx", "算例拼合": "<PERSON><PERSON>", "算例拆分": "Split", "重置": "Reset", "撤销": "Undo", "恢复": "Redo", "复制": "Copy", "粘贴": "Paste", "剪切": "Cut", "新增行": "Append line", "删除行": "Delete line", "清除筛选": "Remove filtering", "表格交互": "Excel", "当前表导出": "Export current", "当前表导入": "Import current", "批量处理": "<PERSON><PERSON>", "计算": "Start", "替换": "Replace", "透视图修改": "Modify pivot", "参数补充": "Fill properties", "参数管理": "Manage defaults", "恢复默认": "Reset to defaults", "上传时序": "Upload timeseries", "智能关联": "Associations", "类型转换": "Switch type", "逐项视图": "Item-by-item", "GIS视图": "GIS", "曲线预览": "<PERSON><PERSON><PERSON>", "信息统计": "Statistics", "装机统计": "Capacities", "检修容量统计": "Maintenance", "字段设置": "Config", "算例数据": "Case", "仿真计算参数": "Simulation", "协同规划": "Planning", "时序模拟": "Chronological", "滚动计算": "Rolling", "求解器": "Solver", "任务列表": "TaskList", "负荷生成": "Load profiles", "风电生成": "Wind profiles", "光伏生成": "PV profiles", "检修生成": "Maintenance", "系统设置": "Settings", "系统帮助": "Help", "请设置算例时间范围": "Set case time range", "时间范围": "Time range", "时间间隔": "Step size", "确认": "Comfirm", "取消": "Cancel", "计算中": "Running", "等待中": "Waiting", "计算任务列表": "Ongoing", "结果查询列表": "Finished", "名称": "Name", "类型": "Type", "完成时间": "Finish time", "备注": "Remarks", "中长期电力电量平衡": "Chronological Simulation", "源网荷储协同规划": "Co-optimizition Planning", "计算场景": "<PERSON><PERSON><PERSON>", "滚动模拟": "Rolling Simulation", "参数设置": "Parameters", "旋转备用率": "Spinning reserve", "停机备用率": "Emergency reserve", "仿真时间": "Simulation time range", "仿真步长": "Timestep", "场景选择": "<PERSON><PERSON><PERSON>", "水电场景": "Hydro Scenario", "时序削减方式": "Method-specific settings", "求解方法": "Method", "是否分区平衡": "Zonal balance", "周期颗粒度": "Period gran", "周期内分段数": "Segments", "拆分并行加速": "Parallel Acc", "计算前自动安排检修": "A<PERSON><PERSON> maint first", "案例备注": "Remarks", "专业参数设置": "Advanced Parameters", "负荷松弛惩罚(元/MWh)": "Load relax penalty (USD/MWh)", "最大缺电功率惩罚(元/MWh)": "Max relax penalty (USD/MWh)", "备用松弛惩罚(元/MWh)": "Reserve relax penalty (USD/MWh)", "最大备用松弛惩罚(元/MWh)": "Max rsv relax penalty (USD/MWh)", "弃风惩罚(元/MWh)": "Wind curt penalty (USD/MWh)", "弃光惩罚(元/MWh)": "Solar curt penalty (USD/MWh)", "弃水惩罚(元/MWh)": "Water curt penalty (USD/MWh)", "支路过载惩罚(元/MWh)": "Branch overload penalty (USD/MWh)", "停备松弛惩罚为旋备的倍数": "Emergency/Spinning reserve ratio", "储能充放状态二次优化": "Reoptimize storage state", "电池充放电状态统一": "Same battery state", "抽蓄抽水出力状态统一": "Same pumped hydro state", "抽蓄、电池周期首末时刻SOC一致": "Same SOC at start/end of period", "储能聚合": "Aggregate storage", "需求侧响应是否影响备用需求": "Consider DR in reserve", "计算时间上限": "Timelimit", "储能提供备用的模式": "Storage provides reserve", "水电提供备用模式": "Hydropower provides reserve", "停备需求计算模式": "Emergency reserve", "储能按容量百分比投运": "Storage commissionned by percentage", "待规划设备最短投产时间": "Minimium commissionned time", "新能源最低消纳率(<=1)": "Minimium renewable accommodation rate", "上备用率": "Spinning reserve rate", "下备用率": "Downward reserve rate", "风电备用率": "Wind reserve rate", "光伏备用率": "Solar reserve rate", "滚动计算设置": "Rolling Simulation Settings", "单次滚动小时数": "Timestep per roll", "前瞻小时数": "Lookahead", "时序最大分段数": "Max segments", "启停约束": "Start/shut constraints", "爬坡约束": "Ramp constraints", "备用约束": "Reserve constraints", "网架约束": "Branch constraints", "断面约束": "Interface constraints", "考虑网损": "Grid loss", "模型选择": "Objective", "分段首时刻无启停成本": "No start/shut cost at first timestep", "指定出力机组必开": "Generators with fixed output must online", "时序分解重叠天数": "Overlap days", "并行计算进程数": "Processes", "最大回滚次数": "Max rollbacks", "单次默认求解时间上限": "Default timelimit", "单次最大求解时间上限": "<PERSON> timelimit", "仅保存": "Save only", "开始计算": "Start", "停止计算": "Stop", "删除": "Delete", "求解器设置": "<PERSON><PERSON>", "预测条件设置": "Forecast inputs", "预测年份": "Year", "预测区域": "Area", "历史参考": "Historical ref", "极端系数": "Extreme event factor", "参数配置": "Parameters", "可选": "Optional", "全年最大负荷": "Max load", "万千瓦": "MW", "全年总用电量": "Yearly consumption", "总量": "All sectors", "分行业电量": "Sectoral consumption", "历史负荷管理": "Manage historical", "模型参数管理": "Manage model", "关联负荷": "Associate with case", "全选": "Select all", "已选择": "Selected", "搜索": "Search", "请输入": "Input", "请选择": "Select", "选择区域": "Choose area", "数据设置": "Data", "年份": "Year", "用电量": "Consumption", "负荷曲线": "Curve", "第一产业用电量": "Primary sector", "第二产业用电量": "Secondary sector", "第三产业用电量": "Tertiary sector", "城乡居民生活用电量": "Domestic consumption", "亿千瓦时": "GWh", "时序负荷曲线": "Timeseries", "点击上传": "Upload", "模型可调参数": "Model parameters", "树的数量": "Num of trees", "树的最大深度": "Max depth", "分裂节点所需最小样本数": "Min samples per node", "叶节点所需最小样本数": "Min samples per leaf", "热启动开关": "Warm start", "开": "Yes", "关": "No", "是": "Yes", "否": "No", "省/直辖市": "Region", "区域": "Area", "经纬度": "Coordinates", "市": "City", "年目标利用小时数": "Full load hours", "关联场站": "Associated station", "检修安排": "Maintenance schedule", "年内检修安排": "Within Year", "跨年检修安排": "Inter Year", "类别": "Type", "可同时检修最大机组的数目": "Max units in maint", "可同时检修最大机组的最大容量": "Max capacity in maint", "强制检修": "Forced", "根据多年检修计划安排年内检修次数和天数": "Consider longer horizon", "检修计划上传": "Upload", "选择生成方式": "Results", "生成并下载": "Download", "生成并插入算例": "Insert into case", "直接插入算例": "Insert only", "系统信息": "Hardware", "操作系统": "OS", "处理器": "CPU", "物理内存": "RAM", "软件版本": "Version", "功能管理": "Functions", "输出日志": "Logs", "选择日志输出规则": "Log levels", "下载日志": "Download logs", "字号设置": "Font size", "小": "Small", "中": "Medium", "大": "Large", "完成通知": "Notify when task finishes", "自定义设置": "Customize", "工作位置图": "Balance chart", "单位选择": "Units", "¥/万千瓦时": "USD/MWh", "万千瓦时": "MWh", "示例图": "Preview", "面积图": "Area chart", "曲线图": "Curve chart", "功率": "Power", "下载模版": "Download template", "上传模版": "Upload template", "时间": "Time", "清空": "Clear", "替换为": "Replace with", "请输入将要筛选的内容": "Find", "请输入将要替换的内容": "Replace", "一级类别": "Level 1", "二级类别": "Level 2", "三级类别": "Level 3", "统计参数": "Statistics", "处理方式": "Processing method", "查询": "Query", "修改": "Change", "设备选择": "Component", "地图": "Map", "元件": "Component", "显示场站名称": "Show names", "网侧元件库": "Grid side library", "变电站": "Transformer", "电力流": "<PERSON><PERSON><PERSON>", "通道": "Line", "储能元件库": "Storage library", "按名字及备注匹配": "Match by name and remark", "按备注匹配": "Match by remark", "最大容量": "Max capacity", "数量": "Quantity", "曲线名称": "Curve name", "搜索曲线名称": "Search", "曲线类型": "Type", "最大值": "Max", "最小值": "Min", "总和": "Sum", "操作": "Details", "暂无数据": "No data", "请选转换类型": "Conversion type", "主要参数": "Main properties", "其他参数": "Secondary properties", "关联时序": "Time series Association", "新增": "New", "关联": "Associate", "范围选择": "Range", "自定义": "Customize", "单位": "Unit", "统计图": "Statistical charts", "统计表": "Statistical tables", "编辑器": "Editor", "字段管理": "Config", "选择显示字段": "Show fields", "设备全选": "All components", "属性全选": "All properties", "存量设备全选": "All existing", "待规划设备全选": "All plan pool", "打开算例": "Open case", "下载案例": "Download case", "打开结果": "Open result", "平衡表": "Balance sheets", "工作位置表": "Balance charts", "曲线": "<PERSON><PERSON><PERSON>", "统计图表": "Statistics", "计算参数": "Simulation parameters", "成本": "Costs", "请选择分区": "Zone", "月度电力平衡表": "Monthly power balance", "月度电量平衡表": "Monthly el balance", "重新生成": "Regenerate", "自定义盈亏时刻": "Choose control snapshot", "全时序电力平衡表": "Full range power balance", "置信度分析": "Confidence analysis", "数据选择": "Select curves", "下载数据": "Download data", "全选默认": "Select defaults", "全选所有": "Select all", "全不选": "Select none", "反选": "Select reverse", "保存为图片": "Save as image", "刷新": "Refresh", "选择分区": "Zones", "选择类型": "Components", "可选曲线": "<PERSON><PERSON><PERSON>", "请输入曲线名称": "Filter", "已选曲线": "Selected curves", "全部清除": "Clear", "发电装机组成": "Install Capacities", "发电量组成": "Generation Mix", "新能源消纳情况": "Renewable Statistics", "发电利用小时数": "Full load hours", "算例信息": "Case information", "通用设置": "General parameters", "仿真日志": "Logs", "成本统计表": "Cost table", "分区成本与惩罚": "Zonal costs and penalties", "联络线成本与惩罚": "Interconnection costs and penalties", "下载结果": "Download", "查看参数": "Simulation parameters", "下载算例": "Download case", "请输入名称或备注": "Name or remarks", "时序曲线": "Timeseries", "本表类型": "Type", "按节点拆分": "Split by bus", "按分区拆分": "Split by zone", "BPA文件解析": "Parse", "BPA文件": "BPA file", "选择文件": "Upload", "解析配置": "Parse config", "请选择默认配置": "<PERSON><PERSON><PERSON>", "配置管理": "Config", "开始解析": "Start", "BPA解析日志": "Log", "智能匹配": "Match", "请填写需要解析的分区，以回车或逗号隔开": "Zone names", "开始匹配": "Start matching", "可选分区": "Avaliable zones", "已选分区": "Selected zones", "请输入搜索内容": "Filter", "插入机组默认参数": "Fill with defaults", "插入示例时序曲线": "Insert temp time series", "日志下载": "Download logs", "新增配置": "New config", "上传配置文件": "Upload config", "分区配置": "Config zones", "关键词配置": "Keywords", "火电": "Coal", "燃气": "Gas", "水电": "Hydro", "核电": "Nuclear", "抽蓄": "PumpedHydro", "储能": "Storage", "火 电": "Coal", "燃 气": "Gas", "水 电": "Hydro", "核 电": "Nuclear", "抽 蓄": "PumpedHydro", "储 能": "Storage", "燃煤": "Coal", "生物质": "Biomass", "风电": "Wind", "光伏": "Solar", "风 电": "Wind", "光 伏": "Solar", "总计": "Sum", "是否考虑负荷有功PZ卡的调节系数": "Consider active load PZ card factors", "考虑负荷无功PZ卡的调节系数": "Consider reactive load PZ card factors", "是否考虑发电有功PZ卡的调节系数": "Consider active generator PZ card factors", "是否考虑发电无功PZ卡的调节系数": "Consider reactive generator PZ card factors", "无": "None", "个算例": " cases", "是针对当前表格、还是全部表格": "Current table or all tables", "提示": "Note", "当前表格": "Current table", "全部表格": "All tables", "算例文件存在还未保存的改动，是否保存并开始仿真": "Not saved, save and start?", "分钟": "Minutes", "小时": "Hours", "天": "Days", "绑定情况检查": "Check Associations", "被多个设备绑定": "Reference by multiple components", "未被绑定": "Not associated", "周度": "Week", "月度": "Month", "季度": "Season", "自动设置": "AUTO", "指定值": "Value", "负荷持续时间分段时序削减": "Load Duration Curve Reduction", "变步长时序聚合": "Step-variant Aggregation", "快速粗略": "Coarse and fast", "一般居中": "Medium", "慢速精准": "Fine and slow", "储能不提供备用": "Provide no reserve", "以时刻SOC为上限提供备用": "SOC as upper limit", "以周内最低SOC为上限提供备用": "Weekly minimum SOC limit", "水电弃水时提供备用": "when curtails", "水电开机时即可提供备用": "when online", "基于周期内最大负荷": "Based on max period load", "基于当前时刻负荷": "Based on snap load", "开始算例": "Start", "删除算例": "Delete", "移动算例": "Move", "停止算例": "Stop", "置顶算例": "Top", "负荷上调量上限百分比": "Load increase limit", "全社会运行成本最低": "Minimize total cost", "显示所有": "Show all", "关联储能": "Link storage", "关联水库": "Link reservoir", "所在节点": "Bus", "所在城市": "City", "类型数据源": "Source", "显示形式": "Format", "24列": "24 Col", "单列": "1 Col", "新增时序": "New", "逐时数据": "Hourly", "日×24时数据": "D x 24h", "月×24时数据": "M x 24h", "年×12月数据": "Y x 12m", "自定义时间段": "Customize", "时序类型": "Type", "数据类型": "Operation", "时间周期": "Period", "添加站点": "Add Station", "激活软件证书": "Activate license", "授权地址": "URL", "授权码": "Token", "激活": "Activate", "软件证书激活成功": "Success", "证书有效时间": "Valid to", "可用模块": "Licensed modules", "日": "Day", "周": "Week", "月": "Month", "修改总和": "Change Sum", "统一赋值": "Set all", "所属区域": "Region", "所属分区": "Zone", "所有者": "Owner", "实际电容电抗器文件": "actual capacitor reactance file", "上传文件": "Upload", "用户登录": "<PERSON><PERSON>", "修改密码": "Change Password", "备用率": "Reserve rate", "考虑规划机组": "Include planned generators", "需求侧响应率": "Demand response rate", "计及下半年投产机组": "Include H2 generators", "负荷平衡系数": "Load balance factor", "电源出力系数表": "Generator output factor table", "置信度曲线": "Confidence Curve", "负荷持续时间曲线分段法设置": "Load duration curve reduction", "计算平台版本号": "Version", "算例计算完成时间": "Finish time", "算例计算总耗时": "Time spent", "计算类型": "Type", "预测成因分析": "Forecast analysis", "最高气温": "Max", "最低气温": "Min", "节假日": "Holidays", "工作日": "Weekdays", "保存至时序表": "Save to case", "保存至本地": "Save to local", "返回": "Return", "理论发电量": "Theorical generation", "实际发电量": "Actual generation", "弃电量": "Curtailment", "消纳率": "Accommodation", "新能源": "Renewable", "边界机组利用小时": "Existing generator full load hours", "电源类型": "Type", "线路型号管理": "Interconnection management", "版本已经上线，点击下载更新": "Update available", "版本已经下载完成，点击安装更新": "Install update", "更新包下载中": "Downloading update", "安装包下载完成，是否立即更新？": "Download complete, install update?", "共": "Total", "项": "items", "台": "units", "拓扑匹配": "Topology Matching", "文件选择": "File", "历史选择": "Historical", "分区": "Zone", "蒙特卡罗随机抽样算法": "Monte Carlo", "跨年检修算法": "Inter-year", "仅对应所选场景": "This scenario only", "光伏场站": "Solar", "风电场站": "Wind", "0～100，极端系数反映全年各时段发电量的大小，趋于0为极端小，趋于100为极端大": "Extreme coefficient reflects the size of the generation.", "纬度": "Lat", "经度": "Lon", "保存算例": "Save", "文件名称": "Filename", "可拖拽调整位置": "Drag", "工作位置图设置": "Config balance chart", "时序导入": "Import timeseries", "导入文件": "Import file", "点击上传时序曲线": "Upload timeseries", "请在模板文件内修改": "Use the template", "下载摸板": "Download template", "碳排放最低": "Minimize carbon emissions", "另存为tg": "Save as tg", "导出PNG": "Export as PNG", "导出SVG": "Export as SVG", "关联tc文件": "Link tc file", "关联节点": "Link bus", "同站节点": "Station Bus", "源侧元件库": "Generator library", "新建变电站": "New trafo", "新建火电机组": "New fired generator", "新建风电场": "New wind farm", "新建水电厂": "New hydropower", "新建光伏电站": "New solar farm", "新建储能电站": "New storage", "新建核电机组": "New nuclear", "新建电力流": "New feedin", "新建通道": "New interconnection", "删除通道": "Delete interconnection", "删除场站": "Delete station", "导出tg": "Export tg", "数据文件": "Data file", "结果列表": "Result list", "gis底图": "GIS basemap", "上传自定义地图": "Upload custom map", "文件备注": "File remark", "通道信息": "Interconnection info", "场站信息": "Station info", "通道内必须至少包含一条线路": "At least one line in a interconnection", "场站内必须至少包含一个节点": "At least one bus in a station", "添加设备": "Add", "删除设备": "Delete", "添加行": "Add line", "包含变压器": "Transformer", "包含节点": "Bus", "包含线路": "Lines", "颜色": "Color", "图形": "Graph", "所属断面": "Interface", "首端": "From", "末端": "To", "首端节点": "From Bus", "线路列表": "Line list", "线路属性": "Line properties", "高压节点": "HV Bus", "低压节点": "LV Bus", "高压侧额定电压": "vn_hv_kv", "低压侧额定电压": "vn_lv_kv", "短路电压百分比": "vk_percent", "额定容量": "rated_capacity", "投运时段": "Commissioned", "电压等级": "vn_kv", "末端节点": "To Bus", "线路长度": "Length", "单位电阻": "r_ohm_per_km", "最大载流上限": "max_i_ka", "单位电抗": "x_ohm_per_km", "暂无": "None", "检测到gis相关数据缺失": "Missing gis data", "缺失": "missing", "关闭": "Close", "当前电压层级": "Voltage Level", "更换tg": "change tg", "显示无功": "Show Q", "单相短路": "short_circuit_1", "三相短路": "short_circuit_3", "电压上限": "Voltage upper Limit", "电压下限": "Voltage lower Limit", "是否单相短路": "Is short_circuit_1", "是否三相短路": "Is short_circuit_3", "扫描范围": "<PERSON><PERSON>", "线路": "Line", "主变": "Transformer", "N-1开断": "N-1", "N-2开断": "N-2", "负载率": "Load rate", "故障原因": "Cause", "故障类型": "Type", "请输入文件名称": "Filename", "请选择gis底图": "Choose GIS basemap", "一端": "Bus 1", "请选择一端": "Choose Bus 1", "另一端": "Bus 2", "请选择另一端": "Choose Bus 2", "场站类型": "Type", "请选择场站类型": "Choose Type", "场站类图形颜色型": "Station color", "通道名称": "Interconnection name", "场站名称": "Station name", "请输入通道名称": "Interconnection name", "请输入场站名称": "Station name", "确定删除线路吗？": "Delete line?", "注：此操作将删除算例文件中的对应线路": "Note this would delete the line in the case file", "确定删除节点吗？": "Delete bus?", "注：此操作将删除算例文件中的对应节点和其包含设备": "Note this would delete the bus and its devices in the case file", "确定删除设备吗？": "Delete component?", "注：此操作将删除算例文件中的对应设备": "Note this would delete the component in the case file", "间隔": "Step", "请选择tc或bpa文件进行该操作": "Choose tc first", "请在参数管理页面执行此功能！": "Do this in Fill Properties section!", "时序设置": "Time range settings", "BPA管理": "BPA config", "基于BPA": "BPA-based", "基于tc": "tc-based", "数据替换": "replace", "数据倍乘": "multiply", "请选择数据": "Choose data", "请选择要计算的数据": "<PERSON><PERSON>", "请选择要替换的数据": "<PERSON><PERSON>", "类型转换成功！": "Success!", "不允许关联时序曲线": "Not allowed to link time series", "参数补充成功！": "Success!", "序号": "No.", "引用": "Ref", "时序详情": "Details", "保存修改": "Save", "tg文件": "tg file", "搜索名称": "Search", "节点": "Bus", "机组": "Gen", "负荷": "Load", "有功功率": "Active power", "无功功率": "Reactive power", "相位角": "Phase angle", "电压": "Voltage", "电流": "Current", "线路负载率": "Line load", "基电压": "Base Voltage", "母线名": "Bus", "最大负载率": "<PERSON>", "越限次数": "Overload Count", "详情": "Details", "导出": "Export", "高压侧电流": "hv_current", "低压侧电流": "lv_current", "高压侧有功功率": "hv_active_power", "低压侧有功功率": "lv_active_power", "高压侧无功功率": "hv_reactive_power", "低压侧无功功率": "lv_reactive_power", "高压侧电压": "hv_kv", "低压侧电压": "hv_kv", "（可选）": "(Select)", "（已选）": "Selected", "是否考虑分区平衡": "Consider Zonal Balance", "未来年份总用电量": "Annual Consumption", "输入名称或备注": "Name or remarks", "请至少提供两年的历史用电量数据和一年的时序负荷曲线": "At least 2y of historical and 1y of load curve.", "算例时间范围": "Case time range", "请选择预测区域！": "Region must be selected!", "请选择预测年份！": "Year must be selected!", "请选择历史参考年份！": "Reference year must be selected!", "请选择省/直辖市": "Choose region", "请完整填写经纬度坐标": "Coordinates must be filled", "请选择预测年份": "Choose forecast year", "请确认操作！": "Comfirm!", "直接删除": "Delete directly", "解除关联": "Disable link", "直接开始仿真": "Start without saving", "保存并开始仿真": "Save and start", "包含设备": "Components", "注": "Note", "率": "rate", "注：其它数据请前往编辑器或生成器完善": "Note: Go to editor to fill others", "设备类型": "Type", "新建设备": "New", "编辑设备": "Modify", "请选择线路型号": "Choose line model", "添加型号": "Add model", "变压器": "Transformer", "线路组成": "Line Components", "线路型号": "Line model", "请选择线路型号或者输入线路长度": "Choose line model or input line length", "BPA扫描": "BPA Scan", "换流站": "Transformer", "开关站": "Switch", "煤电": "coal", "气电": "gas", "次": "times", "过载次数": "Overload count", "负荷有功功率": "Load active power", "负荷无功功率": "Load reactive power", "机组有功功率": "Gen active power", "机组无功功率": "Gen reactive power", "区外来电": "<PERSON><PERSON><PERSON>", "以上": "above", "计算结果": "Result", "拓扑": "Topology", "计算文件": "File", "日期选择": "Date", "选择电压上限": "Select voltage upper limit", "选择电压下限": "Select voltage lower limit", "N-1故障": "N-1 Fault", "N-2故障": "N-2 Fault", "母线短路": "Bus short circuit", "直流闭锁": "DC blockage", "交流潮流": "AC OPF", "短路故障": "Short circuit fault", "请选择转换类型": "Choose type", "可同时检修机组的最大容量": "Max capacity in maintenance", "分区检修计划生成中，请耐心等待。": "Generating", "接口请求中": "Calculating...", "枯水年": "Low inflow scenario", "设定的机组最大同时检修容量或数量总计值小于各类型机组分项设定值之和！": "The number of units to be maintained cannot be less than the sum of the values set for each type of unit!", "请至少选择一个分区": "Choose at least one zone", "已勾选的类别，数值不能为空": "Cannot leave the value blank", "一体化电站暂不支持此功能！": "Not supported for system-friendly stations!", "复制成功！": "<PERSON>pied", "已粘贴至表末端！": "Append to the end", "剪切成功！": "Cut", "请在算例数据页面操作！": "Operate in the editor!", "请先项目导入": "Import first", "（空）": "(blank)", "请选择一条数据！": "Select one row first", "搜索设备": "Search", "搜索曲线": "Search", "存储电量下限": "SOC lower limit", "存储电量上限": "SOC upper limit", "保供模式月度电力平衡表": "SS Monthly Power Balance", "保供模式月度电量平衡表": "SS Monthly el Balance", "促消纳模式月度电力平衡表": "ACC Monthly Power Balance", "促消纳模式月度电量平衡表": "ACC Monthly el Balance", "自定义盈亏控制时刻": "Customized Control Snap", "场站": "Station", "确定删除该通道吗？": "Delete this interconnection?", "确定删除该场站吗？": "Delete this station?", "注：此操作将删除算例文件中的对应通道和其包含线路": "Note this will delete its lines in case", "注：此操作将删除算例文件中的对应场站和其包含节点及设备": "Note this will delete its nodes and devices in case", "集成树的数量": "Tree number", "状态": "Status", "控制决策树的最大深度": "<PERSON>", "控制当前节点分裂所需的最小样本数": "Min sample size at node", "任何深度的分割点只有在左右分支中至少留下所填数量的训练样本时才会被考虑": "Cut points at depth considered", "并行数": "<PERSON><PERSON><PERSON>", "并行线程的数量，默认1代表不并行": "Number of parallel threads", "当设置为True,重新使用之前的结构去拟合样例并且加入更多的估计器(随机树)到集成树中": "Retrain", "请填写完整参数": "Fill all parameters", "净负荷优化": "Use netload", "考虑线路传输功率": "Consider line power", "若考虑该项，请确保区域间交直流线路参数填写正确": "Make sure branch properties are filled", "按最小备用容量计算": "Based on min reserve capacity", "勾选表示备用容量不足时仍然安排检修": "Schedule maint even if reserve insuff", "自定义配置": "Customize", "修改备注成功": "Success", "停止中": "Stopping", "失败": "Failed", "等待": "Waiting", "算例停止成功": "Stopped", "计算中及停止中的算例无法删除，请等待状态切换后重试": "Wait for status switch", "计算中等待中及停止中的算例无法计算，请等待状态切换后重试": "Wait for status switch", "未开始及停止中的算例无法停止，请等待状态切换后重试": "Wait for status switch", "确认删除选中算例?": "Confirm to delete", "删除算例成功": "Deleted", "删除算例失败": "Delete failed", "算例开始成功": "Simulation started", "算例开始失败": "Simulation initiation failed", "算例停止失败": "Simulation termination failed", "WebSocket发生错误：": "WebSocket failure", "未开始": "Not started", "全系统": "All", "已添加到计算任务": "Added to tasklist", "请选择日期": "Date", "计算通知": "Notification", "案例": "Case", "已计算完成": "Completed", "软证书定期校验失败，请确保您所处的网络环境可以连接证书申请时使用的服务器后重试。": "License verification failed.", "数据加载中": "Loading", "规划": "Plan", "成本类型": "Type", "全省": "Region", "曲线汇总": "<PERSON><PERSON><PERSON>", "促消纳模式": "ACC", "保供模式": "SS", "颗粒度选择": "Granularity", "请选择场景": "Choose a scenario", "规划交流线路": "Planned AC line", "规划直流线路": "Planned DC line", "规划储能最大放电功率": "Planned storage discharge", "规划储能容量": "Planned storage capacity", "亿元": "Billion USD", "万元": "Million USD", "元": "USD", "装机容量占比": "Capacity mix", "发电量占比": "Generation mix", "实际投运容量": "Planned capacity", "最大可投运容量": "Capacity in plan pool", "投运时间曲线": "Commissioning curve", "确定": "Confirm", "警告": "Warning", "重新校验软证书": "License authorization retry", "重试": "Retry", "申请新的软证书": "Apply for new license", "激活软证书": "Activate license", "日志文件": "Log file", "风光": "Wind and solar", "第二产业负荷": "Secondary industry load", "第三产业+居民用电负荷": "Tertiary + residential load", "年每月用电量": "Monthly consumption", "节假日类型": "Holiday type", "星期几": "DayofWeek", "是否节假日": "IsHoliday", "平均气温": "AVG T", "月份": "Month", "第二产业": "Secondary industry", "第三产业+居民用电": "Tertiary + residential load", "第": "", "下载BPA": "Download BPA", "机组启停状态": "Online/Offline", "功率(万千瓦)": "Power", "慢速精确": "Accurate and slow", "该结果不支持此功能": "Not supported", "请点击浏览器弹窗中的": "Please click the pop-up window", "打开tscan": "Open tscan", "若未出现弹窗，请点击": "If no pop-up window appears, click", "启动任务": "Start", "启动BPA扫描": "Start BPA scan", "提醒：启动任务之前，请先检查本地是否已安装BPA扫描客户端": "Note: check BPA scan is installed first", "提醒：启动BPA扫描之前，请先检查本地是否已安装BPA扫描客户端": "Note: check BPA scan is installed first", "暂未安装": "Not installed", "关闭弹窗": "Close window", "请输入授权服务器地址": "Enter the authorization server address", "电力电量平衡": "Power Balance", "请选择查询条件": "Query conditions", "目标值": "Value", "请输入密码": "Password", "设置成功": "Success", "设置失败": "Failed", "删除成功": "Deleted", "删除失败": "Not deleted", "密码错误": "Wrong password", "输入原始密码": "Current password", "设置新密码": "New password", "再次输入密码": "New password again", "请保持再次输入密码与新密码一致": "Same password", "请填写完整信息": "Fill all", "密码修改成功": "Changed!", "再次输入": "Again", "文件上传中": "Uploading", "请输入版本名称": "Version name", "请选择前述版本": "Previous version", "新增成功": "Added", "上传成功": "Uploaded", "无前述版本": "No previous version", "版本名称": "Version name", "前述版本": "Previous version", "更新时间": "Updated time", "请确认是否要将": "Confirm that", "设为标准版本": "Set as standard version", "请确认是否要取消": "Cancel?", "的标准版本状态": "standard version", "算例拆分成功": "Splitted", "算例拆分失败": "Split failed", "请确认当前操作": "Confirm", "建设成本": "Investment cost", "运行成本": "Operational cost", "惩罚": "Penalties", "必须选择相同单位的曲线": "Must select curves with the same units", "汇总曲线": "Summary", "创建副本": "New copy", "覆盖": "Overwrite", "跳过": "<PERSON><PERSON>", "下载更新": "Download update", "忽略此版本": "Skip this version", "稍后更新": "Update later", "安装更新": "Install update", "稍后安装": "Update later", "电压等级上限限不能小于下限": "Voltage upper limit > lower limit", "电压等级下限不能高于上限": "Voltage upper limit > lower limit", "该地理json文件已存在": "Geojson file already exists", "请上传正确的地理json文件": "Upload geojson files", "暂无可预览的曲线，请先关联": "No preview, please make links first", "关联成功": "Linked", "保存失败": "Save failed", "保存成功": "Saved", "请输入线路型号": "Line model", "型号": "Model", "导流截面": "Interface", "额定电流": "Rated current", "软件更新": "Software update", "检测到新版本": "New update", "当前版本": "Current version", "已是最新版本": "Already the latest version", "自动更新检测失败、请检查网络连接": "Update failed, check network connection", "安装包已过期即将重新下载更新": "Installer expired, re-downloading update", "请输入数字": "Numerics", "线型": "Line style", "工作位置图自定义配置": "Customize balance chart", "请选择水电场景": "Hydro Scenario", "请输入授权码": "Token", "变步长时序聚合法": "Step-variant ts agg", "选择默认配置": "Choose defaults", "孤岛检测": "Detect islands", "BPA文件上传中": "Uploading", "所有日志开关均被关闭": "All log switches are off", "解析中": "Parsing", "BPA文件解析中": "Parsing", "编辑": "Edit", "下载": "Download", "确认删除选中文件": "Delete?", "是否保存对": "Save changes made to", "的更改": "", "请求中": "Loading", "拓扑绘制": "Draw topologies", "拓扑绘制中": "Drawing", "注意": "Note", "平水年": "Average inflow scenario", "一体化电站暂不支持此功能": "Not supported for system-friendly stations", "已粘贴至表末端": "Appended to the end", "剪切成功": "Cut", "除数不可以为零": "Dividant cannot be zero", "删除“节点”将一并删除如下关联设备，请问是否仍要删除'": "Deleting this bus would also delete the following components. Delete?", "请输入替换后的内容": "Replace with", "时序新增成功": "ts added", "时序新增失败": "ts addition failed", "恢复默认成功": "Reset to defaults", "已关联其他设备，请确认操作": "Linked to other components. Confirm?", "全部修改": "Change all", "更新成功": "Updated", "更新失败": "Update failed", "负荷持续时间曲线分段法": "Load duration curve reduction", "半年": "halfyear", "秒": "s", "年": "1 year", "请求出错，请重试": "Failed, try again", "火电,煤电,煤,火": "???", "燃气,气,然气,9E,9F,6E,6F": "???", "水电,梯级,坝,一级,二级,三级,四级": "???", "太阳,光伏,光": "???", "风电,风": "???", "核电,核": "???", "抽蓄,抽,蓄": "???", "储能,储": "???", "实线图": "Solid line", "虚线图": "Dotted line", "点线图": "Dashdot line", "设备名称": "Name", "设备引用列表": "Refs", "预览": "Preview", "规划装机": "Planned Capacity", "线路规划容量": "Planned Line Capacity", "滚动模拟设置": "Rolling simulation settings", "型号管理": "Manage models", "删除“节点”将一并删除如下关联设备，请问是否仍要删除": "This would delete the following linked components, confirm?", "时序新增": "New", "时间段数量": "ranges", "电力盈亏平衡表": "Power balance", "自定义盈亏时段电力平衡表": "Customize power balance", "上传": "Upload", "选择": "Select", "数据管理": "Data Management", "完成": "Completed", "成本分析": "Cost analysis", "选择模式": "Select mode", "请选择模式": "Select mode", "预测曲线生成": "Generate forecast curves", "规划机组利用小时": "Planned units' full load hours", "图形颜色": "Color", "确定删除变压器吗？": "Delete transformer?", "注：此操作将删除算例文件中的对应变压器": "Note this would delete the transformer in the case", "短路计算": "Short circuit calculation", "年度": "Yearly", "近似分段数量": "c.a. segments", "添加": "Add", "设备列表": "List", "关联设备": "Refs", "可拖拽调整位置）": "Drag to adjust position", "程序目录设置": "Directory settings", "结果文件保存路径": "Result directory", "取消修改": "Cancel", "确认修改": "Confirm", "时序": "ts", "水库": "reservoir", "(空白)": "(blank)", "已选": "Selected", "导入": "Import", "风光生成": "Wind/solar profiles", "不保存": "Don't Save", "设备类别不一致，是否强制粘贴": "Type mismatch, force paste?", "导入成功": "Success", "送出规划": "Outflow planning", "省间联络": "Interconnections", "检查更新": "Check for updates", "复制成功": "<PERSON>pied", "全部选中": "Select all", "全部取消选中": "Select none", "另存为": "Save as", "盈亏时段电力平衡表": "Control time power balance", "确定删除吗？": "Delete?", "删除后无法恢复": "Not reversible", "请输入第一产业用电量": "Priminary industry consumption", "请输入第二产业用电量": "Secondary industry consumption", "请输入第三产业用电量": "Tertiary industry consumption", "请输入城乡居民生活用电量": "Domestic consumption", "时序负荷曲线模板": "load curve template", "功率曲线": "Power curves", "正在计算中，确认关闭程序？": "Simulation in process, close program?", "格式错误,请输入英文逗号分隔的数值！示例：1,2,3": "Correct format: comma-separated numerics. e.g.: 1,2,3"}