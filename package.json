{"name": "prsas", "private": true, "main": "electron/main.js", "version": "1.1.0", "date": "March 27, 2024", "type": "commonjs", "electron_ENV": "product", "productName": "PRSAS", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "wait-on tcp:3000 && electron .", "electron:serve": "concurrently -k \"npm run dev\" \"npm run electron\"", "electron:dist": "electron .", "build:electron": "vite build && electron-builder", "electron:build": "electron-builder", "lint:create": "eslint --init", "lint": "eslint --ext .js,.vue --ignore-path .eslint<PERSON>ore --fix src"}, "build": {"appId": "362C92C2-3C55-43D1-8640-2E7F58B6B78A", "productName": "PRSAS", "copyright": "Copyright © 2020-2023 图德科技", "publish": [{"provider": "generic", "url": ""}], "mac": {"category": "public.app-category.utilities", "target": "dmg", "icon": "public/software.ico"}, "win": {"target": "dir", "icon": "public/software.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}, "extraResources": [{"from": "./teap", "to": "../teap"}, {"from": "tc.ico", "to": "../"}, {"from": "tr.ico", "to": "../"}, {"from": "./app-update.yml", "to": "./"}], "files": ["dist/**/*", "electron/**/*"], "directories": {"buildResources": "public", "output": "dist_electron"}}, "dependencies": {"ag-grid-community": "~31.0.1", "ag-grid-enterprise": "~31.0.1", "ag-grid-vue3": "^31.0.2", "ant-design-vue": "^4.0.6", "axios": "^1.3.4", "echarts": "^5.4.3", "electron-log": "^5.0.1", "electron-updater": "^6.1.7", "file-saver": "^2.0.5", "iconv-lite": "^0.6.3", "jspreadsheet-ce": "^4.11.4", "mitt": "^3.0.0", "pinia": "^2.0.33", "sortablejs": "^1.15.6", "vue": "^3.2.47", "vue-i18n": "^9.14.4", "vue-router": "^4.1.6", "vue-virtual-scroller": "next", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "@vitejs/plugin-vue": "^4.1.0", "concurrently": "^8.2.2", "electron": "^28.1.3", "electron-builder": "^24.9.1", "eslint": "^8.37.0", "eslint-plugin-vue": "^9.10.0", "sass": "^1.59.3", "unplugin-vue-components": "^0.25.2", "vite": "^4.2.0", "vite-plugin-eslint": "^1.8.1", "wait-on": "^7.2.0"}}